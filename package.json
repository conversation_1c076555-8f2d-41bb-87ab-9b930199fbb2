{"name": "@toolproof/core", "private": true, "packageManager": "pnpm@10.15.0", "scripts": {"build": "pnpm -r run build", "test": "pnpm -r run test", "lint": "pnpm -r run lint", "clean": "pnpm -r run clean"}, "devDependencies": {"clear": "^0.1.0", "rimraf": "^6.0.1"}, "dependencies": {"gsap": "^3.14.2"}, "pnpm": {"overrides": {"@langchain/langgraph": "^0.3.12", "@langchain/core": "^0.3.79", "@langchain/langgraph-checkpoint": "0.0.18"}}}