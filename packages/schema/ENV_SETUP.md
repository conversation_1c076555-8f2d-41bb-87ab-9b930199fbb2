# Environment Variable Configuration

## Overview

The schema generation scripts now use environment variables instead of hardcoded file paths. This provides flexibility for different execution contexts (development, CI/CD, production) and protects against file structure changes.

## Required Environment Variables

All environment variables must be set before running the schema generation scripts. No defaults are provided to ensure explicit configuration.

| Variable | Description | Example |
|----------|-------------|---------|
| `TP_SCHEMA_ROOT` | Root directory of the schema package | `C:\Users\<USER>\toolproof\core\packages\schema` |
| `TP_SCHEMA_SOURCE_DIR` | Directory containing source Genesis.json | `src/genesis` |
| `TP_SCHEMA_SOURCE_FILE` | Name of the source schema file | `Genesis.json` |
| `TP_SCHEMA_OUTPUT_DIR` | Directory for generated schemas | `src/genesis/generated/schemas` |
| `TP_SCHEMA_TYPES_SRC_DIR` | Directory for generated TypeScript types (source) | `src/_lib/types` |
| `TP_SCHEMA_TYPES_DIST_DIR` | Directory for generated TypeScript types (dist) | `dist/_lib/types` |
| `TP_SCHEMA_BASE_URL` | Base URL for schema identifiers | `https://schemas.toolproof.com` |
| `TP_SCHEMA_VERSION` | Schema version identifier | `v0` |

## Setup

1. Copy `.env.example` to `.env`:
   ```powershell
   Copy-Item .env.example .env
   ```

2. Edit `.env` and fill in all required values:
   ```bash
   TP_SCHEMA_ROOT=C:\Users\<USER>\toolproof\core\packages\schema
   TP_SCHEMA_SOURCE_DIR=src/genesis
   TP_SCHEMA_SOURCE_FILE=Genesis.json
   TP_SCHEMA_OUTPUT_DIR=src/genesis/generated/schemas
   TP_SCHEMA_TYPES_SRC_DIR=src/_lib/types
   TP_SCHEMA_TYPES_DIST_DIR=dist/_lib/types
   TP_SCHEMA_BASE_URL=https://schemas.toolproof.com
   TP_SCHEMA_VERSION=v0
   ```

3. Load environment variables before running scripts:
   ```powershell
   # PowerShell
   Get-Content .env | ForEach-Object {
       if ($_ -match '^([^=]+)=(.*)$') {
           [Environment]::SetEnvironmentVariable($matches[1], $matches[2], 'Process')
       }
   }
   ```

## Usage

### Running Individual Scripts

All scripts now read from environment variables:

```powershell
# Extract schemas
pnpm run extractSchemas

# Extract subschema (still requires --name argument)
pnpm run extractSubschema -- --name Job

# Generate TypeScript types
pnpm run generateTypes

# Generate resource data types (still requires --name argument)
pnpm run generateResource -- --name Job
```

### Running Full Update

The `update` script runs all generation steps in sequence:

```powershell
pnpm run update
```

This will:
1. Clean `dist/` directory
2. Build scripts
3. Extract Genesis schema
4. Extract Job subschema
5. Generate TypeScript types
6. Generate Resource types
7. Build the package

## Migration from Hardcoded Paths

### Before (hardcoded)
```typescript
const projectRoot = process.cwd();
const inputDir = path.join(projectRoot, 'src', 'schemas');
```

### After (environment-driven)
```typescript
import { getConfig } from './_lib/config.js';

const config = getConfig();
const inputDir = config.getOutputDir();
```

## File Structure Changes

Generated schemas are now stored in:
- **Old location**: `src/schemas/` (did not exist, was referenced incorrectly)
- **New location**: `src/genesis/generated/schemas/` (actual current location, now properly configured)

## Error Handling

If any required environment variable is missing, scripts will fail immediately with a clear error message:

```
Error: Required environment variable TP_SCHEMA_ROOT is not set.
Please set it before running schema generation scripts.
```

## CI/CD Integration

Set environment variables in your CI/CD pipeline:

```yaml
# GitHub Actions example
env:
  TP_SCHEMA_ROOT: ${{ github.workspace }}/core/packages/schema
   TP_SCHEMA_SOURCE_DIR: src/genesis
  TP_SCHEMA_SOURCE_FILE: Genesis.json
  TP_SCHEMA_OUTPUT_DIR: src/genesis/generated/schemas
  TP_SCHEMA_TYPES_SRC_DIR: src/_lib/types
  TP_SCHEMA_TYPES_DIST_DIR: dist/_lib/types
  TP_SCHEMA_BASE_URL: https://schemas.toolproof.com
  TP_SCHEMA_VERSION: v0
```

## Troubleshooting

### "Required environment variable not set"
Ensure all variables in `.env.example` are set in your environment before running scripts.

### "Schema file not found"
Verify that `TP_SCHEMA_ROOT` points to the correct package directory and that relative paths in other variables are correct.

### Schema URL resolution issues
Check that `TP_SCHEMA_BASE_URL` and `TP_SCHEMA_VERSION` match the `$id` fields in your schema files.
