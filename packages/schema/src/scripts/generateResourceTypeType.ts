import fs from 'fs';
import path from 'path';
import { getConfig } from './_lib/config.js';

/**
 * Generate a typed Resource variant where `extractedData` is typed to a specific schema
 * extracted under the configured output directory.
 *
 * Usage: node ./dist/scripts/generateResourceTypeType.js --name Job
 */
async function main() {
    const config = getConfig();
    const { name } = parseArgs(process.argv.slice(2));
    if (!name) {
        console.error('Missing --name <SchemaBasename> argument');
        process.exit(1);
    }

    const inPath = config.getOutputPath(`${name}.json`);

    if (!fs.existsSync(inPath)) {
        console.error(`Schema file not found: ${inPath}`);
        process.exit(1);
    }

    // Basic validation against the expected shape of ExtractionSchema.
    const raw = fs.readFileSync(inPath, 'utf8');
    let parsed: any = null;
    try {
        parsed = JSON.parse(raw);
    } catch (e) {
        console.error(`Failed to parse JSON schema ${inPath}:`, e);
        process.exit(1);
    }

    // Minimal checks that roughly match the ExtractionSchema constraints used elsewhere.
    if (parsed.$schema && parsed.$schema !== 'https://json-schema.org/draft/2020-12/schema') {
        console.warn(`Warning: schema $schema is '${parsed.$schema}', expected draft 2020-12. Proceeding anyway.`);
    }
    if (parsed.type && parsed.type !== 'object') {
        console.warn(`Warning: ExtractionSchema usually has type: 'object' but this schema has type: '${parsed.type}'. Proceeding.`);
    }

    // Ensure output folder exists
    const outDir = config.getTypesSrcDir();
    fs.mkdirSync(outDir, { recursive: true });

    const outName = `Resource_${name}.d.ts`;
    const outPath = config.getTypesSrcPath(outName);
    const outJsName = `Resource_${name}.js`;
    const outJsPath = config.getTypesSrcPath(outJsName);

    // IMPORTANT:
    // We intentionally do NOT re-run json-schema-to-typescript here.
    // That path produces self-contained types that degrade schema-patterned identities/refs
    // into plain `string`, which then becomes incompatible with our canonical branded
    // identity types in `types.d.ts` (e.g. `ResourceIdentity = `RESOURCE-${string}``).
    //
    // Instead, we generate a tiny module that composes the canonical types:
    //   Resource_<Name> = ResourceMetaBase & { extractedData: <Name> }
    const header = '// Auto-generated strict composite type. Do not edit.\n';
    const ts =
        `import type { ResourceMetaBase, ${name} as ExtractedData } from './types.js';\n` +
        `export type Resource_${name} = ResourceMetaBase & { extractedData: ExtractedData };\n`;

    fs.writeFileSync(outPath, header + ts, 'utf8');
    console.log(`Wrote ${outPath}`);

    // Ensure a runtime-resolvable JS shim exists alongside the .d.ts for NodeNext resolution
    if (!fs.existsSync(outJsPath)) {
        fs.writeFileSync(outJsPath, 'export {}\n', 'utf8');
        console.log(`Wrote ${outJsPath}`);
    }

    // Also copy both files into dist so consumers can resolve the module and its types
    const distLibDir = config.getTypesDistDir();
    fs.mkdirSync(distLibDir, { recursive: true });
    const distDtsPath = config.getTypesDistPath(outName);
    const distJsPath = config.getTypesDistPath(outJsName);
    fs.writeFileSync(distDtsPath, header + ts, 'utf8');
    fs.writeFileSync(distJsPath, 'export {}\n', 'utf8');
    console.log(`Wrote ${distDtsPath}`);
    console.log(`Wrote ${distJsPath}`);
}

function parseArgs(args: string[]): { name?: string } {
    let name: string | undefined;
    for (let i = 0; i < args.length; i++) {
        const a = args[i];
        if (a === '--name') {
            name = args[i + 1];
            i++;
        } else if (a.startsWith('--name=')) {
            name = a.split('=')[1];
        }
    }
    return { name };
}

main().catch((e) => {
    console.error(e);
    process.exit(1);
});
