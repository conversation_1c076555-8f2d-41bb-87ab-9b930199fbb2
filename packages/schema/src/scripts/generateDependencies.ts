import fs from "fs";
import path from "path";
import { getConfig } from "./_lib/config.js";

type JSONValue = null | boolean | number | string | JSONValue[] | { [k: string]: JSONValue };

function decodeJsonPointerSegment(segment: string): string {
  // JSON Pointer decoding: ~1 => / and ~0 => ~
  return segment.replace(/~1/g, "/").replace(/~0/g, "~");
}

function collectRefs(node: unknown, out: Set<string>): void {
  if (Array.isArray(node)) {
    for (const item of node) collectRefs(item, out);
    return;
  }
  if (!node || typeof node !== "object") return;

  const obj = node as Record<string, unknown>;
  const ref = obj["$ref"];
  if (typeof ref === "string") out.add(ref);

  for (const value of Object.values(obj)) {
    collectRefs(value, out);
  }
}

function resolveInternalRefToDefKey(ref: string, defKeys: Set<string>, anchorToDef: Record<string, string>): string | null {
  if (!ref.startsWith("#")) return null;

  // JSON Pointer: #/$defs/<Name>(/...)
  const defsPrefix = "#/$defs/";
  if (ref.startsWith(defsPrefix)) {
    const rest = ref.slice(defsPrefix.length);
    const firstSegment = rest.split("/")[0] ?? "";
    const defKey = decodeJsonPointerSegment(firstSegment);
    return defKeys.has(defKey) ? defKey : null;
  }

  // Anchor ref: #AnchorName
  if (!ref.startsWith("#/")) {
    const anchor = ref.slice(1);
    const mapped = anchorToDef[anchor];
    if (mapped && defKeys.has(mapped)) return mapped;
    if (defKeys.has(anchor)) return anchor;
  }

  return null;
}

function main() {
  const config = getConfig();

  const inPath = config.getOutputPath("Genesis.json");
  const outPath = path.join(config.getRoot(), "src/genesis/generated/dependencies.json");

  if (!fs.existsSync(inPath)) {
    throw new Error(`Genesis schema not found at ${inPath}. Run extractSchemas first.`);
  }

  const raw = fs.readFileSync(inPath, "utf8");
  const doc = JSON.parse(raw) as any;

  const defs: Record<string, JSONValue> = doc?.$defs && typeof doc.$defs === "object" ? doc.$defs : {};
  const defKeys = new Set(Object.keys(defs));

  // Map anchors to $defs keys (useful if any anchor-style refs remain)
  const anchorToDef: Record<string, string> = {};
  for (const [defKey, defSchema] of Object.entries(defs)) {
    if (!defSchema || typeof defSchema !== "object" || Array.isArray(defSchema)) continue;
    const anchor = (defSchema as any).$anchor;
    if (typeof anchor === "string" && !(anchor in anchorToDef)) {
      anchorToDef[anchor] = defKey;
    }
  }

  const dependencyMap: Record<string, string[]> = {};

  for (const [defKey, defSchema] of Object.entries(defs)) {
    const refs = new Set<string>();
    collectRefs(defSchema, refs);

    const deps = new Set<string>();
    for (const ref of refs) {
      const depKey = resolveInternalRefToDefKey(ref, defKeys, anchorToDef);
      if (!depKey) continue;
      if (depKey === defKey) continue;
      deps.add(depKey);
    }

    dependencyMap[defKey] = Array.from(deps);
  }

  fs.mkdirSync(path.dirname(outPath), { recursive: true });
  fs.writeFileSync(outPath, JSON.stringify(dependencyMap, null, 4), "utf8");
  console.log(`Wrote dependency map to ${outPath}`);
}

main();
