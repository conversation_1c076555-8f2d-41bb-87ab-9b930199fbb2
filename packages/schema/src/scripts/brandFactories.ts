// Runtime factories for branded Identity string types.
//
// IMPORTANT:
// `pnpm run update` starts with `pnpm run build:scripts` (tsc over `src/scripts/**/*`) *before*
// running `generateTypes`. So this file must NOT depend on generated types existing yet.
//
// These are structural brands; `generateTypes` will later emit equivalent aliases for consumers.
type Brand<K extends string> = string & { readonly __brand: K };

export type ExecutionIdentity = Brand<'ExecutionIdentity'>;
export type ResourceIdentity = Brand<'ResourceIdentity'>;
export type WorkStepIdentity = Brand<'WorkStepIdentity'>;
export type BranchStepIdentity = Brand<'BranchStepIdentity'>;
export type ForStepIdentity = Brand<'ForStepIdentity'>;
export type WhileStepIdentity = Brand<'WhileStepIdentity'>;
export type ResourceFormatIdentity = Brand<'ResourceFormatIdentity'>;
export type ResourceTypeIdentity = Brand<'ResourceTypeIdentity'>;
export type ResourceRoleIdentity = Brand<'ResourceRoleIdentity'>;
export type StatelessStrategyIdentity = Brand<'StatelessStrategyIdentity'>;
export type StatefulStrategyIdentity = Brand<'StatefulStrategyIdentity'>;

// Optional runtime validation toggle. Off by default.
// Enable by setting TP_BRAND_VALIDATE=1 (or NODE_ENV=development) when running.
const shouldValidate: boolean =
  (typeof process !== "undefined" && !!process.env &&
    (process.env.TP_BRAND_VALIDATE === "1" || process.env.NODE_ENV === "development")) ||
  false;

function makeFactory<T extends string>(name: string, pattern?: RegExp) {
  return (s: string): T => {
    if (shouldValidate && pattern && !pattern.test(s)) {
      throw new TypeError(
        `Invalid ${name}: "${s}" does not match ${String(pattern)}`
      );
    }
    return s as T;
  };
}

/** Generic unsafe brand helper when a bespoke factory is not available. */
export function unsafeBrand<T extends string>(s: string): T {
  return s as T;
}

// Known patterns from schemas (when available)
const RE_TYPE_IDENTITY = /^TYPE-.+$/;
const RE_JOB_IDENTITY = /^JOB-.+$/;
const RE_ROLE_IDENTITY = /^ROLE-.+$/;

// Factories for each Identity-like type
export const asResourceTypeIdentity = makeFactory<ResourceTypeIdentity>("ResourceTypeIdentity", RE_TYPE_IDENTITY);
export const asResourceRoleIdentity = makeFactory<ResourceRoleIdentity>("ResourceRoleIdentity", RE_ROLE_IDENTITY);

// IDs without strict regex patterns in the current schemas: keep unchecked casts.
export const asExecutionIdentity = makeFactory<ExecutionIdentity>("ExecutionIdentity");
export const asResourceIdentity = makeFactory<ResourceIdentity>("ResourceIdentity");
export const asWorkStepIdentity = makeFactory<WorkStepIdentity>("WorkStepIdentity");
export const asBranchStepIdentity = makeFactory<BranchStepIdentity>("BranchStepIdentity");
export const asForStepIdentity = makeFactory<ForStepIdentity>("ForStepIdentity");
export const asResourceFormatIdentity = makeFactory<ResourceFormatIdentity>("ResourceFormatIdentity");
export const asWhileStepIdentity = makeFactory<WhileStepIdentity>("WhileStepIdentity");
export const asStatelessStrategyIdentity = makeFactory<StatelessStrategyIdentity>("StatelessStrategyIdentity");
export const asStatefulStrategyIdentity = makeFactory<StatefulStrategyIdentity>(
  "StatefulStrategyIdentity"
);

// Batch helpers
export const asResourceTypeIdentities = (xs: string[]): ResourceTypeIdentity[] => xs.map(asResourceTypeIdentity);
export const asResourceRoleIdentities = (xs: string[]): ResourceRoleIdentity[] => xs.map(asResourceRoleIdentity);
export const asExecutionIdentities = (xs: string[]): ExecutionIdentity[] =>
  xs.map(asExecutionIdentity);
export const asResourceIdentities = (xs: string[]): ResourceIdentity[] =>
  xs.map(asResourceIdentity);
export const asWorkStepIdentities = (xs: string[]): WorkStepIdentity[] =>
  xs.map(asWorkStepIdentity);
export const asBranchStepIdentities = (xs: string[]): BranchStepIdentity[] =>
  xs.map(asBranchStepIdentity);
export const asForStepIdentities = (xs: string[]): ForStepIdentity[] => xs.map(asForStepIdentity);
export const asResourceFormatIdentities = (xs: string[]): ResourceFormatIdentity[] => xs.map(asResourceFormatIdentity);
export const asWhileStepIdentities = (xs: string[]): WhileStepIdentity[] =>
  xs.map(asWhileStepIdentity);
export const asStatelessStrategyIdentities = (xs: string[]): StatelessStrategyIdentity[] =>
  xs.map(asStatelessStrategyIdentity);
export const asStatefulStrategyIdentities = (xs: string[]): StatefulStrategyIdentity[] =>
  xs.map(asStatefulStrategyIdentity);
