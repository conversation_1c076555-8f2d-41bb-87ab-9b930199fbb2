import fs from 'fs';
import path from 'path';
import { getConfig } from './_lib/config.js';

/**
 * Rewrite anchor-style references to JSON Pointer references in Genesis.json
 * 
 * Converts #AnchorName to #/$defs/AnchorName for compatibility with strict
 * JSON Schema validators and downstream tooling. This normalization step runs
 * before both schema extraction and resource envelope generation.
 * 
 * The rewritten file is saved under src/genesis/generated/resourceTypes/Genesis.json
 * for use by other scripts.
 * 
 * Usage: node ./dist/scripts/rewriteAnchors.js
 */

type JSONValue = null | boolean | number | string | JSONValue[] | { [k: string]: JSONValue };

/**
 * Rewrite anchor-style references to JSON Pointer references
 * Converts #AnchorName to #/$defs/AnchorName for compatibility with strict JSON Schema validators
 * 
 * This function works on a schema object that has $defs at its top level.
 * For Genesis, each $defs entry is a Type envelope with an extractionSchema that may have $anchor.
 */
function rewriteAnchorsToPointers(root: any): void {
    if (!root || typeof root !== "object") return;

    // Build a map of anchors to their definition names
    const defs: Record<string, any> = root.$defs && typeof root.$defs === "object" ? root.$defs : {};
    const anchorToDef: Record<string, string> = {};

    // For Genesis structure: each def is a Type envelope with extractionSchema.$anchor
    for (const [defName, defValue] of Object.entries(defs)) {
        if (!defValue || typeof defValue !== "object") continue;
        
        // Check if this is a Type envelope (has extractionSchema property)
        const extractionSchema = (defValue as any).extractionSchema;
        if (extractionSchema && typeof extractionSchema === "object") {
            // Look for $anchor inside the extractionSchema
            const anchor = extractionSchema.$anchor;
            if (typeof anchor === "string" && !anchorToDef[anchor]) {
                anchorToDef[anchor] = defName;
            }
        } else {
            // Fallback: check for $anchor at the def level (non-envelope case)
            const anchor = (defValue as any).$anchor;
            if (typeof anchor === "string" && !anchorToDef[anchor]) {
                anchorToDef[anchor] = defName;
            }
        }
    }

    // Walk the entire tree and rewrite anchor refs to pointer refs
    function walk(node: any): void {
        if (Array.isArray(node)) {
            for (const item of node) walk(item);
            return;
        }
        if (!node || typeof node !== "object") return;

        // Rewrite $ref if it's an anchor-style reference
        if (typeof node.$ref === "string") {
            const ref: string = node.$ref;
            // Match anchor refs: starts with # but not #/ (JSON Pointer syntax)
            if (ref.startsWith("#") && !ref.startsWith("#/")) {
                const anchor = ref.slice(1);
                const defName = anchorToDef[anchor];
                if (defName) {
                    node.$ref = `#/$defs/${defName}`;
                }
            }
        }

        // Recursively walk all properties
        for (const val of Object.values(node)) {
            walk(val);
        }
    }

    walk(root);
}

async function main() {
    const config = getConfig();
    const genesisSourcePath = config.getSourcePath();
    
    // Create a generated/normalized version (anchor refs rewritten to pointers)
    const normalizedPath = config.getNormalizedSourcePath();

    if (!fs.existsSync(genesisSourcePath)) {
        console.error(`Genesis source file not found at ${genesisSourcePath}`);
        process.exit(1);
    }

    const raw = fs.readFileSync(genesisSourcePath, 'utf-8');
    const genesis = JSON.parse(raw);

    // Validate structure
    if (!genesis.extractionSchema || !genesis.extractionSchema.$defs) {
        console.error('Genesis.json must have extractionSchema.$defs');
        process.exit(1);
    }

    // Rewrite anchors in the extractionSchema (where $defs is at the top level)
    rewriteAnchorsToPointers(genesis.extractionSchema);

    // Write normalized version
    fs.mkdirSync(path.dirname(normalizedPath), { recursive: true });
    fs.writeFileSync(normalizedPath, JSON.stringify(genesis, null, 4), 'utf-8');
    console.log(`Wrote normalized Genesis with pointer refs to ${normalizedPath}`);
}

main().catch((e) => {
    console.error(e);
    process.exit(1);
});
