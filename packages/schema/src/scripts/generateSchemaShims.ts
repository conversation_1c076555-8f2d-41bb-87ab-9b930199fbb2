import fs from 'fs';
import path from 'path';
import { getConfig } from './_lib/config.js';

/**
 * Generate TypeScript shim files for all JSON schemas and resources
 * 
 * Creates .ts files that import and re-export .json files using
 * the JSON import assertion syntax. This enables proper dependency resolution
 * and type inference when importing schemas in TypeScript.
 * 
 * Generates shims for:
 * - src/genesis/generated/schemas/*.json (schema files)
 * - src/genesis/generated/resources/*.json (resource envelope files)
 * 
 * Usage: node ./dist/scripts/generateSchemaShims.js
 */
async function main() {
    const config = getConfig();
    const schemasDir = config.getOutputDir();
    const resourcesDir = path.join(path.dirname(schemasDir), 'resources');
    const generatedResourceTypesDir = config.getNormalizedDir();

    // Process schemas directory
    let totalCount = 0;
    if (fs.existsSync(schemasDir)) {
        const files = fs.readdirSync(schemasDir);
        const jsonFiles = files.filter(f => f.endsWith('.json') && !f.startsWith('.'));

        for (const jsonFile of jsonFiles) {
            const baseName = path.basename(jsonFile, '.json');
            const tsFile = `${baseName}.ts`;
            const tsPath = path.join(schemasDir, tsFile);

            const content = `import schema from './${jsonFile}' with { type: 'json' };\nexport default schema;\n`;

            fs.writeFileSync(tsPath, content, 'utf-8');
            console.log(`Generated ${tsFile}`);
            totalCount++;
        }

        console.log(`Generated ${jsonFiles.length} TypeScript schema shims in ${schemasDir}`);
    } else {
        console.warn(`Schemas directory not found at ${schemasDir}`);
    }

    // Process resources directory
    if (fs.existsSync(resourcesDir)) {
        const files = fs.readdirSync(resourcesDir);
        const jsonFiles = files.filter(f => f.endsWith('.json') && !f.startsWith('.'));

        for (const jsonFile of jsonFiles) {
            const baseName = path.basename(jsonFile, '.json');
            const tsFile = `${baseName}.ts`;
            const tsPath = path.join(resourcesDir, tsFile);

            const content = `import resources from './${jsonFile}' with { type: 'json' };\nexport default resources;\n`;

            fs.writeFileSync(tsPath, content, 'utf-8');
            console.log(`Generated ${tsFile}`);
            totalCount++;
        }

        console.log(`Generated ${jsonFiles.length} TypeScript resource shims in ${resourcesDir}`);
    } else {
        console.warn(`Resources directory not found at ${resourcesDir}`);
    }

    // Genesis (normalized) shim
    // We treat the normalized Genesis.json under src/genesis/generated/resourceTypes as the
    // public import target, and generate a shim right next to it.
    try {
        const genesisJsonPath = config.getNormalizedSourcePath();
        if (fs.existsSync(genesisJsonPath)) {
            fs.mkdirSync(generatedResourceTypesDir, { recursive: true });
            const outPath = path.join(generatedResourceTypesDir, 'Genesis.ts');
            const content = `import schema from './Genesis.json' with { type: 'json' };\nexport default schema;\n`;
            fs.writeFileSync(outPath, content, 'utf-8');
            console.log(`Generated Genesis.ts in ${generatedResourceTypesDir}`);
            totalCount++;
        } else {
            console.warn(`Genesis source JSON not found at ${genesisJsonPath}; skipping Genesis.ts shim`);
        }
    } catch (e) {
        console.warn('Failed to generate Genesis.ts shim:', e);
    }

    console.log(`Generated ${totalCount} total TypeScript shims`);
}

main().catch((e) => {
    console.error(e);
    process.exit(1);
});
