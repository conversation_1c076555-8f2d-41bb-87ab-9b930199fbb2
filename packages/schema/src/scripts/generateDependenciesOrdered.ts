import fs from "fs";
import path from "path";
import { getConfig } from "./_lib/config.js";

type DependencyMap = Record<string, string[]>;

type Phase = "beforeResourceType" | "beforeResource" | "afterResource";

function readDependencyMap(filePath: string): DependencyMap {
  const raw = fs.readFileSync(filePath, "utf8");
  const parsed = JSON.parse(raw) as Record<string, unknown>;

  const out: DependencyMap = {};
  for (const [k, v] of Object.entries(parsed)) {
    if (!Array.isArray(v) || v.some((x) => typeof x !== "string")) {
      throw new Error(`Invalid dependency map: value for ${k} must be string[]`);
    }
    out[k] = v as string[];
  }
  return out;
}

function buildReverseGraph(deps: DependencyMap): Map<string, string[]> {
  const children = new Map<string, string[]>();
  for (const k of Object.keys(deps)) children.set(k, []);

  for (const [node, nodeDeps] of Object.entries(deps)) {
    for (const dep of nodeDeps) {
      if (!children.has(dep)) children.set(dep, []);
      children.get(dep)!.push(node);
    }
  }
  return children;
}

function computeAncestors(target: string, deps: DependencyMap): Set<string> {
  const visited = new Set<string>();
  const stack: string[] = [target];

  while (stack.length) {
    const cur = stack.pop()!;
    if (visited.has(cur)) continue;
    visited.add(cur);

    const direct = deps[cur] ?? [];
    for (const d of direct) {
      if (!visited.has(d)) stack.push(d);
    }
  }

  // If target isn't a known key, treat as empty set
  if (!(target in deps)) return new Set<string>();
  return visited;
}

function pickNext(
  available: string[],
  phase: Phase,
  neededForResourceType: Set<string>,
  neededForResource: Set<string>
): string {
  const sorted = available.slice().sort((a, b) => a.localeCompare(b));

  if (phase === "beforeResourceType") {
    const candidates = sorted.filter((k) => neededForResourceType.has(k));
    return (candidates[0] ?? sorted[0])!;
  }

  if (phase === "beforeResource") {
    const candidates = sorted.filter((k) => neededForResource.has(k));
    return (candidates[0] ?? sorted[0])!;
  }

  return sorted[0]!;
}

function topoOrderWithPrecedence(deps: DependencyMap): string[] {
  const keys = Object.keys(deps);
  const keySet = new Set(keys);

  // Validate that all dependencies are known keys (otherwise ordering is ill-defined)
  for (const [k, ds] of Object.entries(deps)) {
    for (const d of ds) {
      if (!keySet.has(d)) {
        throw new Error(`Unknown dependency key in map: ${k} -> ${d}`);
      }
      if (d === k) {
        throw new Error(`Self-dependency detected for ${k}`);
      }
    }
  }

  const children = buildReverseGraph(deps);

  const inDegree = new Map<string, number>();
  for (const k of keys) inDegree.set(k, 0);
  for (const [node, nodeDeps] of Object.entries(deps)) {
    inDegree.set(node, (inDegree.get(node) ?? 0) + nodeDeps.length);
  }

  const neededForResourceType = computeAncestors("ResourceType", deps);
  const neededForResource = computeAncestors("Resource", deps);

  const order: string[] = [];
  const available: string[] = [];

  for (const k of keys) {
    if ((inDegree.get(k) ?? 0) === 0) available.push(k);
  }

  let phase: Phase = "beforeResourceType";

  while (available.length) {
    const next = pickNext(available, phase, neededForResourceType, neededForResource);

    const idx = available.indexOf(next);
    available.splice(idx, 1);

    order.push(next);

    if (phase === "beforeResourceType" && next === "ResourceType") {
      phase = "beforeResource";
    } else if (phase === "beforeResource" && next === "Resource") {
      phase = "afterResource";
    }

    for (const child of children.get(next) ?? []) {
      const newDeg = (inDegree.get(child) ?? 0) - 1;
      inDegree.set(child, newDeg);
      if (newDeg === 0) available.push(child);
    }
  }

  if (order.length !== keys.length) {
    const remaining = keys.filter((k) => !order.includes(k));
    throw new Error(`Cycle detected or unsatisfied ordering. Remaining: ${remaining.join(", ")}`);
  }

  return order;
}

function main() {
  const config = getConfig();

  const inPath = path.join(config.getRoot(), "src/genesis/generated/dependencies.json");
  const outPath = path.join(config.getRoot(), "src/genesis/generated/dependencies_ordered.json");

  if (!fs.existsSync(inPath)) {
    throw new Error(`dependencies.json not found at ${inPath}. Run generateDependencies first.`);
  }

  const deps = readDependencyMap(inPath);
  const order = topoOrderWithPrecedence(deps);

  const ordered: DependencyMap = {};
  for (const k of order) {
    ordered[k] = deps[k];
  }

  fs.mkdirSync(path.dirname(outPath), { recursive: true });
  fs.writeFileSync(outPath, JSON.stringify(ordered, null, 4), "utf8");
  console.log(`Wrote ordered dependency map to ${outPath}`);
}

main();
