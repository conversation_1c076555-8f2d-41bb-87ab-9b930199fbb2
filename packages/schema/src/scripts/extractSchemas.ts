import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { getConfig } from "./_lib/config.js";

type JSONValue = null | boolean | number | string | JSONValue[] | { [k: string]: JSONValue };

interface ExtractOptions {
  inPath: string;
  outPath: string;
  topLevelId?: string;
}

function parseArgs(): ExtractOptions {
  const config = getConfig();
  const argv = process.argv.slice(2);
  let inPath = "";
  let outPath = "";
  let topLevelId: string | undefined;
  for (let i = 0; i < argv.length; i++) {
    const a = argv[i];
    if (a === "--in" && i + 1 < argv.length) inPath = argv[++i];
    else if (a === "--out" && i + 1 < argv.length) outPath = argv[++i];
    else if (a === "--id" && i + 1 < argv.length) {
      let v = argv[++i];
      // Strip accidental surrounding quotes from PowerShell/cmd
      if ((v.startsWith("'") && v.endsWith("'")) || (v.startsWith('"') && v.endsWith('"'))) {
        v = v.slice(1, -1);
      }
      topLevelId = v;
    }
  }
  
  // Use config defaults if not provided via CLI
  if (!inPath) {
    // Use generated/normalized version with anchor refs rewritten to pointers
    inPath = config.getNormalizedSourcePath();
  }
  if (!outPath) {
    outPath = config.getOutputPath(config.getSourceFile());
  }
  if (!topLevelId) {
    topLevelId = config.getSchemaId('Genesis');
  }
  
  // Resolve to absolute paths from project root
  const cwd = config.getRoot();
  const wasInRelative = !path.isAbsolute(inPath);
  const wasOutRelative = !path.isAbsolute(outPath);
  if (wasInRelative) inPath = path.join(cwd, inPath);
  if (wasOutRelative) outPath = path.join(cwd, outPath);
  // Fallback: resolve relative to script directory if not found
  const scriptDir = path.dirname(fileURLToPath(import.meta.url));
  if (!fs.existsSync(inPath) && wasInRelative) inPath = path.resolve(scriptDir, inPath);
  const outDir = path.dirname(outPath);
  if (!fs.existsSync(outDir) && wasOutRelative) {
    // Try making directory relative to script dir
    const altOut = path.resolve(scriptDir, outPath);
    const altOutDir = path.dirname(altOut);
    if (!fs.existsSync(path.dirname(outPath))) {
      // Prefer creating outDir at cwd location if possible; otherwise fallback below when writing
    } else {
      outPath = altOut;
    }
  }
  return { inPath, outPath, topLevelId };
}

// Heuristic: determine if a node is a Type envelope
function isTypeEnvelope(node: any): boolean {
  return (
    node && typeof node === "object" && !Array.isArray(node) &&
    // Treat any object that has an 'extractionSchema' AND 'identity' as a Type envelope
    // This prevents false positives where 'extractionSchema' is just a regular schema property
    node.extractionSchema && typeof node.extractionSchema === "object" &&
    node.identity && typeof node.identity === "string"
  );
}

// Merge $defs into target, without overwriting existing keys unless identical
function mergeDefs(target: Record<string, JSONValue>, source?: any, label?: string) {
  if (!source || typeof source !== "object") return;
  const src = (source as any)["$defs"];
  if (!src || typeof src !== "object") return;
  for (const [k, v] of Object.entries(src)) {
    if (!(k in target)) {
      target[k] = v as JSONValue;
    } else {
      // Best-effort: if duplicate key, require structural equality; otherwise, namespace
      const existing = JSON.stringify(target[k]);
      const incoming = JSON.stringify(v);
      if (existing !== incoming) {
        const altKey = `${k}__from_${(label || "defs").replace(/[^A-Za-z0-9_]+/g, "_")}`;
        if (!(altKey in target)) target[altKey] = v as JSONValue;
      }
    }
  }
}

// Deeply traverse an object replacing any Type envelope with its extractionSchema,
// and hoist its inner $defs to topDefs. Prevent infinite recursion with a visited set.
function unwrapTypes(node: JSONValue, topDefs: Record<string, JSONValue>, labelPath: string[] = [], visited = new Set<any>()): JSONValue {
  if (node && typeof node === "object") {
    if (visited.has(node)) return node; // avoid cycles
    visited.add(node);
  }

  if (isTypeEnvelope(node)) {
    const env = node as any;
    const inner = env.extractionSchema;
    // Hoist inner $defs before stripping
    mergeDefs(topDefs, inner, labelPath.join("_"));
    // Return the inner schema itself, after also unwrapping any nested envelopes it may contain
    const unwrappedInner = unwrapTypes(inner as JSONValue, topDefs, labelPath.concat([String(env.identity || "env")]), visited);
    return unwrappedInner;
  }

  if (Array.isArray(node)) {
    return node.map((v, i) => unwrapTypes(v, topDefs, labelPath.concat([String(i)]), visited)) as JSONValue;
  }

  if (node && typeof node === "object") {
    const out: Record<string, JSONValue> = {};
    for (const [k, v] of Object.entries(node)) {
      if (k === "$defs" && v && typeof v === "object" && !Array.isArray(v)) {
        // Process nested $defs: unwrap each entry value if it's a Type envelope
        const defsOut: Record<string, JSONValue> = {};
        for (const [dk, dv] of Object.entries(v as any)) {
          const unwrapped = unwrapTypes(dv as JSONValue, topDefs, labelPath.concat(["$defs", dk]), visited);
          defsOut[dk] = unwrapped;
        }
        out[k] = defsOut;
      } else {
        out[k] = unwrapTypes(v as JSONValue, topDefs, labelPath.concat([k]), visited);
      }
    }
    return out;
  }

  return node;
}

function main() {
  const { inPath, outPath, topLevelId } = parseArgs();
  const raw = fs.readFileSync(inPath, "utf8");
  const doc = JSON.parse(raw);

  if (!doc || typeof doc !== "object" || !doc.extractionSchema) {
    throw new Error("Input must be a Type JSON with an extractionSchema at the top level");
  }

  const topSchema = (doc as any).extractionSchema;
  const outDefs: Record<string, JSONValue> = {};

  // Seed with top-level $defs (if any) before unwrapping
  mergeDefs(outDefs, topSchema, "top");

  // Unwrap the entire top schema tree so that any nested Type envelopes become raw schemas
  const flattened = unwrapTypes(topSchema as JSONValue, outDefs, ["extractionSchema"]);

  // Assemble output: force $schema, optionally set $id, hoist collected $defs
  let base: any;
  if (flattened && typeof flattened === "object" && !Array.isArray(flattened)) {
    base = { ...(flattened as any) };
  } else {
    // If flattened is not an object (should be rare for a top-level schema), wrap it
    base = { const: flattened };
  }
  // Assemble, but avoid duplicating $id: if the flattened base already has $id, prefer it.
  const output: Record<string, JSONValue> = {
    $schema: "https://json-schema.org/draft/2020-12/schema",
    ...base,
  };
  if (topLevelId && !(output as any).$id) {
    (output as any).$id = topLevelId;
  }

  // Enforce presence of $id: schema must declare an absolute identity.
  if (!(output as any).$id) {
    throw new Error(
      "Flattened schema must define $id. Provide it via CLI --id or include $id in the source extractionSchema."
    );
  }

  // Merge collected defs into output.$defs, taking care not to clobber any existing
  if (!("$defs" in output)) output.$defs = {} as any;
  const finalDefs: Record<string, JSONValue> = (output.$defs as any) || {};
  for (const [k, v] of Object.entries(outDefs)) {
    if (!(k in finalDefs)) finalDefs[k] = v;
  }
  output.$defs = finalDefs as any;

  // Ensure a stable order for readability
  const ordered = orderKeys(output, ["$id", "$schema", "$vocabulary", "$defs", "title", "description", "type", "allOf", "anyOf", "oneOf", "not", "if", "then", "else", "properties", "required", "additionalProperties", "unevaluatedProperties"]);

  fs.mkdirSync(path.dirname(outPath), { recursive: true });
  fs.writeFileSync(outPath, JSON.stringify(ordered, null, 4), "utf8");
  console.log(`Wrote flattened schema to ${outPath}`);
}

function orderKeys(obj: any, preferred: string[]): any {
  if (Array.isArray(obj)) return obj.map((v) => orderKeys(v, preferred));
  if (!obj || typeof obj !== "object") return obj;
  const keys = Object.keys(obj);
  const sorted = [
    ...preferred.filter((k) => keys.includes(k)),
    ...keys.filter((k) => !preferred.includes(k)).sort()
  ];
  const out: any = {};
  for (const k of sorted) out[k] = orderKeys(obj[k], preferred);
  return out;
}

main();
