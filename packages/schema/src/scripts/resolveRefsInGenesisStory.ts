import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { getConfig } from "./_lib/config.js";

type JSONValue = null | boolean | number | string | JSONValue[] | { [k: string]: JSONValue };

interface ResolveOptions {
	storyInPath: string;
	storyOutPath: string;
	schemasPath: string;
	resourceTypesPath: string;
}

function stripSurroundingQuotes(v: string): string {
	if ((v.startsWith("'") && v.endsWith("'")) || (v.startsWith('"') && v.endsWith('"'))) {
		return v.slice(1, -1);
	}
	return v;
}

function parseArgs(): ResolveOptions {
	const config = getConfig();
	const argv = process.argv.slice(2);

	let storyInPath = "";
	let storyOutPath = "";
	let schemasPath = "";
	let resourceTypesPath = "";

	for (let i = 0; i < argv.length; i++) {
		const a = argv[i];
		if ((a === "--in" || a === "--storyIn") && i + 1 < argv.length) storyInPath = stripSurroundingQuotes(argv[++i]);
		else if ((a === "--out" || a === "--storyOut") && i + 1 < argv.length) storyOutPath = stripSurroundingQuotes(argv[++i]);
		else if ((a === "--schemas" || a === "--schemasPath") && i + 1 < argv.length) schemasPath = stripSurroundingQuotes(argv[++i]);
		else if ((a === "--resourceTypes" || a === "--resourceTypesPath") && i + 1 < argv.length)
			resourceTypesPath = stripSurroundingQuotes(argv[++i]);
	}

	// Defaults
	if (!storyInPath) storyInPath = path.join(config.getRoot(), "src/genesis/GenesisStory.json");
	if (!storyOutPath) storyOutPath = path.join(config.getRoot(), "src/genesis/generated/GenesisStory.json");
	// Extracted schemas (raw JSON Schemas)
	if (!schemasPath) schemasPath = config.getOutputPath(config.getSourceFile());
	// ResourceType envelopes (ToolProof resources)
	if (!resourceTypesPath) resourceTypesPath = config.getSourcePath();

	const scriptDir = path.dirname(fileURLToPath(import.meta.url));

	// Resolve relative paths against config root; then fallback relative to script dir
	const resolveWithFallback = (p: string): string => {
		const wasRelative = !path.isAbsolute(p);
		let candidate = p;
		if (wasRelative) candidate = path.join(config.getRoot(), p);
		if (fs.existsSync(candidate)) return candidate;
		if (wasRelative) {
			const alt = path.resolve(scriptDir, p);
			if (fs.existsSync(alt)) return alt;
		}
		return candidate;
	};

	storyInPath = resolveWithFallback(storyInPath);
	storyOutPath = !path.isAbsolute(storyOutPath) ? path.join(config.getRoot(), storyOutPath) : storyOutPath;
	schemasPath = resolveWithFallback(schemasPath);
	resourceTypesPath = resolveWithFallback(resourceTypesPath);

	return { storyInPath, storyOutPath, schemasPath, resourceTypesPath };
}

function deepClone<T>(v: T): T {
	return JSON.parse(JSON.stringify(v)) as T;
}

function isRefWrapper(node: JSONValue): node is { $ref: string } {
	return (
		!!node &&
		typeof node === "object" &&
		!Array.isArray(node) &&
		(node as any).$ref &&
		typeof (node as any).$ref === "string" &&
		Object.keys(node as any).length === 1
	);
}

function refToDefName(ref: string): string {
	let r = stripSurroundingQuotes(ref.trim());

	// Support URL#anchor or URL#/$defs/Name
	const hashIndex = r.indexOf("#");
	if (hashIndex >= 0) r = r.slice(hashIndex + 1);

	// r could now be "Name" or "/$defs/Name" or "$defs/Name"
	if (r.startsWith("/")) r = r.slice(1);
	if (r.startsWith("$defs/")) r = r.slice("$defs/".length);

	// If it's still a JSON pointer-like path, support only '$defs/<Name>'
	if (r.includes("/")) {
		const parts = r.split("/").filter(Boolean);
		if (parts.length === 2 && parts[0] === "$defs") return parts[1];
		throw new Error(`Unsupported $ref format in GenesisStory: '${ref}'`);
	}

	return r;
}

function applyRefAliases(defName: string): string {
	// Backwards-compat / naming aliases (story names -> canonical def names)
	const aliasMap: Record<string, string> = {
		ExtractedSchemaValue: "ExtractionSchemaValue",
		ExtractedSchema: "ExtractionSchema",
	};
	return defName in aliasMap ? aliasMap[defName] : defName;
}

function resolveRawSchemaDef(extractedSchemas: any, defName: string): any {
	defName = applyRefAliases(defName);
	const defs = extractedSchemas?.$defs;
	if (!defs || typeof defs !== "object") {
		throw new Error("Extracted schemas file must contain a top-level $defs object");
	}
	if (defName in defs) return (defs as any)[defName];
	for (const v of Object.values(defs)) {
		if (v && typeof v === "object" && (v as any).$anchor === defName) return v;
	}
	const available = Object.keys(defs).slice(0, 25);
	throw new Error(
		`Could not resolve raw schema for ref '${defName}' from extracted schemas.$defs. ` +
			`Example available keys: ${available.join(", ")}${Object.keys(defs).length > 25 ? ", ..." : ""}`
	);
}

function resolveResourceTypeEnvelope(resourceTypesDoc: any, defName: string): any {
	defName = applyRefAliases(defName);
	const defs = resourceTypesDoc?.extractionSchema?.$defs;
	if (!defs || typeof defs !== "object") {
		throw new Error("ResourceTypes file must contain extractionSchema.$defs");
	}
	if (defName in defs) return (defs as any)[defName];

	// Fallback: match by envelope name or inner extractionSchema $anchor
	for (const v of Object.values(defs)) {
		if (!v || typeof v !== "object") continue;
		if ((v as any).name === defName) return v;
		const innerAnchor = (v as any).extractionSchema?.$anchor;
		if (innerAnchor === defName) return v;
	}

	const available = Object.keys(defs).slice(0, 25);
	throw new Error(
		`Could not resolve ResourceType envelope for ref '${defName}' from resourceTypes.extractionSchema.$defs. ` +
			`Example available keys: ${available.join(", ")}${Object.keys(defs).length > 25 ? ", ..." : ""}`
	);
}

type DefResolver = (defName: string) => any;

function inlineRefs(node: JSONValue, resolve: DefResolver): JSONValue {
	if (isRefWrapper(node)) {
		const defName = refToDefName(node.$ref);
		const resolved = resolve(defName);
		return deepClone(resolved) as JSONValue;
	}

	if (Array.isArray(node)) {
		return node.map((v) => inlineRefs(v as JSONValue, resolve)) as JSONValue;
	}

	if (node && typeof node === "object") {
		const out: Record<string, JSONValue> = {};
		for (const [k, v] of Object.entries(node)) {
			out[k] = inlineRefs(v as JSONValue, resolve);
		}
		return out;
	}

	return node;
}

function main() {
	const { storyInPath, storyOutPath, schemasPath, resourceTypesPath } = parseArgs();

	if (!fs.existsSync(storyInPath)) throw new Error(`Story file not found: ${storyInPath}`);
	if (!fs.existsSync(schemasPath)) throw new Error(`Schemas file not found: ${schemasPath}`);
	if (!fs.existsSync(resourceTypesPath)) throw new Error(`ResourceTypes file not found: ${resourceTypesPath}`);

	const storyRaw = fs.readFileSync(storyInPath, "utf8");
	const story = JSON.parse(storyRaw) as any;

	const schemasRaw = fs.readFileSync(schemasPath, "utf8");
	const extractedSchemas = JSON.parse(schemasRaw);

	const resourceTypesRaw = fs.readFileSync(resourceTypesPath, "utf8");
	const resourceTypesDoc = JSON.parse(resourceTypesRaw);

	const resolved = resolveStoryDocument(story as JSONValue, extractedSchemas, resourceTypesDoc);

	fs.mkdirSync(path.dirname(storyOutPath), { recursive: true });
	fs.writeFileSync(storyOutPath, JSON.stringify(resolved, null, 4) + "\n", "utf8");
	console.log(`Wrote resolved GenesisStory to ${storyOutPath}`);
}

function resolveStoryDocument(storyDoc: JSONValue, extractedSchemas: any, resourceTypesDoc: any): JSONValue {
	// Special handling: switch ref source after a marker in the GenesisStory array.
	if (
		storyDoc &&
		typeof storyDoc === "object" &&
		!Array.isArray(storyDoc) &&
		Array.isArray((storyDoc as any).GenesisStory)
	) {
		const items = (storyDoc as any).GenesisStory as JSONValue[];
		let inResourceTypeSpace = false;

		const resolvedItems = items.map((item) => {
			if (typeof item === "string" && item.trim() === "!!!!!") {
				inResourceTypeSpace = true;
				return item;
			}

			const resolver: DefResolver = inResourceTypeSpace
				? (name) => resolveResourceTypeEnvelope(resourceTypesDoc, name)
				: (name) => resolveRawSchemaDef(extractedSchemas, name);

			return inlineRefs(item, resolver);
		});

		return { ...(storyDoc as any), GenesisStory: resolvedItems } as JSONValue;
	}

	// Fallback: if it's not a GenesisStory doc, just resolve using raw schemas.
	return inlineRefs(storyDoc, (name) => resolveRawSchemaDef(extractedSchemas, name));
}

main();