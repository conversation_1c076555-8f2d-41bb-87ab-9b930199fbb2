/**
 * Configuration for schema generation scripts
 * All paths are configurable via environment variables
 * Provides sensible defaults for standard project structure
 */

import path from 'path';

/**
 * Get environment variable with optional default
 */
function getEnv(name: string, defaultValue: string): string {
  const value = process.env[name];
  return value || defaultValue;
}

/**
 * Schema configuration with required environment variables
 */
export class SchemaConfig {
  // Base paths
  private readonly root: string;
  private readonly sourceDir: string;
  private readonly sourceFile: string;
  private readonly normalizedDir: string;
  private readonly outputDir: string;
  private readonly typesSrcDir: string;
  private readonly typesDistDir: string;
  
  // Schema metadata
  private readonly baseUrl: string;
  private readonly version: string;

  constructor() {
    // Environment variables with sensible defaults
    this.root = getEnv('TP_SCHEMA_ROOT', process.cwd());
    this.sourceDir = getEnv('TP_SCHEMA_SOURCE_DIR', 'src/genesis');
    this.sourceFile = getEnv('TP_SCHEMA_SOURCE_FILE', 'Genesis.json');
    // Intermediate, generated artifact produced by rewriteAnchors.
    // This should NOT live next to the source-of-truth schemas.
    this.normalizedDir = getEnv('TP_SCHEMA_NORMALIZED_DIR', 'src/genesis/generated/resourceTypes');
    this.outputDir = getEnv('TP_SCHEMA_OUTPUT_DIR', 'src/genesis/generated/schemas');
    this.typesSrcDir = getEnv('TP_SCHEMA_TYPES_SRC_DIR', 'src/genesis/generated/types');
    this.typesDistDir = getEnv('TP_SCHEMA_TYPES_DIST_DIR', 'dist/genesis/generated/types');
    this.baseUrl = getEnv('TP_SCHEMA_BASE_URL', 'https://schemas.toolproof.com');
    this.version = getEnv('TP_SCHEMA_VERSION', 'v0');
  }

  // Path getters
  getRoot(): string {
    return this.root;
  }

  getSourceDir(): string {
    return path.isAbsolute(this.sourceDir) 
      ? this.sourceDir 
      : path.join(this.root, this.sourceDir);
  }

  getSourceFile(): string {
    return this.sourceFile;
  }

  getSourcePath(): string {
    return path.join(this.getSourceDir(), this.sourceFile);
  }

  getNormalizedDir(): string {
    return path.isAbsolute(this.normalizedDir)
      ? this.normalizedDir
      : path.join(this.root, this.normalizedDir);
  }

  getNormalizedSourceFile(): string {
    // We keep the same basename (Genesis.json) in the generated folder.
    // The source-of-truth Genesis.json lives under `TP_SCHEMA_SOURCE_DIR`.
    // The generated/normalized Genesis.json lives under `TP_SCHEMA_NORMALIZED_DIR`.
    return this.sourceFile;
  }

  getNormalizedSourcePath(): string {
    return path.join(this.getNormalizedDir(), this.getNormalizedSourceFile());
  }

  getOutputDir(): string {
    return path.isAbsolute(this.outputDir)
      ? this.outputDir
      : path.join(this.root, this.outputDir);
  }

  getOutputPath(filename: string): string {
    return path.join(this.getOutputDir(), filename);
  }

  getTypesSrcDir(): string {
    return path.isAbsolute(this.typesSrcDir)
      ? this.typesSrcDir
      : path.join(this.root, this.typesSrcDir);
  }

  getTypesDistDir(): string {
    return path.isAbsolute(this.typesDistDir)
      ? this.typesDistDir
      : path.join(this.root, this.typesDistDir);
  }

  getTypesSrcPath(filename: string): string {
    return path.join(this.getTypesSrcDir(), filename);
  }

  getTypesDistPath(filename: string): string {
    return path.join(this.getTypesDistDir(), filename);
  }

  // Schema URL methods
  getBaseUrl(): string {
    return this.baseUrl;
  }

  getVersion(): string {
    return this.version;
  }

  getSchemaId(schemaName: string): string {
    return `${this.baseUrl}/${this.version}/${schemaName}.json`;
  }

  /**
   * Check if a URL matches the configured schema base URL pattern
   */
  isSchemaUrl(url: string): boolean {
    const baseUrlPattern = this.baseUrl.replace('https://', 'https?://');
    return new RegExp(`^${baseUrlPattern}/`, 'i').test(url);
  }

  /**
   * Extract schema name from URL (removes base URL and version prefix)
   */
  extractSchemaName(url: string): string {
    // Remove base URL
    let name = url.replace(new RegExp(`^${this.baseUrl}/`, 'i'), '');
    
    // Remove version prefix (v0/, v1/, etc.)
    name = name.replace(/^v\d+\//, '');
    
    // Remove .json extension
    name = name.replace(/\.json$/, '');
    
    return name;
  }
}

// Singleton instance
let configInstance: SchemaConfig | null = null;

/**
 * Get the schema configuration singleton
 * Throws error if required environment variables are not set
 */
export function getConfig(): SchemaConfig {
  if (!configInstance) {
    configInstance = new SchemaConfig();
  }
  return configInstance;
}
