import fs from "fs";
import path from "path";
import { getConfig } from "./_lib/config.js";

function main() {
  const config = getConfig();
  const orderedDepsPath = path.join(config.getRoot(), "src/genesis/generated/dependencies_ordered.json");
  const fallbackDepsPath = path.join(config.getRoot(), "src/genesis/generated/dependencies.json");
  const outPath = path.join(config.getRoot(), "src/genesis/generated/terminals.json");

  const depsPath = fs.existsSync(orderedDepsPath) ? orderedDepsPath : fallbackDepsPath;
  if (!fs.existsSync(depsPath)) {
    throw new Error(`No dependency file found at ${orderedDepsPath} or ${fallbackDepsPath}.`);
  }

  const raw = fs.readFileSync(depsPath, "utf8");
  const dependencyMap = JSON.parse(raw) as Record<string, unknown>;

  const keys = Object.keys(dependencyMap);
  const dependedUpon = new Set<string>();

  for (const key of keys) {
    const deps = (dependencyMap as any)[key];
    if (deps == null) continue;
    if (!Array.isArray(deps)) {
      throw new Error(`Invalid dependencies.json: value for ${key} must be an array`);
    }
    for (const dep of deps) {
      if (typeof dep === "string") dependedUpon.add(dep);
    }
  }

  // Preserve the key order from the dependency file (dependencies_ordered.json)
  // so terminals are emitted in a dependency-respecting order.
  const terminals = keys.filter((k) => !dependedUpon.has(k));

  fs.mkdirSync(path.dirname(outPath), { recursive: true });
  fs.writeFileSync(outPath, JSON.stringify(terminals, null, 4), "utf8");
  console.log(`Wrote terminals to ${outPath}`);
}

main();
