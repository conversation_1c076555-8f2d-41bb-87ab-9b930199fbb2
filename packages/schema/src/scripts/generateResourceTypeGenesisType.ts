import fs from 'fs';
import path from 'path';
import { getConfig } from './_lib/config.js';

/**
 * Generates a TS type mapping Genesis $defs names -> ResourceType.
 *
 * Output:
 * - src/genesis/generated/types/ResourceTypeGenesis.d.ts
 * - dist/genesis/generated/types/ResourceTypeGenesis.d.ts
 */
async function main() {
    const config = getConfig();

    const genesisPath = config.getSourcePath();
    if (!fs.existsSync(genesisPath)) {
        console.error(`Genesis file not found: ${genesisPath}`);
        process.exit(1);
    }

    const raw = fs.readFileSync(genesisPath, 'utf8');
    let parsed: any;
    try {
        parsed = JSON.parse(raw);
    } catch (e) {
        console.error(`Failed to parse JSON: ${genesisPath}`, e);
        process.exit(1);
    }

    const defs = parsed?.extractionSchema?.$defs;
    if (!defs || typeof defs !== 'object' || Array.isArray(defs)) {
        console.error(`Expected extractionSchema.$defs to be an object in: ${genesisPath}`);
        process.exit(1);
    }

    const defNames = Object.keys(defs).sort((a, b) => a.localeCompare(b));
    if (defNames.length === 0) {
        console.error(`No $defs found at extractionSchema.$defs in: ${genesisPath}`);
        process.exit(1);
    }

    const header = '// Auto-generated from src/genesis/Genesis.json. Do not edit.\n';
    const importLine = "import type { ResourceType } from './types.js';\n\n";
    const lines: string[] = [];
    lines.push('export type ResourceTypeGenesis = {');
    for (const name of defNames) {
        // Use a string-literal key to handle any non-identifier $defs names safely.
        lines.push(`  ${JSON.stringify(name)}: ResourceType;`);
    }
    lines.push('};\n');

    const outName = 'ResourceTypeGenesis.d.ts';

    // Write to src
    const srcOutDir = config.getTypesSrcDir();
    fs.mkdirSync(srcOutDir, { recursive: true });
    const srcOutPath = config.getTypesSrcPath(outName);
    fs.writeFileSync(srcOutPath, header + importLine + lines.join('\n'), 'utf8');
    console.log(`Wrote ${srcOutPath}`);

    // Ensure a runtime-resolvable JS shim exists for NodeNext module resolution
    const srcOutJsPath = config.getTypesSrcPath('ResourceTypeGenesis.js');
    if (!fs.existsSync(srcOutJsPath)) {
        fs.writeFileSync(srcOutJsPath, 'export {}\n', 'utf8');
        console.log(`Wrote ${srcOutJsPath}`);
    }

    // Also copy into dist (consistent with other generators)
    const distOutDir = config.getTypesDistDir();
    fs.mkdirSync(distOutDir, { recursive: true });
    const distOutPath = config.getTypesDistPath(outName);
    fs.writeFileSync(distOutPath, header + importLine + lines.join('\n'), 'utf8');
    console.log(`Wrote ${distOutPath}`);

    const distOutJsPath = config.getTypesDistPath('ResourceTypeGenesis.js');
    fs.writeFileSync(distOutJsPath, 'export {}\n', 'utf8');
    console.log(`Wrote ${distOutJsPath}`);
}

main().catch((e) => {
    console.error(e);
    process.exit(1);
});
