
// Re-export JSON schemas via .ts shims to avoid .json re-exports in declarations
export { default as SchemaGenesis } from './genesis/generated/schemas/Genesis.js';
export { default as ResourceTypeGenesis } from './genesis/generated/resource-type-envelopes/Genesis.js';
export { default as SchemaJob } from './genesis/generated/schemas/Job.js';


export type {
  ResourceTypeGenesis as ResourceTypeGenesisJson
} from './genesis/generated/types/ResourceTypeGenesis.js';

export type {
  Resource_ResourceFormat as Resource_ResourceFormatJson
} from './genesis/generated/types/Resource_ResourceFormat.js';
export type {
  Resource_ResourceType as Resource_ResourceTypeJson
} from './genesis/generated/types/Resource_ResourceType.js';
export type {
  Resource_Job as Resource_JobJson
} from './genesis/generated/types/Resource_Job.js';
export type {
  Resource_StatelessStrategy as Resource_StatelessStrategyJson
} from './genesis/generated/types/Resource_StatelessStrategy.js';
export type {
  Resource_StatefulStrategy as Resource_StatefulStrategyJson
} from './genesis/generated/types/Resource_StatefulStrategy.js';
export type {
  Resource_StrategyRun as Resource_StrategyRunJson
} from './genesis/generated/types/Resource_StrategyRun.js';


export type {
  Documented as DocumentedJson,
  ResourceFormatIdentity as ResourceFormatIdentityJson,
  ResourceFormat as ResourceFormatJson,
  ExtractionSchema as ExtractionSchemaJson,
  IdentityProp as IdentityPropJson,
  MeritProp as MeritPropJson,
  ResourceTypeIdentity as ResourceTypeIdentityJson,
  ResourceType as ResourceTypeJson,
  ResourceRoleIdentity as ResourceRoleIdentityJson,
  ResourceRoleValue as ResourceRoleValueJson,
  ExecutionIdentity as ExecutionIdentityJson,
  Execution as ExecutionJson,
  Conditional as ConditionalJson,
  RoleMap as RoleMapJson,
  Roles as RolesJson,
  RoleBindingMap as RoleBindingMapJson,
  RoleBindings as RoleBindingsJson,
  ResourceIdentity as ResourceIdentityJson,
  WorkStepIdentity as WorkStepIdentityJson,
  BranchStepIdentity as BranchStepIdentityJson,
  WhileStepIdentity as WhileStepIdentityJson,
  ForStepIdentity as ForStepIdentityJson,
  WorkStep as WorkStepJson,
  BranchStep as BranchStepJson,
  WhileStep as WhileStepJson,
  ForStep as ForStepJson,
  Step as StepJson,
  CreationContext as CreationContextJson,
  ResourceMissing as ResourceMissingJson,
  ResourcePotentialInput as ResourcePotentialInputJson,
  ResourcePotentialOutput as ResourcePotentialOutputJson,
  ResourceMetaBase as ResourceMetaJson, // ATTENTION: type not generated for ResourceMeta
  Resource as ResourceJson,
  StrategyState as StrategyStateJson,
  StatelessStrategyIdentity as StatelessStrategyIdentityJson,
  StatelessStrategy as StatelessStrategyJson,
  StatefulStrategyIdentity as StatefulStrategyIdentityJson,
  StatefulStrategy as StatefulStrategyJson,
  StrategyRun as StrategyRunJson,
  JobIdentity as JobIdentityJson,
  Job as JobJson,
  JsonData as JsonDataJson,
} from './genesis/generated/types/types.js';
// Re-export brand factories so consumers can construct branded identities at runtime.
export {
  unsafeBrand,
  asResourceTypeIdentity,
  asResourceRoleIdentity,
  asExecutionIdentity,
  asResourceIdentity,
  asWorkStepIdentity,
  asBranchStepIdentity,
  asForStepIdentity,
  asResourceFormatIdentity,
  asWhileStepIdentity,
  asStatelessStrategyIdentity,
  asStatefulStrategyIdentity,
  asResourceTypeIdentities,
  asResourceRoleIdentities,
  asExecutionIdentities,
  asResourceIdentities,
  asWorkStepIdentities,
  asBranchStepIdentities,
  asForStepIdentities,
  asResourceFormatIdentities,
  asWhileStepIdentities,
  asStatelessStrategyIdentities,
  asStatefulStrategyIdentities,
} from './scripts/brandFactories.js';
