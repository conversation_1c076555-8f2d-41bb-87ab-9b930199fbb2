{"GenesisStory": ["The schema/src/genesis folder defines the source-of-truth for the ToolProof ecosystem.", "We can think of a genesis story where at first, in a fresh informational universe, we only have one data instance, which is the JSON specification (RFC 8259/ECMA-404). With that data instance, we have a portal into an informational space where we can define JSON data.", "The first JSON instance we define in this new space is the JSON Schema specification (https://json-schema.org/). With that, we have a set of portals into a set of different informational spaces, namely the spaces defined by each of the JSON Schemas in accordence with the JSON Schema specification. These JSON Schema instances are instances of the JSON Schema specification itself and must not be confused with instances of a particular JSON Schema.", "Note that JSON Schemas act as Set-generators, where the sets are the collections of instances that validate against the respective schemas. Another way to put it is that each of the JSON Schema instances we can define are themselves portals into other spaces, namely Set-like spaces where instances of them live as entities.", "In our JSON Schema space, we now define some schemas that will help us build the ToolProof ecosystem. Note that each schema can depende on previously defined schemas by composition. The schemas we define are:", {"$ref": "Name"}, {"$ref": "Description"}, {"$ref": "Documented"}, {"$ref": "IdentityValue"}, {"$ref": "IdentitySchemaValue"}, {"$ref": "IdentityProp"}, {"$ref": "MeritValue"}, {"$ref": "MeritProp"}, {"$ref": "ExtractionSchemaValue"}, "Next, we create a simple wrapper for our ExtractionSchemaValue called ExtractionSchema. This is called a simple wrapper because it does not add any additional properties at the composition level, but merely wraps a schema to form a self-contained entity which instances can be assigned to the extractionSchema property of a ResourceType (which we'll introduce next).", {"$ref": "ExtractionSchema"}, "We now create a wrapper for ExtractionSchema called ResourceType. Note that this is not a simple wrapper: ResourceType adds additional properties that are required for all ResourceTypes in the ToolProof ecosystem.", "A ResourceType instance composes a JSON Schema via its extractionSchema property (as introduced by the ExtractionSchema wrapper), whose value conforms to ExtractionSchemaValue. Put differently: ExtractionSchemaValue describes schema payloads; ResourceType describes resources that carry schema payloads.", "Crucially, an instance of ResourceType is not itself, technically, a JSON Schema. Remember that we started out with the JSON specification, so we can also have non-schema JSON instances in our ecosystem. That said, a ResourceType instance is schema-like in that it's able to instantiate what we call Resources (to be introduced soon). ResourceType is an abstraction on top of JSON Schema, and from now on we'll use ResourceTypes instead of raw JSON Schemas to expand the ToolProof ecosystem.", {"$ref": "ResourceType"}, "With ResourceType defined, now have a different animal and we can leave the JSON Schema space.", "!!!!!", "We'll first introduce a new concept: terminality. Terminality can be thought of as an implicit property of ResourceTypes, meaning that a ResourceType instance is either a non-terminal or a terminal. Implicit means that terminality is not represented as a property on ResourceType instances; rather, it's derived from whether the ResourceType has a life-cycle independent of composing entities. Non-terminal ResourceTypes are ResourceTypes that exist solely to be composed by other ResourceTypes, whereas terminal ResourceTypes are ResourceTypes that have an independent life-cycle and can be instantiated into what we'll call Resources (to be introduced soon).", "Next, we turn all the schemas we defined initially in JSON Schema space into ResourceTypes by creating ResourceType instances whose extractionSchema points at those schemas.", {"$ref": "Name"}, {"$ref": "Description"}, {"$ref": "Documented"}, {"$ref": "IdentityValue"}, {"$ref": "IdentitySchemaValue"}, {"$ref": "IdentityProp"}, {"$ref": "MeritValue"}, {"$ref": "MeritProp"}, {"$ref": "ExtractionSchemaValue"}, {"$ref": "ExtractionSchema"}, "Note that these schema-derived ResourceTypes are all non-terminal, as they exist solely to be composed by other ResourceTypes. The exeption is the last schema we defined, ExtractionSchema, as we not yet have a ResourceType that composes it.", "But doesn't ResourceType itself compose ExtractionSchema? Yes, but ResourceType is not yet a ResourceType instance... Let's fix that now by creating a ResourceType instance for ResourceType itself. This ResourceType instance (the ResourceType ResourceType) is our first terminal.", {"$ref": "ResourceType"}]}