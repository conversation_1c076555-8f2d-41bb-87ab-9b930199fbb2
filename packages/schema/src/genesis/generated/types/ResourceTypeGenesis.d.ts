// Auto-generated from src/genesis/Genesis.json. Do not edit.
import type { ResourceType } from './types.js';

export type ResourceTypeGenesis = {
  "BranchStep": ResourceType;
  "BranchStepIdentity": ResourceType;
  "Conditional": ResourceType;
  "CreationContext": ResourceType;
  "CreationContextWrapper": ResourceType;
  "Described": ResourceType;
  "Documented": ResourceType;
  "Execution": ResourceType;
  "ExecutionIdentity": ResourceType;
  "ExtractionSchema": ResourceType;
  "ExtractionSchemaWrapper": ResourceType;
  "ForStep": ResourceType;
  "ForStepIdentity": ResourceType;
  "IdentityProp": ResourceType;
  "IdentitySchema": ResourceType;
  "IdentitySchemaRef": ResourceType;
  "Job": ResourceType;
  "JobIdentity": ResourceType;
  "JsonData": ResourceType;
  "MeritProp": ResourceType;
  "MeritSchema": ResourceType;
  "MeritSchemaRef": ResourceType;
  "Named": ResourceType;
  "Path": ResourceType;
  "Resource": ResourceType;
  "ResourceBase": ResourceType;
  "ResourceFormat": ResourceType;
  "ResourceFormatIdentity": ResourceType;
  "ResourceIdentity": ResourceType;
  "ResourceKind": ResourceType;
  "ResourceMeta": ResourceType;
  "ResourceMetaBase": ResourceType;
  "ResourceMissing": ResourceType;
  "ResourcePotentialInput": ResourceType;
  "ResourcePotentialOutput": ResourceType;
  "ResourceRoleIdentity": ResourceType;
  "ResourceRoleValue": ResourceType;
  "ResourceType": ResourceType;
  "ResourceTypeIdentity": ResourceType;
  "RoleBindingMap": ResourceType;
  "RoleBindings": ResourceType;
  "RoleBindingsWrapper": ResourceType;
  "RoleMap": ResourceType;
  "Roles": ResourceType;
  "RolesWrapper": ResourceType;
  "StatefulStrategy": ResourceType;
  "StatefulStrategyIdentity": ResourceType;
  "StatefulStrategyWrapper": ResourceType;
  "StatelessStrategy": ResourceType;
  "StatelessStrategyIdentity": ResourceType;
  "StatelessStrategyWrapper": ResourceType;
  "Step": ResourceType;
  "StepKind": ResourceType;
  "StrategyRun": ResourceType;
  "StrategyRunContext": ResourceType;
  "StrategyRunIdentity": ResourceType;
  "StrategyRunStatus": ResourceType;
  "StrategyState": ResourceType;
  "StrategyStateWrapper": ResourceType;
  "StrategyThreadIdentity": ResourceType;
  "StrategyThreadMap": ResourceType;
  "Timestamp": ResourceType;
  "WhileStep": ResourceType;
  "WhileStepIdentity": ResourceType;
  "WorkStep": ResourceType;
  "WorkStepIdentity": ResourceType;
};
