// Auto-generated from JSON schemas. Do not edit.

/**
 * This interface was referenced by `Genesis<PERSON>son`'s JSON-Schema
 * via the `definition` "BranchStep".
 */
export type BranchStep =
 {
  /**
   * @minItems 1
   */
  cases: [Conditional, ...Conditional[]];
  identity: BranchStepIdentity;
  kind: "branch";
} & StepKind;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WorkStep".
 */
export type WorkStep =
 {
  execution: Execution;
  identity: WorkStepIdentity;
  kind: "work";
} & StepKind;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Execution".
 */
export type Execution =
 {
  identity: ExecutionIdentity;
  jobRef: JobIdentity;
} & RoleBindingsWrapper;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ExecutionIdentity".
 */
export type ExecutionIdentity =
 `EXECUTION-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s J<PERSON><PERSON>-Schema
 * via the `definition` "JobIdentity".
 */
export type JobIdentity =
 `JOB-${string}`;
/**
 * This interface was referenced by `<PERSON><PERSON><PERSON>`'s JSON-Schema
 * via the `definition` "ResourceIdentity".
 */
export type ResourceIdentity =
 `RESOURCE-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WorkStepIdentity".
 */
export type WorkStepIdentity =
 `WORKSTEP-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "BranchStepIdentity".
 */
export type BranchStepIdentity =
 `BRANCHSTEP-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceRoleIdentity".
 */
export type ResourceRoleIdentity =
 `ROLE-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Documented".
 */
export type Documented =
 Named & Described;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ExtractionSchema".
 */
export type ExtractionSchema =
 {
  $schema: "https://json-schema.org/draft/2020-12/schema";
  $defs?: Record<string, unknown>;
  type: "object";
  allOf?: Array<{[k: string]: unknown}>;
  properties?: Record<string, unknown>;
  required?: string[];
  additionalProperties?: false;
  unevaluatedProperties?: false;
  $anchor: string;
} & {
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ForStep".
 */
export type ForStep =
 {
  case: Conditional;
  identity: ForStepIdentity;
  kind: "for";
} & StepKind;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ForStepIdentity".
 */
export type ForStepIdentity =
 `FORSTEP-${string}`;
/**
 * This interface was referenced by `undefined`'s JSON-Schema definition
 * via the `patternProperty` "^[A-Za-z][A-Za-z0-9._-]*Identity$".
 *
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "IdentitySchema".
 */
export type IdentitySchema =
 {
  $schema?: "https://json-schema.org/draft/2020-12/schema";
  type: "string" | "number" | "integer" | "boolean";
  $anchor?: string;
  $comment?: string;
  /**
   * @minItems 1
   */
  enum?: [unknown, ...unknown[]];
  format?: string;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
} & {
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Job".
 */
export type Job =
 {
  identity: JobIdentity;
  implementationUri: string;
} & Documented &
  RolesWrapper;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceRoleValue".
 */
export type ResourceRoleValue =
 {
  resourceTypeRef: ResourceTypeIdentity;
} & Documented;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceTypeIdentity".
 */
export type ResourceTypeIdentity =
 `TYPE-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "JsonData".
 */
export type JsonData =
  | null
  | boolean
  | number
  | string
  | JsonData
  | {
      [k: string]: JsonData;
    };
/**
 * This interface was referenced by `undefined`'s JSON-Schema definition
 * via the `patternProperty` "^[A-Za-z][A-Za-z0-9._-]*Merit$".
 *
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "MeritSchema".
 */
export type MeritSchema =
 {
  $schema?: "https://json-schema.org/draft/2020-12/schema";
  type?: "number" | "integer";
  $anchor?: string;
  $comment?: string;
  /**
   * @minItems 1
   */
  enum?: [number, ...number[]];
  exclusiveMaximum?: number;
  exclusiveMinimum?: number;
  maximum?: number;
  minimum?: number;
  multipleOf?: number;
} & {
} & MeritSchema1;
export type MeritSchema1 =
  | {
      type: "number" | "integer";
}
  | {
      /**
       * @minItems 1
       */
      enum: [number, ...number[]];
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Resource".
 */
export type Resource =
 {
} & ResourceMetaBase & {
    extractedData: {
      [k: string]: JsonData;
    };
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceMetaBase".
 */
export type ResourceMetaBase =
 ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "materialized";
} & Timestamp &
  Path;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceFormat".
 */
export type ResourceFormat =
 {
  identity: ResourceFormatIdentity;
  recognizerUri: string;
} & Documented;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceFormatIdentity".
 */
export type ResourceFormatIdentity =
 `FORMAT-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceMeta".
 */
export type ResourceMetaBase1 =
 ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "materialized";
} & Timestamp &
  Path;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceMissing".
 */
export type ResourceMissing =
 {
} & ResourceBase &
  ResourceKind & {
    kind: "missing";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourcePotentialInput".
 */
export type ResourcePotentialInput =
 {
} & ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "potential-input";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourcePotentialOutput".
 */
export type ResourcePotentialOutput =
 {
} & ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "potential-output";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceType".
 */
export type ResourceType =
 {
  identity: ResourceTypeIdentity;
  resourceFormatRef: ResourceFormatIdentity;
} & Documented &
  ExtractionSchemaWrapper & {
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatefulStrategy".
 */
export type StatefulStrategy =
 {
  identity: StatefulStrategyIdentity;
} & StatelessStrategyWrapper &
  StrategyStateWrapper;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatefulStrategyIdentity".
 */
export type StatefulStrategyIdentity =
 `STATEFUL_STRATEGY-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatelessStrategyIdentity".
 */
export type StatelessStrategyIdentity =
 `STATELESS_STRATEGY-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Step".
 */
export type Step =
 WorkStep | BranchStep | WhileStep | ForStep;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WhileStep".
 */
export type WhileStep =
 {
  case: Conditional;
  identity: WhileStepIdentity;
  kind: "while";
} & StepKind;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WhileStepIdentity".
 */
export type WhileStepIdentity =
 `WHILESTEP-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyRun".
 */
export type StrategyRun =
 {
  identity: StrategyRunIdentity;
  statefulStrategyRef: StatefulStrategyIdentity;
  strategyRunContext: StrategyRunContext;
} & StrategyThreadMap &
  StrategyStateWrapper1;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyRunIdentity".
 */
export type StrategyRunIdentity =
 `STRATEGY_RUN-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyRunStatus".
 */
export type StrategyRunStatus =
 "pending" | "running" | "completed" | "failed" | "cancelled";
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyThreadIdentity".
 */
export type StrategyThreadIdentity =
 `STRATEGY_THREAD-${string}`;

export interface GenesisJson {
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Conditional".
 */
export interface Conditional {
  what: WorkStep;
  when: WorkStep;
}
export interface RoleBindingsWrapper {
  roleBindings: RoleBindings;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleBindings".
 */
export interface RoleBindings {
  inputBindingMap: RoleBindingMap;
  outputBindingMap: RoleBindingMap;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleBindingMap".
 */
export type RoleBindingMap = Record<ResourceRoleIdentity, ResourceIdentity>;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StepKind".
 */
export interface StepKind {
  kind: "work" | "branch" | "while" | "for";
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "CreationContext".
 */
export interface CreationContext {
  executionRef: ExecutionIdentity;
  resourceRoleRef: ResourceRoleIdentity;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "CreationContextWrapper".
 */
export interface CreationContextWrapper {
  creationContext: CreationContext;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Described".
 */
export interface Described {
  description: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Named".
 */
export interface Named {
  name: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ExtractionSchemaWrapper".
 */
export interface ExtractionSchemaWrapper {
  extractionSchema: ExtractionSchema;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "IdentityProp".
 */
export interface IdentityProp {
  $defs: Record<string, unknown>;
  properties: {
    identity: IdentitySchemaRef;
};
  required: string[];
  additionalProperties?: unknown;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "IdentitySchemaRef".
 */
export interface IdentitySchemaRef {
  $ref: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RolesWrapper".
 */
export interface RolesWrapper {
  roles: Roles;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Roles".
 */
export interface Roles {
  inputMap: RoleMap;
  outputMap: RoleMap;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleMap".
 */
export type RoleMap = Record<ResourceRoleIdentity, ResourceRoleValue>;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "MeritProp".
 */
export interface MeritProp {
  $defs: Record<string, unknown>;
  properties: {
    merit: MeritSchemaRef;
};
  required: string[];
  additionalProperties?: unknown;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "MeritSchemaRef".
 */
export interface MeritSchemaRef {
  $ref: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Path".
 */
export interface Path {
  path: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceBase".
 */
export interface ResourceBase {
  identity: ResourceIdentity;
  resourceTypeRef: ResourceTypeIdentity;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceKind".
 */
export interface ResourceKind {
  kind: "missing" | "potential-input" | "potential-output" | "materialized";
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Timestamp".
 */
export interface Timestamp {
  timestamp: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleBindingsWrapper".
 */
export interface RoleBindingsWrapper1 {
  roleBindings: RoleBindings;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatelessStrategyWrapper".
 */
export interface StatelessStrategyWrapper {
  statelessStrategy: StatelessStrategy;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatelessStrategy".
 */
export interface StatelessStrategy {
  identity: StatelessStrategyIdentity;
  steps: Step[];
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyStateWrapper".
 */
export interface StrategyStateWrapper {
  strategyState: StrategyState;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyState".
 */
export type StrategyState = Record<ExecutionIdentity, Record<ResourceRoleIdentity, ResourceMissing | ResourcePotentialInput | ResourcePotentialOutput | Resource>>;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatefulStrategyWrapper".
 */
export interface StatefulStrategyWrapper {
  statefulStrategy: StatefulStrategy;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyRunContext".
 */
export interface StrategyRunContext {
  completedAt?: Timestamp;
  startedAt?: Timestamp;
  status: StrategyRunStatus;
}
export interface StrategyThreadMap {
  [k: string]: Step[];
}
export interface StrategyStateWrapper1 {
  strategyState: StrategyState;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyThreadMap".
 */
export interface StrategyThreadMap1 {
  [k: string]: Step[];
}

export type Normalized =
 {
  identity: JobIdentity;
  implementationUri: string;
} & Documented &
  RolesWrapper;
export type JobIdentity =
 `JOB-${string}`;
export type Documented =
 Named & Described;
export type ResourceRoleValue =
 {
  resourceTypeRef: ResourceTypeIdentity;
} & Documented;
export type ResourceTypeIdentity =
 `TYPE-${string}`;

export interface Named {
  name: string;
}
export interface Described {
  description: string;
}
export interface RolesWrapper {
  roles: Roles;
}
export interface Roles {
  inputMap: RoleMap;
  outputMap: RoleMap;
}
export type RoleMap = Record<ResourceRoleIdentity, ResourceRoleValue>;

export type Normalized =
 {
  identity: ResourceFormatIdentity;
  recognizerUri: string;
} & Documented;
export type ResourceFormatIdentity =
 `FORMAT-${string}`;
export type Documented =
 Named & Described;

export interface Named {
  name: string;
}
export interface Described {
  description: string;
}

export type Normalized =
 {
  identity: ResourceTypeIdentity;
  resourceFormatRef: ResourceFormatIdentity;
} & Documented &
  ExtractionSchemaWrapper & {
};
export type ResourceTypeIdentity =
 `TYPE-${string}`;
export type ResourceFormatIdentity =
 `FORMAT-${string}`;
export type Documented =
 Named & Described;
export type ExtractionSchema =
 {
  $schema: "https://json-schema.org/draft/2020-12/schema";
  $defs?: Record<string, unknown>;
  type: "object";
  allOf?: Array<{[k: string]: unknown}>;
  properties?: Record<string, unknown>;
  required?: string[];
  additionalProperties?: false;
  unevaluatedProperties?: false;
  $anchor: string;
} & {
};

export interface Named {
  name: string;
}
export interface Described {
  description: string;
}
export interface ExtractionSchemaWrapper {
  extractionSchema: ExtractionSchema;
}

export type Normalized =
 {
  identity: StatefulStrategyIdentity;
} & StatelessStrategyWrapper &
  StrategyStateWrapper;
export type StatefulStrategyIdentity =
 `STATEFUL_STRATEGY-${string}`;
export type StatelessStrategyIdentity =
 `STATELESS_STRATEGY-${string}`;
export type Step =
 WorkStep | BranchStep | WhileStep | ForStep;
export type WorkStep =
 {
  execution: Execution;
  identity: WorkStepIdentity;
  kind: "work";
} & StepKind;
export type Execution =
 {
  identity: ExecutionIdentity;
  jobRef: JobIdentity;
} & RoleBindingsWrapper;
export type ExecutionIdentity =
 `EXECUTION-${string}`;
export type JobIdentity =
 `JOB-${string}`;
export type ResourceIdentity =
 `RESOURCE-${string}`;
export type WorkStepIdentity =
 `WORKSTEP-${string}`;
export type BranchStep =
 {
  /**
   * @minItems 1
   */
  cases: [Conditional, ...Conditional[]];
  identity: BranchStepIdentity;
  kind: "branch";
} & StepKind;
export type BranchStepIdentity =
 `BRANCHSTEP-${string}`;
export type WhileStep =
 {
  case: Conditional;
  identity: WhileStepIdentity;
  kind: "while";
} & StepKind;
export type WhileStepIdentity =
 `WHILESTEP-${string}`;
export type ForStep =
 {
  case: Conditional;
  identity: ForStepIdentity;
  kind: "for";
} & StepKind;
export type ForStepIdentity =
 `FORSTEP-${string}`;
export type ResourceMissing =
 {
} & ResourceBase &
  ResourceKind & {
    kind: "missing";
};
export type ResourceTypeIdentity =
 `TYPE-${string}`;
export type ResourcePotentialInput =
 {
} & ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "potential-input";
};
export type ResourceRoleIdentity =
 `ROLE-${string}`;
export type ResourcePotentialOutput =
 {
} & ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "potential-output";
};
export type Resource =
 {
} & ResourceMetaBase & {
    extractedData: {
      [k: string]: JsonData;
    };
};
export type ResourceMetaBase =
 ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "materialized";
} & Timestamp &
  Path;
export type JsonData =
  | null
  | boolean
  | number
  | string
  | JsonData
  | {
      [k: string]: JsonData;
    };

export interface StatelessStrategyWrapper {
  statelessStrategy: StatelessStrategy;
}
export interface StatelessStrategy {
  identity: StatelessStrategyIdentity;
  steps: Step[];
}
export interface RoleBindingsWrapper {
  roleBindings: RoleBindings;
}
export interface RoleBindings {
  inputBindingMap: RoleBindingMap;
  outputBindingMap: RoleBindingMap;
}
export type RoleBindingMap = Record<ResourceRoleIdentity, ResourceIdentity>;
export interface StepKind {
  kind: "work" | "branch" | "while" | "for";
}
export interface Conditional {
  what: WorkStep;
  when: WorkStep;
}
export interface StrategyStateWrapper {
  strategyState: StrategyState;
}
export type StrategyState = Record<ExecutionIdentity, Record<ResourceRoleIdentity, ResourceMissing | ResourcePotentialInput | ResourcePotentialOutput | Resource>>;
export interface ResourceBase {
  identity: ResourceIdentity;
  resourceTypeRef: ResourceTypeIdentity;
}
export interface ResourceKind {
  kind: "missing" | "potential-input" | "potential-output" | "materialized";
}
export interface CreationContextWrapper {
  creationContext: CreationContext;
}
export interface CreationContext {
  executionRef: ExecutionIdentity;
  resourceRoleRef: ResourceRoleIdentity;
}
export interface Timestamp {
  timestamp: string;
}
export interface Path {
  path: string;
}

/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "StatelessStrategyIdentity".
 */
export type StatelessStrategyIdentity =
 `STATELESS_STRATEGY-${string}`;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "Step".
 */
export type Step =
 WorkStep | BranchStep | WhileStep | ForStep;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "WorkStep".
 */
export type WorkStep =
 {
  execution: Execution;
  identity: WorkStepIdentity;
  kind: "work";
} & StepKind;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "Execution".
 */
export type Execution =
 {
  identity: ExecutionIdentity;
  jobRef: JobIdentity;
} & RoleBindingsWrapper;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "ExecutionIdentity".
 */
export type ExecutionIdentity =
 `EXECUTION-${string}`;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "JobIdentity".
 */
export type JobIdentity =
 `JOB-${string}`;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "ResourceIdentity".
 */
export type ResourceIdentity =
 `RESOURCE-${string}`;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "WorkStepIdentity".
 */
export type WorkStepIdentity =
 `WORKSTEP-${string}`;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "BranchStep".
 */
export type BranchStep =
 {
  /**
   * @minItems 1
   */
  cases: [Conditional, ...Conditional[]];
  identity: BranchStepIdentity;
  kind: "branch";
} & StepKind;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "BranchStepIdentity".
 */
export type BranchStepIdentity =
 `BRANCHSTEP-${string}`;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "WhileStep".
 */
export type WhileStep =
 {
  case: Conditional;
  identity: WhileStepIdentity;
  kind: "while";
} & StepKind;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "WhileStepIdentity".
 */
export type WhileStepIdentity =
 `WHILESTEP-${string}`;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "ForStep".
 */
export type ForStep =
 {
  case: Conditional;
  identity: ForStepIdentity;
  kind: "for";
} & StepKind;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "ForStepIdentity".
 */
export type ForStepIdentity =
 `FORSTEP-${string}`;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "ResourceRoleIdentity".
 */
export type ResourceRoleIdentity =
 `ROLE-${string}`;

export interface Normalized {
  identity: StatelessStrategyIdentity;
  steps: Step[];
}
export interface RoleBindingsWrapper {
  roleBindings: RoleBindings;
}
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "RoleBindings".
 */
export interface RoleBindings {
  inputBindingMap: RoleBindingMap;
  outputBindingMap: RoleBindingMap;
}
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "RoleBindingMap".
 */
export type RoleBindingMap = Record<ResourceRoleIdentity, ResourceIdentity>;
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "StepKind".
 */
export interface StepKind {
  kind: "work" | "branch" | "while" | "for";
}
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "Conditional".
 */
export interface Conditional {
  what: WorkStep;
  when: WorkStep;
}
/**
 * This interface was referenced by `Normalized`'s JSON-Schema
 * via the `definition` "RoleBindingsWrapper".
 */
export interface RoleBindingsWrapper1 {
  roleBindings: RoleBindings;
}

export type Normalized =
 {
  identity: StrategyRunIdentity;
  statefulStrategyRef: StatefulStrategyIdentity;
  strategyRunContext: StrategyRunContext;
} & StrategyThreadMap &
  StrategyStateWrapper;
export type StrategyRunIdentity =
 `STRATEGY_RUN-${string}`;
export type StatefulStrategyIdentity =
 `STATEFUL_STRATEGY-${string}`;
export type StrategyRunStatus =
 "pending" | "running" | "completed" | "failed" | "cancelled";
export type Step =
 WorkStep | BranchStep | WhileStep | ForStep;
export type WorkStep =
 {
  execution: Execution;
  identity: WorkStepIdentity;
  kind: "work";
} & StepKind;
export type Execution =
 {
  identity: ExecutionIdentity;
  jobRef: JobIdentity;
} & RoleBindingsWrapper;
export type ExecutionIdentity =
 `EXECUTION-${string}`;
export type JobIdentity =
 `JOB-${string}`;
export type ResourceIdentity =
 `RESOURCE-${string}`;
export type WorkStepIdentity =
 `WORKSTEP-${string}`;
export type BranchStep =
 {
  /**
   * @minItems 1
   */
  cases: [Conditional, ...Conditional[]];
  identity: BranchStepIdentity;
  kind: "branch";
} & StepKind;
export type BranchStepIdentity =
 `BRANCHSTEP-${string}`;
export type WhileStep =
 {
  case: Conditional;
  identity: WhileStepIdentity;
  kind: "while";
} & StepKind;
export type WhileStepIdentity =
 `WHILESTEP-${string}`;
export type ForStep =
 {
  case: Conditional;
  identity: ForStepIdentity;
  kind: "for";
} & StepKind;
export type ForStepIdentity =
 `FORSTEP-${string}`;
export type ResourceMissing =
 {
} & ResourceBase &
  ResourceKind & {
    kind: "missing";
};
export type ResourceTypeIdentity =
 `TYPE-${string}`;
export type ResourcePotentialInput =
 {
} & ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "potential-input";
};
export type ResourceRoleIdentity =
 `ROLE-${string}`;
export type ResourcePotentialOutput =
 {
} & ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "potential-output";
};
export type Resource =
 {
} & ResourceMetaBase & {
    extractedData: {
      [k: string]: JsonData;
    };
};
export type ResourceMetaBase =
 ResourceBase &
  CreationContextWrapper &
  ResourceKind & {
    kind: "materialized";
} & Timestamp &
  Path;
export type JsonData =
  | null
  | boolean
  | number
  | string
  | JsonData
  | {
      [k: string]: JsonData;
    };

export interface StrategyRunContext {
  completedAt?: Timestamp;
  startedAt?: Timestamp;
  status: StrategyRunStatus;
}
export interface Timestamp {
  timestamp: string;
}
export interface StrategyThreadMap {
  [k: string]: Step[];
}
export interface RoleBindingsWrapper {
  roleBindings: RoleBindings;
}
export interface RoleBindings {
  inputBindingMap: RoleBindingMap;
  outputBindingMap: RoleBindingMap;
}
export type RoleBindingMap = Record<ResourceRoleIdentity, ResourceIdentity>;
export interface StepKind {
  kind: "work" | "branch" | "while" | "for";
}
export interface Conditional {
  what: WorkStep;
  when: WorkStep;
}
export interface StrategyStateWrapper {
  strategyState: StrategyState;
}
export type StrategyState = Record<ExecutionIdentity, Record<ResourceRoleIdentity, ResourceMissing | ResourcePotentialInput | ResourcePotentialOutput | Resource>>;
export interface ResourceBase {
  identity: ResourceIdentity;
  resourceTypeRef: ResourceTypeIdentity;
}
export interface ResourceKind {
  kind: "missing" | "potential-input" | "potential-output" | "materialized";
}
export interface CreationContextWrapper {
  creationContext: CreationContext;
}
export interface CreationContext {
  executionRef: ExecutionIdentity;
  resourceRoleRef: ResourceRoleIdentity;
}
export interface Path {
  path: string;
}

