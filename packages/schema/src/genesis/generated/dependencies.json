{"BranchStep": ["<PERSON><PERSON><PERSON>", "Conditional", "BranchStepIdentity"], "BranchStepIdentity": [], "Conditional": ["WorkStep"], "CreationContext": ["ExecutionIdentity", "ResourceRoleIdentity"], "CreationContextWrapper": ["CreationContext"], "Described": [], "Documented": ["Named", "Described"], "Error": ["Documented"], "Execution": ["RoleBindingsWrapper", "ExecutionIdentity", "JobIdentity"], "ExecutionIdentity": [], "ExtractionSchema": ["IdentityProp", "MeritProp"], "ExtractionSchemaWrapper": ["ExtractionSchema"], "ForStep": ["<PERSON><PERSON><PERSON>", "Conditional", "ForStepIdentity"], "ForStepIdentity": [], "IdentityProp": ["IdentitySchema", "IdentitySchemaRef"], "IdentitySchema": [], "IdentitySchemaRef": [], "Job": ["Documented", "RolesWrapper", "JobIdentity"], "JobIdentity": [], "JsonData": [], "MeritProp": ["MeritSchema", "MeritSchemaRef"], "MeritSchema": [], "MeritSchemaRef": [], "Named": [], "Path": [], "Resource": ["ResourceMetaBase", "JsonData"], "ResourceBase": ["ResourceIdentity", "ResourceTypeIdentity"], "ResourceFormat": ["Documented", "ResourceFormatIdentity"], "ResourceFormatIdentity": [], "ResourceIdentity": [], "ResourceKind": [], "ResourceMeta": ["ResourceMetaBase"], "ResourceMetaBase": ["ResourceBase", "CreationContextWrapper", "ResourceKind", "Timestamp", "Path"], "ResourceMissing": ["ResourceBase", "ResourceKind"], "ResourcePotentialInput": ["ResourceBase", "CreationContextWrapper", "ResourceKind"], "ResourcePotentialOutput": ["ResourceBase", "CreationContextWrapper", "ResourceKind"], "ResourceRoleIdentity": [], "ResourceRoleValue": ["ResourceTypeIdentity", "Documented"], "ResourceType": ["Documented", "ExtractionSchemaWrapper", "ResourceTypeIdentity", "ResourceFormatIdentity"], "ResourceTypeIdentity": [], "RoleBindingMap": ["ResourceIdentity", "ResourceRoleIdentity"], "RoleBindings": ["RoleBindingMap"], "RoleBindingsWrapper": ["RoleBindings"], "RoleMap": ["ResourceRoleValue", "ResourceRoleIdentity"], "Roles": ["RoleMap", "ResourceRoleValue"], "RolesWrapper": ["Roles"], "StatefulStrategy": ["StatelessStrategyWrapper", "StrategyStateWrapper", "StatefulStrategyIdentity"], "StatefulStrategyIdentity": [], "StatelessStrategy": ["StatelessStrategyIdentity", "Step"], "StatelessStrategyIdentity": [], "StatelessStrategyWrapper": ["StatelessStrategy"], "Step": ["WorkStep", "BranchStep", "WhileStep", "ForStep"], "StepKind": [], "StrategyRun": ["StrategyThreadMapWrapper", "StrategyStateWrapper", "StrategyRunIdentity", "StatefulStrategyIdentity", "StrategyRunContext"], "StrategyRunContext": ["Timestamp", "StrategyRunStatus"], "StrategyRunIdentity": [], "StrategyRunStatus": [], "StrategyState": ["ResourceMissing", "ResourcePotentialInput", "ResourcePotentialOutput", "Resource", "ResourceRoleIdentity", "ExecutionIdentity"], "StrategyStateWrapper": ["StrategyState"], "StrategyThreadIdentity": [], "StrategyThreadMap": ["Step", "StrategyThreadIdentity"], "StrategyThreadMapWrapper": ["StrategyThreadMap"], "Timestamp": [], "WhileStep": ["<PERSON><PERSON><PERSON>", "Conditional", "WhileStepIdentity"], "WhileStepIdentity": [], "WorkStep": ["<PERSON><PERSON><PERSON>", "Execution", "WorkStepIdentity"], "WorkStepIdentity": []}