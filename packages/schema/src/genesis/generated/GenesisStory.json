{"GenesisStory": ["The schema/src/genesis folder defines the source-of-truth for the ToolProof ecosystem.", "We can think of a genesis story where at first, in a fresh informational universe, we only have one data instance, which is the JSON specification (RFC 8259/ECMA-404). With that data instance, we have a portal into an informational space where can define JSON data.", "The first JSON instance we define in this new space is the JSON Schema specification (https://json-schema.org/). With that, we have a set of portals into a set of different informational spaces, namely the spaces defined by each of the JSON Schemas in accordence with the JSON Schema specification. These JSON Schema instances are instances of the JSON Schema specification itself and must not be confused with instances of a particular JSON Schema.", "Note that JSON Schemas act as Set-generators, where the sets are the collections of instances that validate against the respective schemas. Another way to put it is that each of the JSON Schema instances we can define are themselves portals into other spaces, namely Set-like spaces where instances of them live as entities.", "In our JSON Schema space, we now define some schemas that will help us build the ToolProof ecosystem. Note that each schema can depende on previously defined schemas by composition. The schemas we define are:", {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Name"}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Description"}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Name"}, {"$ref": "#/$defs/Description"}], "$anchor": "Documented"}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$ref": {"type": "string", "$comment": "Supports both anchor syntax (#<Name>Id) and JSON Pointer syntax (#/path/to/definition). Anchors use #<Name>Id for identity refs. Top-level and nested $defs should expose an $anchor matching <Name>Id.", "pattern": "^#([A-Za-z][A-Za-z0-9._-]*Id|/.*)$"}}, "required": ["$ref"], "additionalProperties": false, "$anchor": "IdentityValue"}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"type": {"enum": ["string", "number", "integer", "boolean"]}}, "required": ["type"], "$anchor": "IdSchemaValue"}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$defs": {"type": "object", "additionalProperties": true, "$comment": "Ajv-specific: patternRequired enforces at least one Id-like key in this $defs block when identity is present.", "minProperties": 1, "patternProperties": {"^[A-Za-z][A-Za-z0-9._-]*Id$": {"$ref": "#/$defs/IdSchemaValue"}}, "patternRequired": ["^[A-Za-z][A-Za-z0-9._-]*Id$"]}, "properties": {"type": "object", "properties": {"identity": {"$ref": "#/$defs/IdentityValue"}}, "required": ["identity"]}, "required": {"type": "array", "contains": {"const": "identity"}, "items": {"type": "string"}, "uniqueItems": true}}, "required": ["$defs", "required", "properties"], "$anchor": "IdentityProp"}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"properties": {"type": {"enum": ["number", "integer"]}}, "required": ["type"]}, {"properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}, "required": ["enum"]}], "$anchor": "MeritValue"}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"properties": {"type": "object", "properties": {"merit": {"$ref": "#/$defs/MeritValue"}}}}, "$anchor": "MeritProp"}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"if": {"anyOf": [{"properties": {"properties": {"type": "object", "properties": {"identity": {}}}}}, {"properties": {"required": {"type": "array", "contains": {"const": "identity"}}}}]}, "then": {"$ref": "#/$defs/IdentityProp"}}, {"if": {"properties": {"properties": {"type": "object", "properties": {"merit": {}}}}}, "then": {"$ref": "#/$defs/MeritProp"}}, {"oneOf": [{"required": ["additionalProperties"]}, {"required": ["unevaluatedProperties"]}]}], "properties": {"$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$defs": {"type": "object"}, "type": {"const": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object"}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "additionalProperties": {"const": false}, "unevaluatedProperties": {"const": false}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}}, "required": ["$schema", "$anchor", "type"], "unevaluatedProperties": false, "$anchor": "ExtractionSchemaValue"}, "Next, we create a simple wrapper for our ExtractionSchemaValue called ExtractionSchema. This is called a simple wrapper because it does not add any additional properties at the composition level, but merely wraps a schema to form a self-contained entity which instances can be assigned to the extractionSchema property of a ResourceType (which we'll introduce next).", {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"extractionSchema": {"$ref": "#/$defs/ExtractionSchemaValue"}}, "required": ["extractionSchema"], "$anchor": "ExtractionSchema"}, "We now create a wrapper for ExtractionSchema called ResourceType. Note that this is not a simple wrapper: ResourceType adds additional properties that are required for all ResourceTypes in the ToolProof ecosystem.", "A ResourceType instance composes a JSON Schema via its extractionSchema property (as introduced by the ExtractionSchema wrapper), whose value conforms to ExtractionSchemaValue. Put differently: ExtractionSchemaValue describes schema payloads; ResourceType describes resources that carry schema payloads.", "Crucially, an instance of ResourceType is not itself, technically, a JSON Schema. Remember that we started out with the JSON specification, so we can also have non-schema JSON instances in our ecosystem. That said, a ResourceType instance is schema-like in that it's able to instantiate what we call Resources (to be introduced soon). ResourceType is an abstraction on top of JSON Schema, and from now on we'll use ResourceTypes instead of raw JSON Schemas to expand the ToolProof ecosystem.", {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"properties": {"identity": {"$ref": "#/$defs/ResourceTypeId"}, "resourceFormatId": {"$ref": "#/$defs/ResourceFormatId"}}, "required": ["identity", "resourceFormatId"]}, {"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/ExtractionSchema"}, {"if": {"properties": {"resourceFormatId": {"const": "FORMAT-ApplicationJson"}}, "$comment": "If resourceFormatId is FORMAT-ApplicationJson, then extractor must not be present, but if resourceFormatId is not FORMAT-ApplicationJson, then extractor must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor."}, "then": {"not": {"required": ["extractor"]}}, "else": {"type": "object", "properties": {"extractor": {"type": "string", "format": "uri"}}, "required": ["extractor"]}}], "$anchor": "ResourceType"}, "With ResourceType defined, now have a different animal and we can leave the JSON Schema space.", "!!!!!", "We'll first introduce a new concept: terminality. Terminality can be thought of as an implicit property of ResourceTypes, meaning that a ResourceType instance is either a non-terminal or a terminal. Implicit means that terminality is not represented as a property on ResourceType instances; rather, it's derived from whether the ResourceType has a life-cycle independent of composing entities. Non-terminal ResourceTypes are ResourceTypes that exist solely to be composed by other ResourceTypes, whereas terminal ResourceTypes are ResourceTypes that have an independent life-cycle and can be instantiated into what we'll call Resources (to be introduced soon).", "Next, we turn all the schemas we defined initially in JSON Schema space into ResourceTypes by creating ResourceType instances whose extractionSchema points at those schemas.", {"identity": "TYPE-Name", "name": "Name", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Name", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["name"], "properties": {"name": {"type": "string", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "semanticValidation": "Ajv custom keyword to verify name."}}}}, {"identity": "TYPE-Description", "name": "Description", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Description", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["description"], "properties": {"description": {"type": "string", "minLength": 1, "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "semanticValidation": "Ajv custom keyword to verify description."}}}}, {"identity": "TYPE-Documented", "name": "Documented", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Documented", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#Name"}, {"$ref": "#Description"}]}}, {"identity": "TYPE-IdentityValue", "name": "IdentityValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentityValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$ref"], "properties": {"$ref": {"type": "string", "$comment": "Supports both anchor syntax (#<Name>Id) and JSON Pointer syntax (#/path/to/definition). Anchors use #<Name>Id for identity refs. Top-level and nested $defs should expose an $anchor matching <Name>Id.", "pattern": "^#([A-Za-z][A-Za-z0-9._-]*Id|/.*)$"}}, "additionalProperties": false}}, {"identity": "TYPE-IdSchemaValue", "name": "IdSchemaValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdSchemaValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["type"], "properties": {"type": {"enum": ["string", "number", "integer", "boolean"]}}}}, {"identity": "TYPE-IdentityProp", "name": "IdentityProp", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentityProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$defs", "required", "properties"], "properties": {"$defs": {"type": "object", "patternProperties": {"^[A-Za-z][A-Za-z0-9._-]*Id$": {"$ref": "#IdSchemaValue"}}, "$comment": "Ajv-specific: patternRequired enforces at least one Id-like key in this $defs block when identity is present.", "patternRequired": ["^[A-Za-z][A-Za-z0-9._-]*Id$"], "minProperties": 1, "additionalProperties": true}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "contains": {"const": "identity"}}, "properties": {"type": "object", "required": ["identity"], "properties": {"identity": {"$ref": "#IdentityValue"}}}}}}, {"identity": "TYPE-MeritValue", "name": "MeritValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "MeritValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"required": ["type"], "properties": {"type": {"enum": ["number", "integer"]}}}, {"required": ["enum"], "properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}}]}}, {"identity": "TYPE-MeritProp", "name": "MeritProp", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "MeritProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"properties": {"type": "object", "properties": {"merit": {"$ref": "#MeritValue"}}}}}}, {"identity": "TYPE-ExtractionSchemaValue", "name": "ExtractionSchemaValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExtractionSchemaValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$schema", "$anchor", "type"], "properties": {"$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}, "$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$defs": {"type": "object"}, "type": {"const": "object"}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}, "additionalProperties": {"const": false}, "unevaluatedProperties": {"const": false}}, "allOf": [{"if": {"anyOf": [{"properties": {"properties": {"type": "object", "properties": {"identity": {}}}}}, {"properties": {"required": {"type": "array", "contains": {"const": "identity"}}}}]}, "then": {"$ref": "#IdentityProp"}}, {"if": {"properties": {"properties": {"type": "object", "properties": {"merit": {}}}}}, "then": {"$ref": "#MeritProp"}}, {"oneOf": [{"required": ["additionalProperties"]}, {"required": ["unevaluatedProperties"]}]}], "unevaluatedProperties": false}}, {"identity": "TYPE-ExtractionSchema", "name": "ExtractionSchema", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExtractionSchema", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["extractionSchema"], "properties": {"extractionSchema": {"$ref": "#ExtractionSchemaValue"}}}}, "Note that these schema-derived ResourceTypes are all non-terminal, as they exist solely to be composed by other ResourceTypes. The exeption is the last schema we defined, ExtractionSchema, as we not yet have a ResourceType that composes it.", "But doesn't ResourceType itself compose ExtractionSchema? Yes, but ResourceType is not yet a ResourceType instance... Let's fix that now by creating a ResourceType instance for ResourceType itself. This ResourceType instance (ResourceType ResourceType) is our first terminal.", {"identity": "TYPE-ResourceType", "name": "ResourceType", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceType", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"required": ["identity", "resourceFormatId"], "properties": {"identity": {"$ref": "#ResourceTypeId"}, "resourceFormatId": {"$ref": "#ResourceFormatId"}}}, {"$ref": "#Documented"}, {"$ref": "#ExtractionSchema"}, {"if": {"$comment": "If resourceFormatId is FORMAT-ApplicationJson, then extractor must not be present, but if resourceFormatId is not FORMAT-ApplicationJson, then extractor must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor.", "properties": {"resourceFormatId": {"const": "FORMAT-ApplicationJson"}}}, "then": {"not": {"required": ["extractor"]}}, "else": {"type": "object", "required": ["extractor"], "properties": {"extractor": {"type": "string", "format": "uri"}}}}]}}]}