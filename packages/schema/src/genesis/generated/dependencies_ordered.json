{"Described": [], "IdentitySchema": [], "IdentitySchemaRef": [], "IdentityProp": ["IdentitySchema", "IdentitySchemaRef"], "MeritSchema": [], "MeritSchemaRef": [], "MeritProp": ["MeritSchema", "MeritSchemaRef"], "ExtractionSchema": ["IdentityProp", "MeritProp"], "ExtractionSchemaWrapper": ["ExtractionSchema"], "Named": [], "Documented": ["Named", "Described"], "ResourceFormatIdentity": [], "ResourceTypeIdentity": [], "ResourceType": ["Documented", "ExtractionSchemaWrapper", "ResourceTypeIdentity", "ResourceFormatIdentity"], "ExecutionIdentity": [], "JsonData": [], "Path": [], "ResourceIdentity": [], "ResourceBase": ["ResourceIdentity", "ResourceTypeIdentity"], "ResourceKind": [], "ResourceRoleIdentity": [], "CreationContext": ["ExecutionIdentity", "ResourceRoleIdentity"], "CreationContextWrapper": ["CreationContext"], "Timestamp": [], "ResourceMetaBase": ["ResourceBase", "CreationContextWrapper", "ResourceKind", "Timestamp", "Path"], "Resource": ["ResourceMetaBase", "JsonData"], "BranchStepIdentity": [], "Error": ["Documented"], "ForStepIdentity": [], "JobIdentity": [], "ResourceFormat": ["Documented", "ResourceFormatIdentity"], "ResourceMeta": ["ResourceMetaBase"], "ResourceMissing": ["ResourceBase", "ResourceKind"], "ResourcePotentialInput": ["ResourceBase", "CreationContextWrapper", "ResourceKind"], "ResourcePotentialOutput": ["ResourceBase", "CreationContextWrapper", "ResourceKind"], "ResourceRoleValue": ["ResourceTypeIdentity", "Documented"], "RoleBindingMap": ["ResourceIdentity", "ResourceRoleIdentity"], "RoleBindings": ["RoleBindingMap"], "RoleBindingsWrapper": ["RoleBindings"], "Execution": ["RoleBindingsWrapper", "ExecutionIdentity", "JobIdentity"], "RoleMap": ["ResourceRoleValue", "ResourceRoleIdentity"], "Roles": ["RoleMap", "ResourceRoleValue"], "RolesWrapper": ["Roles"], "Job": ["Documented", "RolesWrapper", "JobIdentity"], "StatefulStrategyIdentity": [], "StatelessStrategyIdentity": [], "StepKind": [], "StrategyRunIdentity": [], "StrategyRunStatus": [], "StrategyRunContext": ["Timestamp", "StrategyRunStatus"], "StrategyState": ["ResourceMissing", "ResourcePotentialInput", "ResourcePotentialOutput", "Resource", "ResourceRoleIdentity", "ExecutionIdentity"], "StrategyStateWrapper": ["StrategyState"], "StrategyThreadIdentity": [], "WhileStepIdentity": [], "WorkStepIdentity": [], "WorkStep": ["<PERSON><PERSON><PERSON>", "Execution", "WorkStepIdentity"], "Conditional": ["WorkStep"], "BranchStep": ["<PERSON><PERSON><PERSON>", "Conditional", "BranchStepIdentity"], "ForStep": ["<PERSON><PERSON><PERSON>", "Conditional", "ForStepIdentity"], "WhileStep": ["<PERSON><PERSON><PERSON>", "Conditional", "WhileStepIdentity"], "Step": ["WorkStep", "BranchStep", "WhileStep", "ForStep"], "StatelessStrategy": ["StatelessStrategyIdentity", "Step"], "StatelessStrategyWrapper": ["StatelessStrategy"], "StatefulStrategy": ["StatelessStrategyWrapper", "StrategyStateWrapper", "StatefulStrategyIdentity"], "StrategyThreadMap": ["Step", "StrategyThreadIdentity"], "StrategyThreadMapWrapper": ["StrategyThreadMap"], "StrategyRun": ["StrategyThreadMapWrapper", "StrategyStateWrapper", "StrategyRunIdentity", "StatefulStrategyIdentity", "StrategyRunContext"]}