{"identity": "TYPE-Genesis", "name": "Genesis", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$comment": "This schema defines all genesis schemas used throughout the Toolproof ecosystem. The genesis schemas themselves are defined in $defs.<ResourceType>.extractionSchema. The build process (via extractSchemas.js) extracts these schemas and writes them to a separate file (genesis/generated/schemas/Genesis.json). The reason for this indirection is to have all these schema envelopes validate positively against the schema at $defs/ResourceType, effectively making them ResourceTypes, which are first-class citizens in the Toolproof ecosystem.", "$id": "https://schemas.toolproof.com/v0/Genesis.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "$defs": {"Named": {"identity": "TYPE-Named", "name": "Named", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Named", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["name"], "properties": {"name": {"type": "string", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "semanticValidation": "Ajv custom keyword to verify name."}}}}, "Described": {"identity": "TYPE-Described", "name": "Described", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Described", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["description"], "properties": {"description": {"type": "string", "minLength": 1, "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "semanticValidation": "Ajv custom keyword to verify description."}}}}, "Documented": {"identity": "TYPE-Documented", "name": "Documented", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Documented", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Named"}, {"$ref": "#/$defs/Described"}]}}, "IdentitySchemaRef": {"identity": "TYPE-IdentitySchemaRef", "name": "IdentitySchemaRef", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentitySchemaRef", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$ref"], "properties": {"$ref": {"type": "string", "$comment": "Supports both anchor syntax (#<Name>Identity) and JSON Pointer syntax (#/path/to/definition). Anchors use #<Name>Identity for identity refs. Top-level and nested $defs should expose an $anchor matching <Name>Identity.", "pattern": "^#([A-Za-z][A-Za-z0-9._-]*Identity|/.*)$"}}, "additionalProperties": false}}, "IdentitySchema": {"identity": "TYPE-IdentitySchema", "name": "IdentitySchema", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentitySchema", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["type"], "properties": {"$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*Identity$"}, "$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$comment": {"type": "string"}, "type": {"enum": ["string", "number", "integer", "boolean"]}, "pattern": {"type": "string"}, "format": {"type": "string"}, "minLength": {"type": "integer", "minimum": 0}, "maxLength": {"type": "integer", "minimum": 0}, "enum": {"type": "array", "minItems": 1, "uniqueItems": true}}, "allOf": [{"if": {"properties": {"type": {"const": "string"}}}, "then": {"properties": {"enum": {"items": {"type": "string"}}}}, "else": {"not": {"anyOf": [{"required": ["pattern"]}, {"required": ["format"]}, {"required": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"required": ["max<PERSON><PERSON><PERSON>"]}]}}}, {"if": {"properties": {"type": {"const": "number"}}}, "then": {"properties": {"enum": {"items": {"type": "number"}}}}}, {"if": {"properties": {"type": {"const": "integer"}}}, "then": {"properties": {"enum": {"items": {"type": "integer"}}}}}, {"if": {"properties": {"type": {"const": "boolean"}}}, "then": {"properties": {"enum": {"items": {"type": "boolean"}}}}}], "additionalProperties": false}}, "IdentityProp": {"identity": "TYPE-IdentityProp", "name": "IdentityProp", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentityProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$defs", "required", "properties"], "properties": {"$defs": {"type": "object", "patternProperties": {"^[A-Za-z][A-Za-z0-9._-]*Identity$": {"$ref": "#/$defs/IdentitySchema"}}, "$comment": "Ajv-specific: patternRequired enforces at least one Identity-like key in this $defs block when identity is present.", "patternRequired": ["^[A-Za-z][A-Za-z0-9._-]*Identity$"], "minProperties": 1, "additionalProperties": true}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "contains": {"const": "identity"}}, "properties": {"type": "object", "required": ["identity"], "properties": {"identity": {"$ref": "#/$defs/IdentitySchemaRef"}}}}}}, "MeritSchemaRef": {"identity": "TYPE-MeritSchemaRef", "name": "MeritSchemaRef", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "MeritSchemaRef", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$ref"], "properties": {"$ref": {"type": "string", "$comment": "Supports both anchor syntax (#<Name>Merit) and JSON Pointer syntax (#/path/to/definition). Anchors use #<Name>Merit for merit refs. Top-level and nested $defs should expose an $anchor matching <Name>Merit.", "pattern": "^#([A-Za-z][A-Za-z0-9._-]*Merit|/.*)$"}}, "additionalProperties": false}}, "MeritSchema": {"identity": "TYPE-MeritSchema", "name": "MeritSchema", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "MeritSchema", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*Merit$"}, "$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$comment": {"type": "string"}, "type": {"enum": ["number", "integer"]}, "enum": {"type": "array", "items": {"type": "number"}, "minItems": 1, "uniqueItems": true}, "minimum": {"type": "number"}, "maximum": {"type": "number"}, "exclusiveMinimum": {"type": "number"}, "exclusiveMaximum": {"type": "number"}, "multipleOf": {"type": "number", "exclusiveMinimum": 0}}, "oneOf": [{"required": ["type"], "properties": {"type": {"enum": ["number", "integer"]}}}, {"required": ["enum"], "properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}}], "allOf": [{"$comment": "Numeric constraints (minimum/maximum/etc) only make sense when using the explicit type form; the enum-only form is allowed for simple enumerations.", "if": {"required": ["type"]}, "then": {}, "else": {"not": {"anyOf": [{"required": ["minimum"]}, {"required": ["maximum"]}, {"required": ["exclusiveMinimum"]}, {"required": ["exclusiveMaximum"]}, {"required": ["multipleOf"]}]}}}], "additionalProperties": false}}, "MeritProp": {"identity": "TYPE-MeritProp", "name": "MeritProp", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "MeritProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$defs", "required", "properties"], "properties": {"$defs": {"type": "object", "patternProperties": {"^[A-Za-z][A-Za-z0-9._-]*Merit$": {"$ref": "#/$defs/MeritSchema"}}, "$comment": "Ajv-specific: patternRequired enforces at least one Merit-like key in this $defs block when merit is present.", "patternRequired": ["^[A-Za-z][A-Za-z0-9._-]*Merit$"], "minProperties": 1, "additionalProperties": true}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "properties": {"type": "object", "required": ["merit"], "properties": {"merit": {"$ref": "#/$defs/MeritSchemaRef"}}}}}}, "ExtractionSchema": {"identity": "TYPE-ExtractionSchema", "name": "ExtractionSchema", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExtractionSchema", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$schema", "$anchor", "type"], "properties": {"$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}, "$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$defs": {"type": "object"}, "type": {"const": "object"}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}, "additionalProperties": {"const": false}, "unevaluatedProperties": {"const": false}}, "allOf": [{"if": {"anyOf": [{"properties": {"properties": {"type": "object", "required": ["identity"], "properties": {"identity": {}}}}}, {"properties": {"required": {"type": "array", "contains": {"const": "identity"}}}}]}, "then": {"$ref": "#/$defs/IdentityProp"}}, {"if": {"anyOf": [{"properties": {"properties": {"type": "object", "required": ["merit"], "properties": {"merit": {}}}}}, {"properties": {"required": {"type": "array", "contains": {"const": "merit"}}}}]}, "then": {"$ref": "#/$defs/MeritProp"}}, {"oneOf": [{"required": ["additionalProperties"]}, {"required": ["unevaluatedProperties"]}]}], "unevaluatedProperties": false}}, "ExtractionSchemaWrapper": {"identity": "TYPE-ExtractionSchemaWrapper", "name": "ExtractionSchemaWrapper", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExtractionSchemaWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["extractionSchema"], "properties": {"extractionSchema": {"$ref": "#/$defs/ExtractionSchema"}}}}, "ResourceFormatIdentity": {"identity": "TYPE-ResourceFormatIdentity", "name": "ResourceFormatIdentity", "description": "dummy-description", "resourceFormatIdentity": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceFormatIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^FORMAT-.+$"}}, "ResourceTypeIdentity": {"identity": "TYPE-ResourceTypeIdentity", "name": "ResourceTypeIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceTypeIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^TYPE-.+$"}}, "ResourceType": {"identity": "TYPE-ResourceType", "name": "ResourceType", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceType", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "resourceFormatRef"], "properties": {"identity": {"$ref": "#/$defs/ResourceTypeIdentity"}, "resourceFormatRef": {"$ref": "#/$defs/ResourceFormatIdentity"}}, "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/ExtractionSchemaWrapper"}, {"if": {"$comment": "If resourceFormatRef is FORMAT-ApplicationJson, then extractor<PERSON>ri must not be present, but if resourceFormatRef is not FORMAT-ApplicationJson, then extractorUri must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor.", "properties": {"resourceFormatRef": {"const": "FORMAT-ApplicationJson"}}}, "then": {"not": {"required": ["extractorUri"]}}, "else": {"type": "object", "required": ["extractorUri"], "properties": {"extractorUri": {"type": "string", "format": "uri"}}}}]}}, "ResourceFormat": {"identity": "TYPE-ResourceFormat", "name": "ResourceFormat", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceFormat", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "<PERSON><PERSON><PERSON><PERSON>"], "properties": {"identity": {"$ref": "#/$defs/ResourceFormatIdentity"}, "recognizerUri": {"type": "string", "format": "uri"}}, "allOf": [{"$ref": "#/$defs/Documented"}]}}, "ResourceRoleIdentity": {"identity": "TYPE-ResourceRoleIdentity", "name": "ResourceRoleIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceRoleIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^ROLE-.+$"}}, "ResourceIdentity": {"identity": "TYPE-ResourceIdentity", "name": "ResourceIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$comment": "", "pattern": "^RESOURCE-.+$"}}, "ResourceRoleValue": {"identity": "TYPE-ResourceRoleValue", "name": "ResourceRoleValue", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceRoleValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "A ResourceRole does not have a self-contained identity, as it is always defined in the context of a RoleMap. ResourceRoleValue uses the Value suffix to mean 'the value stored in this map' (RoleMap.additionalProperties).", "required": ["resourceTypeRef"], "properties": {"resourceTypeRef": {"$ref": "#/$defs/ResourceTypeIdentity"}}}, {"$ref": "#/$defs/Documented"}]}}, "RoleMap": {"identity": "TYPE-RoleMap", "name": "RoleMap", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}, "additionalProperties": {"$ref": "#/$defs/ResourceRoleValue"}}}, "Roles": {"identity": "TYPE-Roles", "name": "Roles", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Roles", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["inputMap", "outputMap"], "properties": {"inputMap": {"$ref": "#/$defs/RoleMap"}, "outputMap": {"$ref": "#/$defs/RoleMap"}}, "unevaluatedProperties": false}}, "RolesWrapper": {"identity": "TYPE-RolesWrapper", "name": "RolesWrapper", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RolesWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["roles"], "properties": {"roles": {"$ref": "#/$defs/Roles"}}}}, "JobIdentity": {"identity": "TYPE-JobIdentity", "name": "JobIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "JobIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$comment": "", "pattern": "^JOB-.+$"}}, "Job": {"identity": "TYPE-Job", "name": "Job", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJob", "extractorUri": "https://extractors.toolproof.com/v0/JobExtractor.js", "extractionSchema": {"$anchor": "Job", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "implementationUri"], "properties": {"identity": {"$ref": "#/$defs/JobIdentity"}, "implementationUri": {"type": "string", "format": "uri"}}, "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/RolesWrapper"}], "unevaluatedProperties": false}}, "RoleBindingMap": {"identity": "TYPE-RoleBindingMap", "name": "RoleBindingMap", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleBindingMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}, "additionalProperties": {"$ref": "#/$defs/ResourceIdentity"}}}, "RoleBindings": {"identity": "TYPE-RoleBindings", "name": "RoleBindings", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleBindings", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["inputBindingMap", "outputBindingMap"], "properties": {"inputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}, "outputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}}, "unevaluatedProperties": false}}, "RoleBindingsWrapper": {"identity": "TYPE-RoleBindingsWrapper", "name": "RoleBindingsWrapper", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleBindingsWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["roleB<PERSON>ings"], "properties": {"roleBindings": {"$ref": "#/$defs/RoleBindings"}}}}, "CreationContext": {"identity": "TYPE-CreationContext", "name": "CreationContext", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "CreationContext", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["resourceRoleRef", "executionRef"], "properties": {"resourceRoleRef": {"$ref": "#/$defs/ResourceRoleIdentity"}, "executionRef": {"$ref": "#/$defs/ExecutionIdentity"}}}}, "CreationContextWrapper": {"identity": "TYPE-CreationContextWrapper", "name": "CreationContextWrapper", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "CreationContextWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["creationContext"], "properties": {"creationContext": {"$ref": "#/$defs/CreationContext"}}}}, "ResourceBase": {"identity": "TYPE-ResourceBase", "name": "ResourceBase", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "resourceTypeRef"], "properties": {"identity": {"$ref": "#/$defs/ResourceIdentity"}, "resourceTypeRef": {"$ref": "#/$defs/ResourceTypeIdentity"}}}}, "ResourceKind": {"identity": "TYPE-ResourceKind", "name": "ResourceKind", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceKind", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["kind"], "properties": {"kind": {"enum": ["missing", "potential-input", "potential-output", "materialized"]}}}}, "ResourceMissing": {"identity": "TYPE-ResourceMissing", "name": "ResourceMissing", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceMissing", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "missing"}}}], "unevaluatedProperties": false}}, "ResourcePotentialInput": {"identity": "TYPE-ResourcePotentialInput", "name": "ResourcePotentialInput", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourcePotentialInput", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "potential-input"}}}], "unevaluatedProperties": false}}, "ResourcePotentialOutput": {"identity": "TYPE-ResourcePotentialOutput", "name": "ResourcePotentialOutput", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourcePotentialOutput", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "potential-output"}}}], "unevaluatedProperties": false}}, "Timestamp": {"identity": "TYPE-Timestamp", "name": "Timestamp", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Timestamp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["timestamp"], "properties": {"timestamp": {"type": "string", "format": "date-time"}}}}, "Path": {"identity": "TYPE-Path", "name": "Path", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Path", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["path"], "properties": {"path": {"type": "string"}}}}, "ResourceMetaBase": {"identity": "TYPE-ResourceMetaBase", "name": "ResourceMetaBase", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceMetaBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "materialized"}}}, {"$ref": "#/$defs/Timestamp"}, {"$ref": "#/$defs/Path"}]}}, "ResourceMeta": {"identity": "TYPE-ResourceMeta", "name": "ResourceMeta", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "$ref": "#/$defs/ResourceMetaBase", "unevaluatedProperties": false}}, "JsonData": {"identity": "TYPE-JsonData", "name": "JsonData", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "JsonData", "$schema": "https://json-schema.org/draft/2020-12/schema", "oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"$ref": "#/$defs/JsonData"}}, {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonData"}}]}}, "Resource": {"identity": "TYPE-Resource", "name": "Resource", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Resource", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceMetaBase"}, {"required": ["extractedData"], "properties": {"extractedData": {"$comment": "This will be overlayed at runtime to match the data structure of the underlying type's extractionSchema. At compile time, we guarantee it has an identity property.", "type": "object", "additionalProperties": {"$ref": "#/$defs/JsonData"}}}}], "unevaluatedProperties": false}}, "ExecutionIdentity": {"identity": "TYPE-ExecutionIdentity", "name": "ExecutionIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExecutionIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^EXECUTION-.+$"}}, "Execution": {"identity": "TYPE-Execution", "name": "Execution", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Execution", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "jobRef"], "properties": {"identity": {"$ref": "#/$defs/ExecutionIdentity"}, "jobRef": {"$ref": "#/$defs/JobIdentity"}}, "allOf": [{"$comment": "This will be overlayed at runtime to specify roleBindings corresponding to the roles of the underlying job.", "$ref": "#/$defs/RoleBindingsWrapper"}]}}, "Conditional": {"identity": "TYPE-Conditional", "name": "Conditional", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Conditional", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["when", "what"], "properties": {"when": {"$ref": "#/$defs/WorkStep"}, "what": {"$ref": "#/$defs/WorkStep"}}, "unevaluatedProperties": false}}, "StepKind": {"identity": "TYPE-<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "<PERSON><PERSON><PERSON>", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["kind"], "properties": {"kind": {"type": "string", "enum": ["work", "branch", "while", "for"]}}}}, "WorkStepIdentity": {"identity": "TYPE-WorkStepIdentity", "name": "WorkStepIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WorkStepIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WORKSTEP-.+$"}}, "WorkStep": {"identity": "TYPE-WorkStep", "name": "WorkStep", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WorkStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "kind", "execution"], "properties": {"identity": {"$ref": "#/$defs/WorkStepIdentity"}, "kind": {"const": "work"}, "execution": {"$ref": "#/$defs/Execution"}}, "allOf": [{"$ref": "#/$defs/StepKind"}]}}, "BranchStepIdentity": {"identity": "TYPE-BranchStepIdentity", "name": "BranchStepIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "BranchStepIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^BRANCHSTEP-.+$"}}, "BranchStep": {"identity": "TYPE-BranchStep", "name": "BranchStep", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "BranchStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "kind", "cases"], "properties": {"identity": {"$ref": "#/$defs/BranchStepIdentity"}, "kind": {"const": "branch"}, "cases": {"type": "array", "items": {"$ref": "#/$defs/Conditional"}, "minItems": 1, "uniqueItems": true}}, "allOf": [{"$ref": "#/$defs/StepKind"}]}}, "WhileStepIdentity": {"identity": "TYPE-WhileStepIdentity", "name": "WhileStepIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WhileStepIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WHILESTEP-.+$"}}, "WhileStep": {"identity": "TYPE-WhileStep", "name": "WhileStep", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WhileStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "kind", "case"], "properties": {"identity": {"$ref": "#/$defs/WhileStepIdentity"}, "kind": {"const": "while"}, "case": {"$ref": "#/$defs/Conditional"}}, "allOf": [{"$ref": "#/$defs/StepKind"}]}}, "ForStepIdentity": {"identity": "TYPE-ForStepIdentity", "name": "ForStepIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ForStepIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^FORSTEP-.+$"}}, "ForStep": {"identity": "TYPE-ForStep", "name": "ForStep", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ForStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "kind", "case"], "properties": {"identity": {"$ref": "#/$defs/ForStepIdentity"}, "kind": {"const": "for"}, "case": {"$ref": "#/$defs/Conditional"}}, "allOf": [{"$ref": "#/$defs/StepKind"}]}}, "Step": {"identity": "TYPE-Step", "name": "Step", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Step", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStep"}, {"$ref": "#/$defs/BranchStep"}, {"$ref": "#/$defs/WhileStep"}, {"$ref": "#/$defs/ForStep"}], "unevaluatedProperties": false}}, "StatelessStrategyIdentity": {"identity": "TYPE-StatelessStrategyIdentity", "name": "StatelessStrategyIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatelessStrategyIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^STATELESS_STRATEGY-.+$"}}, "StatelessStrategy": {"identity": "TYPE-StatelessStrategy", "name": "StatelessStrategy", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatelessStrategy", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "steps"], "properties": {"identity": {"$ref": "#/$defs/StatelessStrategyIdentity"}, "steps": {"type": "array", "items": {"$ref": "#/$defs/Step"}, "uniqueItems": true}}, "unevaluatedProperties": false}}, "StatelessStrategyWrapper": {"identity": "TYPE-StatelessStrategyWrapper", "name": "StatelessStrategyWrapper", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatelessStrategyWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["statelessStrategy"], "properties": {"statelessStrategy": {"$ref": "#/$defs/StatelessStrategy"}}}}, "StrategyState": {"identity": "TYPE-StrategyState", "name": "StrategyState", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyState", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/ExecutionIdentity"}, "additionalProperties": {"type": "object", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}, "additionalProperties": {"oneOf": [{"$ref": "#/$defs/ResourceMissing"}, {"$ref": "#/$defs/ResourcePotentialInput"}, {"$ref": "#/$defs/ResourcePotentialOutput"}, {"$ref": "#/$defs/Resource"}]}}}}, "StrategyStateWrapper": {"identity": "TYPE-StrategyStateWrapper", "name": "StrategyStateWrapper", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyStateWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["strategyState"], "properties": {"strategyState": {"$ref": "#/$defs/StrategyState"}}}}, "StatefulStrategyIdentity": {"identity": "TYPE-StatefulStrategyIdentity", "name": "StatefulStrategyIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatefulStrategyIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^STATEFUL_STRATEGY-.+$"}}, "StatefulStrategy": {"identity": "TYPE-StatefulStrategy", "name": "StatefulStrategy", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatefulStrategy", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity"], "properties": {"identity": {"$ref": "#/$defs/StatefulStrategyIdentity"}}, "allOf": [{"$ref": "#/$defs/StatelessStrategyWrapper"}, {"$ref": "#/$defs/StrategyStateWrapper"}], "unevaluatedProperties": false}}, "StatefulStrategyWrapper": {"identity": "TYPE-StatefulStrategyWrapper", "name": "StatefulStrategyWrapper", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatefulStrategyWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["statefulStrategy"], "properties": {"statefulStrategy": {"$ref": "#/$defs/StatefulStrategy"}}}}, "StrategyRunIdentity": {"identity": "TYPE-StrategyRunIdentity", "name": "StrategyRunIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyRunIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^STRATEGY_RUN-.+$"}}, "StrategyRunStatus": {"identity": "TYPE-StrategyRunStatus", "name": "StrategyRunStatus", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyRunStatus", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "enum": ["pending", "running", "completed", "failed", "cancelled"]}}, "StrategyRunContext": {"identity": "TYPE-StrategyRunContext", "name": "StrategyRunContext", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyRunContext", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["status"], "properties": {"status": {"$ref": "#/$defs/StrategyRunStatus"}, "startedAt": {"$ref": "#/$defs/Timestamp"}, "completedAt": {"$ref": "#/$defs/Timestamp"}}, "unevaluatedProperties": false}}, "StrategyThreadIdentity": {"identity": "TYPE-StrategyThreadIdentity", "name": "StrategyThreadIdentity", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyThreadIdentity", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^STRATEGY_THREAD-.+$"}}, "StrategyThreadMap": {"identity": "TYPE-StrategyThreadMap", "name": "StrategyThreadMap", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyThreadMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/StrategyThreadIdentity"}, "additionalProperties": {"type": "array", "items": {"$ref": "#/$defs/Step"}}, "$comment": "Each thread contains an array of steps, allowing for sequential execution within a thread while supporting parallel execution across multiple threads."}}, "StrategyRun": {"identity": "TYPE-StrategyRun", "name": "StrategyRun", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyRun", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity", "statefulStrategyRef", "strategyRunContext"], "properties": {"identity": {"$ref": "#/$defs/StrategyRunIdentity"}, "statefulStrategyRef": {"$ref": "#/$defs/StatefulStrategyIdentity"}, "strategyRunContext": {"$ref": "#/$defs/StrategyRunContext"}}, "allOf": [{"$comment": "The Engine will convert the statelessStrategy.steps array of the referenced StatefulStrategy into this map before the run. This is to allow jobs that do not depend on previous-step inputs to run in parallel.The Engine will also inject internal steps to prompt for runtime-provided inputs and handle other orchestration tasks such as repeted steps when encountering a loop.", "$ref": "#/$defs/StrategyThreadMap"}, {"$comment": "Reusing StrategyStateWrapper to include the strategy state in the StrategyRun. The Engine will update it with materialized Resources (i.e. runtime-provided inputs and job-created outputs) during the run.", "$ref": "#/$defs/StrategyStateWrapper"}], "unevaluatedProperties": false}}}}}