{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "The Engine will convert the statelessStrategy.steps array of the referenced StatefulStrategy into this map before the run. This is to allow jobs that do not depend on previous-step inputs to run in parallel.The Engine will also inject internal steps to prompt for runtime-provided inputs and handle other orchestration tasks such as repeted steps when encountering a loop.", "$ref": "#/$defs/StrategyThreadMap"}, {"$comment": "Reusing StrategyStateWrapper to include the strategy state in the StrategyRun. The Engine will update it with materialized Resources (i.e. runtime-provided inputs and job-created outputs) during the run.", "$ref": "#/$defs/StrategyStateWrapper"}], "properties": {"identity": {"$ref": "#/$defs/StrategyRunIdentity"}, "statefulStrategyRef": {"$ref": "#/$defs/StatefulStrategyIdentity"}, "strategyRunContext": {"$ref": "#/$defs/StrategyRunContext"}}, "required": ["identity", "statefulStrategyRef", "strategyRunContext"], "unevaluatedProperties": false, "$anchor": "StrategyRun", "$defs": {"StrategyThreadMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/$defs/Step"}}, "$anchor": "StrategyThreadMap", "$comment": "Each thread contains an array of steps, allowing for sequential execution within a thread while supporting parallel execution across multiple threads.", "propertyNames": {"$ref": "#/$defs/StrategyThreadIdentity"}}, "StrategyStateWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"strategyState": {"$ref": "#/$defs/StrategyState"}}, "required": ["strategyState"], "$anchor": "StrategyStateWrapper"}, "StrategyRunIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StrategyRunIdentity", "pattern": "^STRATEGY_RUN-.+$"}, "StatefulStrategyIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StatefulStrategyIdentity", "pattern": "^STATEFUL_STRATEGY-.+$"}, "StrategyRunContext": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"completedAt": {"$ref": "#/$defs/Timestamp"}, "startedAt": {"$ref": "#/$defs/Timestamp"}, "status": {"$ref": "#/$defs/StrategyRunStatus"}}, "required": ["status"], "unevaluatedProperties": false, "$anchor": "StrategyRunContext"}, "Step": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStep"}, {"$ref": "#/$defs/BranchStep"}, {"$ref": "#/$defs/WhileStep"}, {"$ref": "#/$defs/ForStep"}], "unevaluatedProperties": false, "$anchor": "Step"}, "StrategyThreadIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StrategyThreadIdentity", "pattern": "^STRATEGY_THREAD-.+$"}, "StrategyState": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/$defs/ResourceMissing"}, {"$ref": "#/$defs/ResourcePotentialInput"}, {"$ref": "#/$defs/ResourcePotentialOutput"}, {"$ref": "#/$defs/Resource"}]}, "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "$anchor": "StrategyState", "propertyNames": {"$ref": "#/$defs/ExecutionIdentity"}}, "Timestamp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}}, "required": ["timestamp"], "$anchor": "Timestamp"}, "StrategyRunStatus": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StrategyRunStatus", "enum": ["pending", "running", "completed", "failed", "cancelled"]}, "WorkStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"execution": {"$ref": "#/$defs/Execution"}, "identity": {"$ref": "#/$defs/WorkStepIdentity"}, "kind": {"const": "work"}}, "required": ["identity", "kind", "execution"], "$anchor": "WorkStep"}, "BranchStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"cases": {"type": "array", "items": {"$ref": "#/$defs/Conditional"}, "minItems": 1, "uniqueItems": true}, "identity": {"$ref": "#/$defs/BranchStepIdentity"}, "kind": {"const": "branch"}}, "required": ["identity", "kind", "cases"], "$anchor": "BranchStep"}, "WhileStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"case": {"$ref": "#/$defs/Conditional"}, "identity": {"$ref": "#/$defs/WhileStepIdentity"}, "kind": {"const": "while"}}, "required": ["identity", "kind", "case"], "$anchor": "WhileStep"}, "ForStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"case": {"$ref": "#/$defs/Conditional"}, "identity": {"$ref": "#/$defs/ForStepIdentity"}, "kind": {"const": "for"}}, "required": ["identity", "kind", "case"], "$anchor": "ForStep"}, "ResourceMissing": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "missing"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourceMissing"}, "ResourcePotentialInput": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "potential-input"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourcePotentialInput"}, "ResourcePotentialOutput": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "potential-output"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourcePotentialOutput"}, "Resource": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceMetaBase"}, {"properties": {"extractedData": {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonData"}, "$comment": "This will be overlayed at runtime to match the data structure of the underlying type's extractionSchema. At compile time, we guarantee it has an identity property."}}, "required": ["extractedData"]}], "unevaluatedProperties": false, "$anchor": "Resource"}, "ResourceRoleIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceRoleIdentity", "pattern": "^ROLE-.+$"}, "ExecutionIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ExecutionIdentity", "pattern": "^EXECUTION-.+$"}, "StepKind": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"kind": {"type": "string", "enum": ["work", "branch", "while", "for"]}}, "required": ["kind"], "$anchor": "<PERSON><PERSON><PERSON>"}, "Execution": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "This will be overlayed at runtime to specify roleBindings corresponding to the roles of the underlying job.", "$ref": "#/$defs/RoleBindingsWrapper"}], "properties": {"identity": {"$ref": "#/$defs/ExecutionIdentity"}, "jobRef": {"$ref": "#/$defs/JobIdentity"}}, "required": ["identity", "jobRef"], "$anchor": "Execution"}, "WorkStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkStepIdentity", "pattern": "^WORKSTEP-.+$"}, "Conditional": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"what": {"$ref": "#/$defs/WorkStep"}, "when": {"$ref": "#/$defs/WorkStep"}}, "required": ["when", "what"], "unevaluatedProperties": false, "$anchor": "Conditional"}, "BranchStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "BranchStepIdentity", "pattern": "^BRANCHSTEP-.+$"}, "WhileStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WhileStepIdentity", "pattern": "^WHILESTEP-.+$"}, "ForStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ForStepIdentity", "pattern": "^FORSTEP-.+$"}, "ResourceBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"identity": {"$ref": "#/$defs/ResourceIdentity"}, "resourceTypeRef": {"$ref": "#/$defs/ResourceTypeIdentity"}}, "required": ["identity", "resourceTypeRef"], "$anchor": "ResourceBase"}, "ResourceKind": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"kind": {"enum": ["missing", "potential-input", "potential-output", "materialized"]}}, "required": ["kind"], "$anchor": "ResourceKind"}, "CreationContextWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"creationContext": {"$ref": "#/$defs/CreationContext"}}, "required": ["creationContext"], "$anchor": "CreationContextWrapper"}, "ResourceMetaBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "materialized"}}, "required": ["kind"]}, {"$ref": "#/$defs/Timestamp"}, {"$ref": "#/$defs/Path"}], "$anchor": "ResourceMetaBase"}, "JsonData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"$ref": "#/$defs/JsonData"}}, {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonData"}}], "$anchor": "JsonData"}, "RoleBindingsWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roleBindings": {"$ref": "#/$defs/RoleBindings"}}, "required": ["roleB<PERSON>ings"], "$anchor": "RoleBindingsWrapper"}, "JobIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "JobIdentity", "$comment": "", "pattern": "^JOB-.+$"}, "ResourceIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceIdentity", "$comment": "", "pattern": "^RESOURCE-.+$"}, "ResourceTypeIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceTypeIdentity", "pattern": "^TYPE-.+$"}, "CreationContext": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"executionRef": {"$ref": "#/$defs/ExecutionIdentity"}, "resourceRoleRef": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "required": ["resourceRoleRef", "executionRef"], "$anchor": "CreationContext"}, "Path": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "$anchor": "Path"}, "RoleBindings": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}, "outputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}}, "required": ["inputBindingMap", "outputBindingMap"], "unevaluatedProperties": false, "$anchor": "RoleBindings"}, "RoleBindingMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceIdentity"}, "$anchor": "RoleBindingMap", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}}}