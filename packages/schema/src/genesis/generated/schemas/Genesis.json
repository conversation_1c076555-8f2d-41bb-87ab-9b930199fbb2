{"$id": "https://schemas.toolproof.com/v0/Genesis.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "$defs": {"BranchStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"cases": {"type": "array", "items": {"$ref": "#/$defs/Conditional"}, "minItems": 1, "uniqueItems": true}, "identity": {"$ref": "#/$defs/BranchStepIdentity"}, "kind": {"const": "branch"}}, "required": ["identity", "kind", "cases"], "$anchor": "BranchStep"}, "BranchStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "BranchStepIdentity", "pattern": "^BRANCHSTEP-.+$"}, "Conditional": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"what": {"$ref": "#/$defs/WorkStep"}, "when": {"$ref": "#/$defs/WorkStep"}}, "required": ["when", "what"], "unevaluatedProperties": false, "$anchor": "Conditional"}, "CreationContext": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"executionRef": {"$ref": "#/$defs/ExecutionIdentity"}, "resourceRoleRef": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "required": ["resourceRoleRef", "executionRef"], "$anchor": "CreationContext"}, "CreationContextWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"creationContext": {"$ref": "#/$defs/CreationContext"}}, "required": ["creationContext"], "$anchor": "CreationContextWrapper"}, "Described": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Described"}, "Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Named"}, {"$ref": "#/$defs/Described"}], "$anchor": "Documented"}, "Execution": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "This will be overlayed at runtime to specify roleBindings corresponding to the roles of the underlying job.", "$ref": "#/$defs/RoleBindingsWrapper"}], "properties": {"identity": {"$ref": "#/$defs/ExecutionIdentity"}, "jobRef": {"$ref": "#/$defs/JobIdentity"}}, "required": ["identity", "jobRef"], "$anchor": "Execution"}, "ExecutionIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ExecutionIdentity", "pattern": "^EXECUTION-.+$"}, "ExtractionSchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"if": {"anyOf": [{"properties": {"properties": {"type": "object", "properties": {"identity": {}}, "required": ["identity"]}}}, {"properties": {"required": {"type": "array", "contains": {"const": "identity"}}}}]}, "then": {"$ref": "#/$defs/IdentityProp"}}, {"if": {"anyOf": [{"properties": {"properties": {"type": "object", "properties": {"merit": {}}, "required": ["merit"]}}}, {"properties": {"required": {"type": "array", "contains": {"const": "merit"}}}}]}, "then": {"$ref": "#/$defs/MeritProp"}}, {"oneOf": [{"required": ["additionalProperties"]}, {"required": ["unevaluatedProperties"]}]}], "properties": {"$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$defs": {"type": "object"}, "type": {"const": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object"}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "additionalProperties": {"const": false}, "unevaluatedProperties": {"const": false}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}}, "required": ["$schema", "$anchor", "type"], "unevaluatedProperties": false, "$anchor": "ExtractionSchema"}, "ExtractionSchemaWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"extractionSchema": {"$ref": "#/$defs/ExtractionSchema"}}, "required": ["extractionSchema"], "$anchor": "ExtractionSchemaWrapper"}, "ForStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"case": {"$ref": "#/$defs/Conditional"}, "identity": {"$ref": "#/$defs/ForStepIdentity"}, "kind": {"const": "for"}}, "required": ["identity", "kind", "case"], "$anchor": "ForStep"}, "ForStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ForStepIdentity", "pattern": "^FORSTEP-.+$"}, "IdentityProp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$defs": {"type": "object", "additionalProperties": true, "$comment": "Ajv-specific: patternRequired enforces at least one Identity-like key in this $defs block when identity is present.", "minProperties": 1, "patternProperties": {"^[A-Za-z][A-Za-z0-9._-]*Identity$": {"$ref": "#/$defs/IdentitySchema"}}, "patternRequired": ["^[A-Za-z][A-Za-z0-9._-]*Identity$"]}, "properties": {"type": "object", "properties": {"identity": {"$ref": "#/$defs/IdentitySchemaRef"}}, "required": ["identity"]}, "required": {"type": "array", "contains": {"const": "identity"}, "items": {"type": "string"}, "uniqueItems": true}}, "required": ["$defs", "required", "properties"], "$anchor": "IdentityProp"}, "IdentitySchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"if": {"properties": {"type": {"const": "string"}}}, "then": {"properties": {"enum": {"items": {"type": "string"}}}}, "else": {"not": {"anyOf": [{"required": ["pattern"]}, {"required": ["format"]}, {"required": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"required": ["max<PERSON><PERSON><PERSON>"]}]}}}, {"if": {"properties": {"type": {"const": "number"}}}, "then": {"properties": {"enum": {"items": {"type": "number"}}}}}, {"if": {"properties": {"type": {"const": "integer"}}}, "then": {"properties": {"enum": {"items": {"type": "integer"}}}}}, {"if": {"properties": {"type": {"const": "boolean"}}}, "then": {"properties": {"enum": {"items": {"type": "boolean"}}}}}], "properties": {"$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "type": {"enum": ["string", "number", "integer", "boolean"]}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*Identity$"}, "$comment": {"type": "string"}, "enum": {"type": "array", "minItems": 1, "uniqueItems": true}, "format": {"type": "string"}, "maxLength": {"type": "integer", "minimum": 0}, "minLength": {"type": "integer", "minimum": 0}, "pattern": {"type": "string"}}, "required": ["type"], "additionalProperties": false, "$anchor": "IdentitySchema"}, "IdentitySchemaRef": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$ref": {"type": "string", "$comment": "Supports both anchor syntax (#<Name>Identity) and JSON Pointer syntax (#/path/to/definition). Anchors use #<Name>Identity for identity refs. Top-level and nested $defs should expose an $anchor matching <Name>Identity.", "pattern": "^#([A-Za-z][A-Za-z0-9._-]*Identity|/.*)$"}}, "required": ["$ref"], "additionalProperties": false, "$anchor": "IdentitySchemaRef"}, "Job": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/RolesWrapper"}], "properties": {"identity": {"$ref": "#/$defs/JobIdentity"}, "implementationUri": {"type": "string", "format": "uri"}}, "required": ["identity", "implementationUri"], "unevaluatedProperties": false, "$anchor": "Job"}, "JobIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "JobIdentity", "$comment": "", "pattern": "^JOB-.+$"}, "JsonData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"$ref": "#/$defs/JsonData"}}, {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonData"}}], "$anchor": "JsonData"}, "MeritProp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$defs": {"type": "object", "additionalProperties": true, "$comment": "Ajv-specific: patternRequired enforces at least one Merit-like key in this $defs block when merit is present.", "minProperties": 1, "patternProperties": {"^[A-Za-z][A-Za-z0-9._-]*Merit$": {"$ref": "#/$defs/MeritSchema"}}, "patternRequired": ["^[A-Za-z][A-Za-z0-9._-]*Merit$"]}, "properties": {"type": "object", "properties": {"merit": {"$ref": "#/$defs/MeritSchemaRef"}}, "required": ["merit"]}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, "required": ["$defs", "required", "properties"], "$anchor": "MeritProp"}, "MeritSchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"if": {"required": ["type"]}, "then": {}, "else": {"not": {"anyOf": [{"required": ["minimum"]}, {"required": ["maximum"]}, {"required": ["exclusiveMinimum"]}, {"required": ["exclusiveMaximum"]}, {"required": ["multipleOf"]}]}}, "$comment": "Numeric constraints (minimum/maximum/etc) only make sense when using the explicit type form; the enum-only form is allowed for simple enumerations."}], "oneOf": [{"properties": {"type": {"enum": ["number", "integer"]}}, "required": ["type"]}, {"properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}, "required": ["enum"]}], "properties": {"$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "type": {"enum": ["number", "integer"]}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*Merit$"}, "$comment": {"type": "string"}, "enum": {"type": "array", "items": {"type": "number"}, "minItems": 1, "uniqueItems": true}, "exclusiveMaximum": {"type": "number"}, "exclusiveMinimum": {"type": "number"}, "maximum": {"type": "number"}, "minimum": {"type": "number"}, "multipleOf": {"type": "number", "exclusiveMinimum": 0}}, "additionalProperties": false, "$anchor": "MeritSchema"}, "MeritSchemaRef": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$ref": {"type": "string", "$comment": "Supports both anchor syntax (#<Name>Merit) and JSON Pointer syntax (#/path/to/definition). Anchors use #<Name>Merit for merit refs. Top-level and nested $defs should expose an $anchor matching <Name>Merit.", "pattern": "^#([A-Za-z][A-Za-z0-9._-]*Merit|/.*)$"}}, "required": ["$ref"], "additionalProperties": false, "$anchor": "MeritSchemaRef"}, "Named": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Named"}, "Path": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "$anchor": "Path"}, "Resource": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceMetaBase"}, {"properties": {"extractedData": {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonData"}, "$comment": "This will be overlayed at runtime to match the data structure of the underlying type's extractionSchema. At compile time, we guarantee it has an identity property."}}, "required": ["extractedData"]}], "unevaluatedProperties": false, "$anchor": "Resource"}, "ResourceBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"identity": {"$ref": "#/$defs/ResourceIdentity"}, "resourceTypeRef": {"$ref": "#/$defs/ResourceTypeIdentity"}}, "required": ["identity", "resourceTypeRef"], "$anchor": "ResourceBase"}, "ResourceFormat": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Documented"}], "properties": {"identity": {"$ref": "#/$defs/ResourceFormatIdentity"}, "recognizerUri": {"type": "string", "format": "uri"}}, "required": ["identity", "<PERSON><PERSON><PERSON><PERSON>"], "$anchor": "ResourceFormat"}, "ResourceFormatIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceFormatIdentity", "pattern": "^FORMAT-.+$"}, "ResourceIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceIdentity", "$comment": "", "pattern": "^RESOURCE-.+$"}, "ResourceKind": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"kind": {"enum": ["missing", "potential-input", "potential-output", "materialized"]}}, "required": ["kind"], "$anchor": "ResourceKind"}, "ResourceMeta": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "unevaluatedProperties": false, "$anchor": "ResourceMeta", "$ref": "#/$defs/ResourceMetaBase"}, "ResourceMetaBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "materialized"}}, "required": ["kind"]}, {"$ref": "#/$defs/Timestamp"}, {"$ref": "#/$defs/Path"}], "$anchor": "ResourceMetaBase"}, "ResourceMissing": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "missing"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourceMissing"}, "ResourcePotentialInput": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "potential-input"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourcePotentialInput"}, "ResourcePotentialOutput": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "potential-output"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourcePotentialOutput"}, "ResourceRoleIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceRoleIdentity", "pattern": "^ROLE-.+$"}, "ResourceRoleValue": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"properties": {"resourceTypeRef": {"$ref": "#/$defs/ResourceTypeIdentity"}}, "required": ["resourceTypeRef"], "$comment": "A ResourceRole does not have a self-contained identity, as it is always defined in the context of a RoleMap. ResourceRoleValue uses the Value suffix to mean 'the value stored in this map' (RoleMap.additionalProperties)."}, {"$ref": "#/$defs/Documented"}], "$anchor": "ResourceRoleValue"}, "ResourceType": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/ExtractionSchemaWrapper"}, {"if": {"properties": {"resourceFormatRef": {"const": "FORMAT-ApplicationJson"}}, "$comment": "If resourceFormatRef is FORMAT-ApplicationJson, then extractor<PERSON>ri must not be present, but if resourceFormatRef is not FORMAT-ApplicationJson, then extractorUri must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor."}, "then": {"not": {"required": ["extractorUri"]}}, "else": {"type": "object", "properties": {"extractorUri": {"type": "string", "format": "uri"}}, "required": ["extractorUri"]}}], "properties": {"identity": {"$ref": "#/$defs/ResourceTypeIdentity"}, "resourceFormatRef": {"$ref": "#/$defs/ResourceFormatIdentity"}}, "required": ["identity", "resourceFormatRef"], "$anchor": "ResourceType"}, "ResourceTypeIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceTypeIdentity", "pattern": "^TYPE-.+$"}, "RoleBindingMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceIdentity"}, "$anchor": "RoleBindingMap", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "RoleBindings": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}, "outputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}}, "required": ["inputBindingMap", "outputBindingMap"], "unevaluatedProperties": false, "$anchor": "RoleBindings"}, "RoleBindingsWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roleBindings": {"$ref": "#/$defs/RoleBindings"}}, "required": ["roleB<PERSON>ings"], "$anchor": "RoleBindingsWrapper"}, "RoleMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceRoleValue"}, "$anchor": "RoleMap", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "Roles": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputMap": {"$ref": "#/$defs/RoleMap"}, "outputMap": {"$ref": "#/$defs/RoleMap"}}, "required": ["inputMap", "outputMap"], "unevaluatedProperties": false, "$anchor": "Roles"}, "RolesWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roles": {"$ref": "#/$defs/Roles"}}, "required": ["roles"], "$anchor": "RolesWrapper"}, "StatefulStrategy": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StatelessStrategyWrapper"}, {"$ref": "#/$defs/StrategyStateWrapper"}], "properties": {"identity": {"$ref": "#/$defs/StatefulStrategyIdentity"}}, "required": ["identity"], "unevaluatedProperties": false, "$anchor": "StatefulStrategy"}, "StatefulStrategyIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StatefulStrategyIdentity", "pattern": "^STATEFUL_STRATEGY-.+$"}, "StatefulStrategyWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"statefulStrategy": {"$ref": "#/$defs/StatefulStrategy"}}, "required": ["statefulStrategy"], "$anchor": "StatefulStrategyWrapper"}, "StatelessStrategy": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"identity": {"$ref": "#/$defs/StatelessStrategyIdentity"}, "steps": {"type": "array", "items": {"$ref": "#/$defs/Step"}, "uniqueItems": true}}, "required": ["identity", "steps"], "unevaluatedProperties": false, "$anchor": "StatelessStrategy"}, "StatelessStrategyIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StatelessStrategyIdentity", "pattern": "^STATELESS_STRATEGY-.+$"}, "StatelessStrategyWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"statelessStrategy": {"$ref": "#/$defs/StatelessStrategy"}}, "required": ["statelessStrategy"], "$anchor": "StatelessStrategyWrapper"}, "Step": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStep"}, {"$ref": "#/$defs/BranchStep"}, {"$ref": "#/$defs/WhileStep"}, {"$ref": "#/$defs/ForStep"}], "unevaluatedProperties": false, "$anchor": "Step"}, "StepKind": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"kind": {"type": "string", "enum": ["work", "branch", "while", "for"]}}, "required": ["kind"], "$anchor": "<PERSON><PERSON><PERSON>"}, "StrategyRun": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "The Engine will convert the statelessStrategy.steps array of the referenced StatefulStrategy into this map before the run. This is to allow jobs that do not depend on previous-step inputs to run in parallel.The Engine will also inject internal steps to prompt for runtime-provided inputs and handle other orchestration tasks such as repeted steps when encountering a loop.", "$ref": "#/$defs/StrategyThreadMap"}, {"$comment": "Reusing StrategyStateWrapper to include the strategy state in the StrategyRun. The Engine will update it with materialized Resources (i.e. runtime-provided inputs and job-created outputs) during the run.", "$ref": "#/$defs/StrategyStateWrapper"}], "properties": {"identity": {"$ref": "#/$defs/StrategyRunIdentity"}, "statefulStrategyRef": {"$ref": "#/$defs/StatefulStrategyIdentity"}, "strategyRunContext": {"$ref": "#/$defs/StrategyRunContext"}}, "required": ["identity", "statefulStrategyRef", "strategyRunContext"], "unevaluatedProperties": false, "$anchor": "StrategyRun"}, "StrategyRunContext": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"completedAt": {"$ref": "#/$defs/Timestamp"}, "startedAt": {"$ref": "#/$defs/Timestamp"}, "status": {"$ref": "#/$defs/StrategyRunStatus"}}, "required": ["status"], "unevaluatedProperties": false, "$anchor": "StrategyRunContext"}, "StrategyRunIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StrategyRunIdentity", "pattern": "^STRATEGY_RUN-.+$"}, "StrategyRunStatus": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StrategyRunStatus", "enum": ["pending", "running", "completed", "failed", "cancelled"]}, "StrategyState": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/$defs/ResourceMissing"}, {"$ref": "#/$defs/ResourcePotentialInput"}, {"$ref": "#/$defs/ResourcePotentialOutput"}, {"$ref": "#/$defs/Resource"}]}, "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "$anchor": "StrategyState", "propertyNames": {"$ref": "#/$defs/ExecutionIdentity"}}, "StrategyStateWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"strategyState": {"$ref": "#/$defs/StrategyState"}}, "required": ["strategyState"], "$anchor": "StrategyStateWrapper"}, "StrategyThreadIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StrategyThreadIdentity", "pattern": "^STRATEGY_THREAD-.+$"}, "StrategyThreadMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/$defs/Step"}}, "$anchor": "StrategyThreadMap", "$comment": "Each thread contains an array of steps, allowing for sequential execution within a thread while supporting parallel execution across multiple threads.", "propertyNames": {"$ref": "#/$defs/StrategyThreadIdentity"}}, "Timestamp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}}, "required": ["timestamp"], "$anchor": "Timestamp"}, "WhileStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"case": {"$ref": "#/$defs/Conditional"}, "identity": {"$ref": "#/$defs/WhileStepIdentity"}, "kind": {"const": "while"}}, "required": ["identity", "kind", "case"], "$anchor": "WhileStep"}, "WhileStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WhileStepIdentity", "pattern": "^WHILESTEP-.+$"}, "WorkStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"execution": {"$ref": "#/$defs/Execution"}, "identity": {"$ref": "#/$defs/WorkStepIdentity"}, "kind": {"const": "work"}}, "required": ["identity", "kind", "execution"], "$anchor": "WorkStep"}, "WorkStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkStepIdentity", "pattern": "^WORKSTEP-.+$"}}, "$comment": "This schema defines all genesis schemas used throughout the Toolproof ecosystem. The genesis schemas themselves are defined in $defs.<ResourceType>.extractionSchema. The build process (via extractSchemas.js) extracts these schemas and writes them to a separate file (genesis/generated/schemas/Genesis.json). The reason for this indirection is to have all these schema envelopes validate positively against the schema at $defs/ResourceType, effectively making them ResourceTypes, which are first-class citizens in the Toolproof ecosystem."}