{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/ExtractionSchemaWrapper"}, {"if": {"properties": {"resourceFormatRef": {"const": "FORMAT-ApplicationJson"}}, "$comment": "If resourceFormatRef is FORMAT-ApplicationJson, then extractor<PERSON>ri must not be present, but if resourceFormatRef is not FORMAT-ApplicationJson, then extractorUri must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor."}, "then": {"not": {"required": ["extractorUri"]}}, "else": {"type": "object", "properties": {"extractorUri": {"type": "string", "format": "uri"}}, "required": ["extractorUri"]}}], "properties": {"identity": {"$ref": "#/$defs/ResourceTypeIdentity"}, "resourceFormatRef": {"$ref": "#/$defs/ResourceFormatIdentity"}}, "required": ["identity", "resourceFormatRef"], "$anchor": "ResourceType", "$defs": {"Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Named"}, {"$ref": "#/$defs/Described"}], "$anchor": "Documented"}, "ExtractionSchemaWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"extractionSchema": {"$ref": "#/$defs/ExtractionSchema"}}, "required": ["extractionSchema"], "$anchor": "ExtractionSchemaWrapper"}, "ResourceTypeIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceTypeIdentity", "pattern": "^TYPE-.+$"}, "ResourceFormatIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceFormatIdentity", "pattern": "^FORMAT-.+$"}, "Named": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Named"}, "Described": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Described"}, "ExtractionSchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"if": {"anyOf": [{"properties": {"properties": {"type": "object", "properties": {"identity": {}}, "required": ["identity"]}}}, {"properties": {"required": {"type": "array", "contains": {"const": "identity"}}}}]}, "then": {"$ref": "#/$defs/IdentityProp"}}, {"if": {"anyOf": [{"properties": {"properties": {"type": "object", "properties": {"merit": {}}, "required": ["merit"]}}}, {"properties": {"required": {"type": "array", "contains": {"const": "merit"}}}}]}, "then": {"$ref": "#/$defs/MeritProp"}}, {"oneOf": [{"required": ["additionalProperties"]}, {"required": ["unevaluatedProperties"]}]}], "properties": {"$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$defs": {"type": "object"}, "type": {"const": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object"}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "additionalProperties": {"const": false}, "unevaluatedProperties": {"const": false}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}}, "required": ["$schema", "$anchor", "type"], "unevaluatedProperties": false, "$anchor": "ExtractionSchema"}, "IdentityProp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$defs": {"type": "object", "additionalProperties": true, "$comment": "Ajv-specific: patternRequired enforces at least one Identity-like key in this $defs block when identity is present.", "minProperties": 1, "patternProperties": {"^[A-Za-z][A-Za-z0-9._-]*Identity$": {"$ref": "#/$defs/IdentitySchema"}}, "patternRequired": ["^[A-Za-z][A-Za-z0-9._-]*Identity$"]}, "properties": {"type": "object", "properties": {"identity": {"$ref": "#/$defs/IdentitySchemaRef"}}, "required": ["identity"]}, "required": {"type": "array", "contains": {"const": "identity"}, "items": {"type": "string"}, "uniqueItems": true}}, "required": ["$defs", "required", "properties"], "$anchor": "IdentityProp"}, "MeritProp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$defs": {"type": "object", "additionalProperties": true, "$comment": "Ajv-specific: patternRequired enforces at least one Merit-like key in this $defs block when merit is present.", "minProperties": 1, "patternProperties": {"^[A-Za-z][A-Za-z0-9._-]*Merit$": {"$ref": "#/$defs/MeritSchema"}}, "patternRequired": ["^[A-Za-z][A-Za-z0-9._-]*Merit$"]}, "properties": {"type": "object", "properties": {"merit": {"$ref": "#/$defs/MeritSchemaRef"}}, "required": ["merit"]}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, "required": ["$defs", "required", "properties"], "$anchor": "MeritProp"}, "IdentitySchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"if": {"properties": {"type": {"const": "string"}}}, "then": {"properties": {"enum": {"items": {"type": "string"}}}}, "else": {"not": {"anyOf": [{"required": ["pattern"]}, {"required": ["format"]}, {"required": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"required": ["max<PERSON><PERSON><PERSON>"]}]}}}, {"if": {"properties": {"type": {"const": "number"}}}, "then": {"properties": {"enum": {"items": {"type": "number"}}}}}, {"if": {"properties": {"type": {"const": "integer"}}}, "then": {"properties": {"enum": {"items": {"type": "integer"}}}}}, {"if": {"properties": {"type": {"const": "boolean"}}}, "then": {"properties": {"enum": {"items": {"type": "boolean"}}}}}], "properties": {"$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "type": {"enum": ["string", "number", "integer", "boolean"]}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*Identity$"}, "$comment": {"type": "string"}, "enum": {"type": "array", "minItems": 1, "uniqueItems": true}, "format": {"type": "string"}, "maxLength": {"type": "integer", "minimum": 0}, "minLength": {"type": "integer", "minimum": 0}, "pattern": {"type": "string"}}, "required": ["type"], "additionalProperties": false, "$anchor": "IdentitySchema"}, "IdentitySchemaRef": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$ref": {"type": "string", "$comment": "Supports both anchor syntax (#<Name>Identity) and JSON Pointer syntax (#/path/to/definition). Anchors use #<Name>Identity for identity refs. Top-level and nested $defs should expose an $anchor matching <Name>Identity.", "pattern": "^#([A-Za-z][A-Za-z0-9._-]*Identity|/.*)$"}}, "required": ["$ref"], "additionalProperties": false, "$anchor": "IdentitySchemaRef"}, "MeritSchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"if": {"required": ["type"]}, "then": {}, "else": {"not": {"anyOf": [{"required": ["minimum"]}, {"required": ["maximum"]}, {"required": ["exclusiveMinimum"]}, {"required": ["exclusiveMaximum"]}, {"required": ["multipleOf"]}]}}, "$comment": "Numeric constraints (minimum/maximum/etc) only make sense when using the explicit type form; the enum-only form is allowed for simple enumerations."}], "oneOf": [{"properties": {"type": {"enum": ["number", "integer"]}}, "required": ["type"]}, {"properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}, "required": ["enum"]}], "properties": {"$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "type": {"enum": ["number", "integer"]}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*Merit$"}, "$comment": {"type": "string"}, "enum": {"type": "array", "items": {"type": "number"}, "minItems": 1, "uniqueItems": true}, "exclusiveMaximum": {"type": "number"}, "exclusiveMinimum": {"type": "number"}, "maximum": {"type": "number"}, "minimum": {"type": "number"}, "multipleOf": {"type": "number", "exclusiveMinimum": 0}}, "additionalProperties": false, "$anchor": "MeritSchema"}, "MeritSchemaRef": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"$ref": {"type": "string", "$comment": "Supports both anchor syntax (#<Name>Merit) and JSON Pointer syntax (#/path/to/definition). Anchors use #<Name>Merit for merit refs. Top-level and nested $defs should expose an $anchor matching <Name>Merit.", "pattern": "^#([A-Za-z][A-Za-z0-9._-]*Merit|/.*)$"}}, "required": ["$ref"], "additionalProperties": false, "$anchor": "MeritSchemaRef"}}}