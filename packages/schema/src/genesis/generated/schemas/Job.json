{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/RolesWrapper"}], "properties": {"identity": {"$ref": "#/$defs/JobIdentity"}, "implementationUri": {"type": "string", "format": "uri"}}, "required": ["identity", "implementationUri"], "unevaluatedProperties": false, "$anchor": "Job", "$defs": {"Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Named"}, {"$ref": "#/$defs/Described"}], "$anchor": "Documented"}, "RolesWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roles": {"$ref": "#/$defs/Roles"}}, "required": ["roles"], "$anchor": "RolesWrapper"}, "JobIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "JobIdentity", "$comment": "", "pattern": "^JOB-.+$"}, "Named": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Named"}, "Described": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Described"}, "Roles": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputMap": {"$ref": "#/$defs/RoleMap"}, "outputMap": {"allOf": [{"$ref": "#/$defs/RoleMap"}, {"properties": {"ROLE-ErrorOutput": {"type": "object", "allOf": [{"$ref": "#/$defs/ResourceRoleValue"}, {"properties": {"description": {"const": "Represents error outputs from job executions."}, "name": {"const": "ErrorOutput"}, "resourceTypeRef": {"const": "TYPE-Error"}}, "required": ["resourceTypeRef", "name", "description"]}]}}, "required": ["ROLE-ErrorOutput"]}]}}, "required": ["inputMap", "outputMap"], "unevaluatedProperties": false, "$anchor": "Roles"}, "RoleMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceRoleValue"}, "$anchor": "RoleMap", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "ResourceRoleValue": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"properties": {"resourceTypeRef": {"$ref": "#/$defs/ResourceTypeIdentity"}}, "required": ["resourceTypeRef"], "$comment": "A ResourceRole does not have a self-contained identity, as it is always defined in the context of a RoleMap. ResourceRoleValue uses the Value suffix to mean 'the value stored in this map' (RoleMap.additionalProperties)."}, {"$ref": "#/$defs/Documented"}], "$anchor": "ResourceRoleValue"}, "ResourceRoleIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceRoleIdentity", "pattern": "^ROLE-.+$"}, "ResourceTypeIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceTypeIdentity", "pattern": "^TYPE-.+$"}}}