{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Documented"}], "properties": {"identity": {"$ref": "#/$defs/ResourceFormatIdentity"}, "recognizerUri": {"type": "string", "format": "uri"}}, "required": ["identity", "<PERSON><PERSON><PERSON><PERSON>"], "$anchor": "ResourceFormat", "$defs": {"Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Named"}, {"$ref": "#/$defs/Described"}], "$anchor": "Documented"}, "ResourceFormatIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceFormatIdentity", "pattern": "^FORMAT-.+$"}, "Named": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Named"}, "Described": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Described"}}}