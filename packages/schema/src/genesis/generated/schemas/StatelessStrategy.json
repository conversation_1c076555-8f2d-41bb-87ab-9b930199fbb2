{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"identity": {"$ref": "#/$defs/StatelessStrategyIdentity"}, "steps": {"type": "array", "items": {"$ref": "#/$defs/Step"}, "uniqueItems": true}}, "required": ["identity", "steps"], "unevaluatedProperties": false, "$anchor": "StatelessStrategy", "$defs": {"StatelessStrategyIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StatelessStrategyIdentity", "pattern": "^STATELESS_STRATEGY-.+$"}, "Step": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStep"}, {"$ref": "#/$defs/BranchStep"}, {"$ref": "#/$defs/WhileStep"}, {"$ref": "#/$defs/ForStep"}], "unevaluatedProperties": false, "$anchor": "Step"}, "WorkStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"execution": {"$ref": "#/$defs/Execution"}, "identity": {"$ref": "#/$defs/WorkStepIdentity"}, "kind": {"const": "work"}}, "required": ["identity", "kind", "execution"], "$anchor": "WorkStep"}, "BranchStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"cases": {"type": "array", "items": {"$ref": "#/$defs/Conditional"}, "minItems": 1, "uniqueItems": true}, "identity": {"$ref": "#/$defs/BranchStepIdentity"}, "kind": {"const": "branch"}}, "required": ["identity", "kind", "cases"], "$anchor": "BranchStep"}, "WhileStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"case": {"$ref": "#/$defs/Conditional"}, "identity": {"$ref": "#/$defs/WhileStepIdentity"}, "kind": {"const": "while"}}, "required": ["identity", "kind", "case"], "$anchor": "WhileStep"}, "ForStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"case": {"$ref": "#/$defs/Conditional"}, "identity": {"$ref": "#/$defs/ForStepIdentity"}, "kind": {"const": "for"}}, "required": ["identity", "kind", "case"], "$anchor": "ForStep"}, "StepKind": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"kind": {"type": "string", "enum": ["work", "branch", "while", "for"]}}, "required": ["kind"], "$anchor": "<PERSON><PERSON><PERSON>"}, "Execution": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "This will be overlayed at runtime to specify roleBindings corresponding to the roles of the underlying job.", "$ref": "#/$defs/RoleBindingsWrapper"}], "properties": {"identity": {"$ref": "#/$defs/ExecutionIdentity"}, "jobRef": {"$ref": "#/$defs/JobIdentity"}}, "required": ["identity", "jobRef"], "$anchor": "Execution"}, "WorkStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkStepIdentity", "pattern": "^WORKSTEP-.+$"}, "Conditional": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"what": {"$ref": "#/$defs/WorkStep"}, "when": {"$ref": "#/$defs/WorkStep"}}, "required": ["when", "what"], "unevaluatedProperties": false, "$anchor": "Conditional"}, "BranchStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "BranchStepIdentity", "pattern": "^BRANCHSTEP-.+$"}, "WhileStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WhileStepIdentity", "pattern": "^WHILESTEP-.+$"}, "ForStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ForStepIdentity", "pattern": "^FORSTEP-.+$"}, "RoleBindingsWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roleBindings": {"$ref": "#/$defs/RoleBindings"}}, "required": ["roleB<PERSON>ings"], "$anchor": "RoleBindingsWrapper"}, "ExecutionIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ExecutionIdentity", "pattern": "^EXECUTION-.+$"}, "JobIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "JobIdentity", "$comment": "", "pattern": "^JOB-.+$"}, "RoleBindings": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}, "outputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}}, "required": ["inputBindingMap", "outputBindingMap"], "unevaluatedProperties": false, "$anchor": "RoleBindings"}, "RoleBindingMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceIdentity"}, "$anchor": "RoleBindingMap", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "ResourceIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceIdentity", "$comment": "", "pattern": "^RESOURCE-.+$"}, "ResourceRoleIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceRoleIdentity", "pattern": "^ROLE-.+$"}}}