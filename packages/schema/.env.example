# Schema Generation Configuration
# All environment variables are OPTIONAL - defaults are provided for standard project structure
# Override these only if your project structure differs from the defaults

# Root directory of the schema package
# Default: process.cwd() (current working directory)
# TP_SCHEMA_ROOT=C:\Users\<USER>\projects\toolproof\core\packages\schema

# Directory containing the source Genesis.json file (relative to TP_SCHEMA_ROOT or absolute)
# Default: src/genesis
# TP_SCHEMA_SOURCE_DIR=src/genesis

# Name of the source schema file
# Default: Genesis.json
# TP_SCHEMA_SOURCE_FILE=Genesis.json

# Directory where generated schemas should be written (relative to TP_SCHEMA_ROOT or absolute)
# Generated schemas include flattened Genesis.json and extracted subschemas like Job.json
# Default: src/genesis/generated/schemas
# TP_SCHEMA_OUTPUT_DIR=src/genesis/generated/schemas

# Directory for generated TypeScript type definitions in source (relative to TP_SCHEMA_ROOT or absolute)
# Default: src/_lib/types
# TP_SCHEMA_TYPES_SRC_DIR=src/_lib/types

# Directory for generated TypeScript type definitions in distribution (relative to TP_SCHEMA_ROOT or absolute)
# Default: dist/_lib/types
# TP_SCHEMA_TYPES_DIST_DIR=dist/_lib/types

# Base URL for schema identifiers (used in $id fields)
# Default: https://schemas.toolproof.com
# TP_SCHEMA_BASE_URL=https://schemas.toolproof.com

# Schema version identifier (used in $id fields and URL resolution)
# Default: v0
# TP_SCHEMA_VERSION=v0
