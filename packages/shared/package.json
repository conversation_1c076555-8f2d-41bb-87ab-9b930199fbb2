{"name": "@toolproof-npm/shared", "version": "0.1.76", "description": "Core library utilities for ToolProof", "keywords": ["toolproof", "utilities", "library"], "author": "ToolProof Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ToolProof/core.git", "directory": "packages/shared"}, "homepage": "https://github.com/ToolProof/core#readme", "bugs": {"url": "https://github.com/ToolProof/core/issues"}, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./constants": {"import": "./dist/constants.js", "types": "./dist/constants.d.ts"}, "./types": {"import": "./dist/types.js", "types": "./dist/types.d.ts"}, "./utils": {"import": "./dist/utils.js", "types": "./dist/utils.d.ts"}, "./server": {"node": "./dist/firebaseAdminHelpers.js", "types": "./dist/firebaseAdminHelpers.d.ts"}}, "scripts": {"build": "tsc -b"}, "files": ["dist", "README.md"], "devDependencies": {"@google-cloud/storage": "^7.17.3", "@types/node": "^20.19.25", "json-schema-to-typescript": "^15.0.4", "ts-node": "^10.9.2", "typescript": "^5.9.3"}, "dependencies": {"@toolproof-npm/schema": "^0.1.65", "firebase-admin": "^13.6.0"}}