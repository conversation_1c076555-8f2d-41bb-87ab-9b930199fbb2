import type {
	Execution<PERSON><PERSON><PERSON><PERSON><PERSON>,
	ResourceIdentity<PERSON>son,
	ResourceJson,
	ResourceRoleIdentityJson,
	ResourceRoleValue<PERSON>son,
	ResourceTypeIdentityJson,
} from '@toolproof-npm/schema';
import { CONSTANTS } from '../constants.js';

export type BucketConst = typeof CONSTANTS.STORAGE.BUCKETS.tp_resources;

export type CollectionConst = keyof typeof CONSTANTS.STORAGE.COLLECTIONS;

export type TerminalConst = keyof typeof CONSTANTS.TERMINALS;

export type StepConst = keyof typeof CONSTANTS.STEPS;

export type Role = { identity: ResourceRoleIdentityJson } & ResourceRoleValueJson;

export type ResourceMap = Record<ResourceTypeIdentityJson, ResourceJson[]>;