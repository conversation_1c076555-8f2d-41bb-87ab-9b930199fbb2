import type { ResourceType<PERSON>dent<PERSON><PERSON><PERSON>, Resource<PERSON>son, ResourcePotentialOutput<PERSON>son, JsonDataJson } from '@toolproof-npm/schema';
import { createHash } from 'crypto';


/**
 * Generates SHA-256 hash of content
 * @param content The content to hash
 * @returns The SHA-256 hash as hex string
 */
export function generateContentHash(content: string): string {
    return createHash('sha256').update(content).digest('hex');
}

/**
 * Generates a content-addressed path for a resource
 * @param resourceTypeRef The resource type reference
 * @param content The content to hash
 * @returns The path in format: {resourceTypeRef}/{contentHash}
 */
export function generateContentAddressedPath(resourceTypeRef: ResourceTypeIdentityJson, content: string): string {
    const contentHash = generateContentHash(content);
    return `${resourceTypeRef}/${contentHash}`;
}


/**
 * Creates a materialized resource from a potential-output resource and content.
 * Generates a content-addressed path from the content.
 * 
 * @param potentialOutput The potential-output resource to materialize
 * @param content The actual content/data (assigned directly to extractedData)
 * @param timestamp Optional timestamp to preserve (e.g., when copying resources). If not provided, generates a new timestamp.
 * @returns A fully materialized ResourceJson
 * 
 * @example
 * ```typescript
 * // Materialize from potential output
 * const materialized = createMaterializedResource(potentialOutput, { identity: 42 });
 * 
 * // Preserve original timestamp when copying
 * const copy = createMaterializedResource(potentialOutput, { identity: 42 }, originalTimestamp);
 * ```
 */
export function createMaterializedResource(
    potentialOutput: ResourcePotentialOutputJson,
    content: JsonDataJson,
    timestamp?: string
): ResourceJson {
    const { identity, resourceTypeRef, creationContext } = potentialOutput;

    // Generate content-addressed path from the content (use pretty-printed format)
    const contentString = JSON.stringify(content, null, 2);
    const path = generateContentAddressedPath(resourceTypeRef, contentString);

    return {
        identity,
        resourceTypeRef,
        creationContext,
        kind: 'materialized',
        path,
        timestamp: timestamp ?? new Date().toISOString(),
        extractedData: content,
    };
}