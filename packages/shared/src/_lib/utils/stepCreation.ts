
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Execution<PERSON><PERSON>, ResourceIdentity<PERSON><PERSON>, WorkStepIdentity<PERSON>son, BranchStepIdentity<PERSON>son, WhileStepIdentity<PERSON>son, ForStepIdentityJson, ExecutionIdentity<PERSON>son, ResourceRoleIdentity<PERSON>son, RoleBindingMap<PERSON>son, Job<PERSON>son } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { getNewIdentity } from '@toolproof-npm/shared/server';


// DOC: Helper function to construct an Execution object from a job
const createExecutionFromJob = async (job: JobJson, executionRef?: ExecutionIdentityJson): Promise<ExecutionJson> => {
    const execIdentity = executionRef ?? await getNewIdentity(CONSTANTS.TERMINALS.execution) as ExecutionIdentityJson;

    const inputBindingMap: RoleBindingMapJson = {};
    const inputs = job.roles.inputMap;
    await Promise.all(
        Object.keys(inputs).map(async (resourceRoleRef) => {
            const resourceId = await getNewIdentity(CONSTANTS.TERMINALS.resource) as ResourceIdentityJson;
            inputBindingMap[resourceRoleRef as ResourceRoleIdentityJson] = resourceId;
        })
    );

    const outputBindingMap: RoleBindingMapJson = {};
    const outputs = job.roles.outputMap;
    await Promise.all(
        Object.keys(outputs).map(async (resourceRoleRef) => {
            const resourceId = await getNewIdentity(CONSTANTS.TERMINALS.resource) as ResourceIdentityJson;
            outputBindingMap[resourceRoleRef as ResourceRoleIdentityJson] = resourceId;
        })
    );

    return {
        identity: execIdentity,
        jobRef: job.identity,
        roleBindings: {
            inputBindingMap,
            outputBindingMap
        }
    } as ExecutionJson;
};

// DOC: Helper function to construct a WorkStep object from an jobMetaJson (does not append to statelessStrategy.steps)
export const createWorkStepFromJob = async (job: JobJson): Promise<WorkStepJson> => {
    const workStepIdentity = await getNewIdentity(CONSTANTS.STEPS.work) as WorkStepIdentityJson;
    const executionRef = workStepIdentity.replace(CONSTANTS.STEPS.work.toUpperCase(), 'execution'.toUpperCase()) as ExecutionIdentityJson; // ATTENTION: use function

    const execution = await createExecutionFromJob(job, executionRef);

    const newWorkStep: WorkStepJson = {
        identity: workStepIdentity,
        kind: CONSTANTS.STEPS.work,
        execution
    };

    return newWorkStep;

};

// DOC: Helper function to construct a ForStep or WhileStep from a job
export const createLoopStepFromJob = async (whatJob: JobJson, whenJob: JobJson, kind: 'for' | 'while'): Promise<ForStepJson | WhileStepJson> => {
    // Create the "what" WorkStep from the provided job
    const whatWorkStep = await createWorkStepFromJob(whatJob);

    // Create the "when" WorkStep from the LessThan job
    const whenWorkStep = await createWorkStepFromJob(whenJob);

    // Generate loop step identity based on kind
    const stepIdentity = (kind === 'for'
        ? await getNewIdentity(CONSTANTS.STEPS.for) as ForStepIdentityJson
        : await getNewIdentity(CONSTANTS.STEPS.while) as WhileStepIdentityJson);

    // Assemble the loop step
    return {
        identity: stepIdentity,
        kind,
        case: {
            what: whatWorkStep,
            when: whenWorkStep
        }
    } as ForStepJson | WhileStepJson;
};


// DOC: Helper function to construct a BranchStep from an array of job pairs
export const createBranchStepFromJobPairs = async (cases: { whatJob: JobJson, whenJob: JobJson }[]): Promise<BranchStepJson> => {
    // Generate branch step identity
    const branchStepIdentity = await getNewIdentity(CONSTANTS.STEPS.branch) as BranchStepIdentityJson;

    // Create WorkSteps for each case (what and when pair)
    const casesWithWorkSteps = await Promise.all(
        cases.map(async ({ whatJob, whenJob }) => {
            const whatWorkStep = await createWorkStepFromJob(whatJob);
            const whenWorkStep = await createWorkStepFromJob(whenJob);
            return {
                what: whatWorkStep,
                when: whenWorkStep
            };
        })
    );

    // Assemble the branch step
    return {
        identity: branchStepIdentity,
        kind: CONSTANTS.STEPS.branch,
        cases: casesWithWorkSteps
    } as BranchStepJson;
};


// DOC: Helper function to clone a ForStep with new identities for steps and executions, preserving job references and bindings
export const cloneForStep = async (forStep: ForStepJson): Promise<ForStepJson> => {
    // Generate new ForStep identity
    const newForStepIdentity = await getNewIdentity(CONSTANTS.STEPS.for) as ForStepIdentityJson;

    // Clone the "what" WorkStep
    const originalWhatWorkStep = forStep.case.what;
    const newWhatWorkStepIdentity = await getNewIdentity(CONSTANTS.STEPS.work) as WorkStepIdentityJson;
    const newWhatExecutionIdentity = newWhatWorkStepIdentity.replace(
        CONSTANTS.STEPS.work.toUpperCase(),
        'execution'.toUpperCase()
    ) as ExecutionIdentityJson;

    const newWhatExecution: ExecutionJson = {
        identity: newWhatExecutionIdentity,
        jobRef: originalWhatWorkStep.execution.jobRef,
        roleBindings: {
            inputBindingMap: { ...originalWhatWorkStep.execution.roleBindings.inputBindingMap },
            outputBindingMap: { ...originalWhatWorkStep.execution.roleBindings.outputBindingMap }
        }
    };

    const newWhatWorkStep: WorkStepJson = {
        identity: newWhatWorkStepIdentity,
        kind: CONSTANTS.STEPS.work,
        execution: newWhatExecution
    };

    // Clone the "when" WorkStep
    const originalWhenWorkStep = forStep.case.when;
    const newWhenWorkStepIdentity = await getNewIdentity(CONSTANTS.STEPS.work) as WorkStepIdentityJson;
    const newWhenExecutionIdentity = newWhenWorkStepIdentity.replace(
        CONSTANTS.STEPS.work.toUpperCase(),
        'execution'.toUpperCase()
    ) as ExecutionIdentityJson;

    const newWhenExecution: ExecutionJson = {
        identity: newWhenExecutionIdentity,
        jobRef: originalWhenWorkStep.execution.jobRef,
        roleBindings: {
            inputBindingMap: { ...originalWhenWorkStep.execution.roleBindings.inputBindingMap },
            outputBindingMap: { ...originalWhenWorkStep.execution.roleBindings.outputBindingMap }
        }
    };

    const newWhenWorkStep: WorkStepJson = {
        identity: newWhenWorkStepIdentity,
        kind: CONSTANTS.STEPS.work,
        execution: newWhenExecution
    };

    // Assemble the cloned ForStep
    return {
        identity: newForStepIdentity,
        kind: CONSTANTS.STEPS.for,
        case: {
            what: newWhatWorkStep,
            when: newWhenWorkStep
        }
    } as ForStepJson;
};