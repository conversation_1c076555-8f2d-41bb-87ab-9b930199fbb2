import type {
  <PERSON><PERSON>ontext<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResourceMissing<PERSON>son,
  ResourcePotentialInputJson,
  ResourcePotentialOutputJson,
  StrategyStateJson,
} from '@toolproof-npm/schema';

export type ResolveResult =
  | { status: 'materialized'; entry: ResourceJson; path: Creation<PERSON>ontextJson[] }
  | { status: 'missing'; entry: ResourceMissingJson; path: CreationContextJson[] }
  | { status: 'potential-output'; entry: ResourcePotentialOutputJson; path: CreationContextJson[] }
  | { status: 'unresolved'; reason: 'not-found' | 'cycle' | 'depth-exceeded'; path: CreationContextJson[] };

export function resolveResourceChain(
  strategyState: StrategyStateJson,
  start: CreationContextJson,
  opts?: { maxDepth?: number }
): ResolveResult {
  const maxDepth = opts?.maxDepth ?? 50;
  const visited = new Set<string>();
  const path: CreationContextJson[] = [];
  let current: CreationContextJson = start;

  for (let depth = 0; depth <= maxDepth; depth++) {
    path.push(current);
    const visitKey = `${current.executionRef}::${current.resourceRoleRef}`;
    if (visited.has(visitKey)) {
      return { status: 'unresolved', reason: 'cycle', path };
    }
    visited.add(visitKey);

    const bucket = strategyState[current.executionRef];
    if (!bucket) return { status: 'unresolved', reason: 'not-found', path };
    const entry = bucket[current.resourceRoleRef] as (
      | (ResourceJson & { kind: 'materialized' })
      | (ResourceMissingJson & { kind: 'missing' })
      | (ResourcePotentialInputJson & { kind: 'potential-input' })
      | (ResourcePotentialOutputJson & { kind: 'potential-output' })
      | undefined
    );
    if (!entry) return { status: 'unresolved', reason: 'not-found', path };

    if (entry.kind === 'materialized') {
      return { status: 'materialized', entry: entry as ResourceJson, path };
    }
    if (entry.kind === 'missing') {
      return { status: 'missing', entry: entry as ResourceMissingJson, path };
    }
    if (entry.kind === 'potential-output') {
      return { status: 'potential-output', entry: entry as ResourcePotentialOutputJson, path };
    }

    // potential-input: follow ref backwards
    if (entry.kind === 'potential-input') {
      const rpi = entry.creationContext as CreationContextJson | undefined;
      if (!rpi) return { status: 'unresolved', reason: 'not-found', path };
      current = rpi;
      continue;
    }

    // Unknown case
    return { status: 'unresolved', reason: 'not-found', path };
  }

  return { status: 'unresolved', reason: 'depth-exceeded', path };
}
