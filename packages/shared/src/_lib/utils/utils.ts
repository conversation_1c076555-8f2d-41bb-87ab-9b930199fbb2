import type { ResourceIdentityJson, ResourceTypeIdentity<PERSON>son, ResourceJson, ResourceMissingJson, Resource_JobJson, JobIdentityJson, JobJson, ResourcePotentialInputJson, ResourcePotentialOutputJson, StrategyStateJson, CreationContextJson } from '@toolproof-npm/schema';
import type { ResourceMap } from '../types.js';


export function extractResourcesByType(
    resourceMap: ResourceMap,
    resourceTypeRef: ResourceTypeIdentityJson
): Record<ResourceIdentityJson, ResourceJson> {
    const resources = resourceMap[resourceTypeRef] ?? [];
    const result: Record<ResourceIdentityJson, ResourceJson> = {};

    for (const resource of resources) {
        result[resource.identity] = resource;
    }

    return result;
}


export function extractJobMap(resourceMap: ResourceMap) {
    const resourceJobMap = extractResourcesByType(resourceMap, 'TYPE-Job') as Record<ResourceIdentityJson, Resource_JobJson>;
    const jobMap = new Map<JobIdentityJson, JobJson>();
    Object.values(resourceJobMap).forEach((resource) => {
        if (resource.extractedData?.identity) {
            jobMap.set(resource.extractedData.identity as JobIdentityJson, resource.extractedData as JobJson); // ATTENTION: why do we need type assertion here?
        }
    });
    return jobMap;
}


export type ResolveResult =
    | { status: 'materialized'; entry: ResourceJson; path: CreationContextJson[] }
    | { status: 'missing'; entry: ResourceMissingJson; path: CreationContextJson[] }
    | { status: 'blocked-output'; entry: ResourcePotentialOutputJson; path: CreationContextJson[] }
    | { status: 'unresolved'; reason: 'missing-entry' | 'cycle' | 'depth-exceeded'; path: CreationContextJson[] };

export function resolveResourceChain(
    strategyState: StrategyStateJson,
    start: CreationContextJson,
    opts?: { maxDepth?: number }
): ResolveResult {
    const maxDepth = opts?.maxDepth ?? 50;
    const visited = new Set<string>();
    const path: CreationContextJson[] = [];
    let current: CreationContextJson = start;

    for (let depth = 0; depth <= maxDepth; depth++) {
        path.push(current);
        const visitKey = `${current.executionRef}::${current.resourceRoleRef}`;
        if (visited.has(visitKey)) {
            return { status: 'unresolved', reason: 'cycle', path };
        }
        visited.add(visitKey);

        const bucket = strategyState[current.executionRef];
        if (!bucket) return { status: 'unresolved', reason: 'missing-entry', path };
        const entry = bucket[current.resourceRoleRef] as (
            | (ResourceJson & { kind: 'materialized' })
            | (ResourceMissingJson & { kind: 'missing' })
            | (ResourcePotentialInputJson & { kind: 'potential-input' })
            | (ResourcePotentialOutputJson & { kind: 'potential-output' })
            | undefined
        );
        if (!entry) return { status: 'unresolved', reason: 'missing-entry', path };

        if (entry.kind === 'materialized') {
            return { status: 'materialized', entry: entry as ResourceJson, path };
        }
        if (entry.kind === 'missing') {
            return { status: 'missing', entry: entry as ResourceMissingJson, path };
        }
        if (entry.kind === 'potential-output') {
            return { status: 'blocked-output', entry: entry as ResourcePotentialOutputJson, path };
        }
        // potential-input: follow ref backwards
        if (entry.kind === 'potential-input') {
            const rpi = entry.creationContext as CreationContextJson | undefined;
            if (!rpi) return { status: 'unresolved', reason: 'missing-entry', path };
            current = rpi;
            continue;
        }

        // Unknown case
        return { status: 'unresolved', reason: 'missing-entry', path };
    }
    return { status: 'unresolved', reason: 'depth-exceeded', path };
}

