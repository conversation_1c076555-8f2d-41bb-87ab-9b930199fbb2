import type {
	<PERSON><PERSON><PERSON>,
	ResourceMissing<PERSON>son,
	ResourcePotentialInputJson,
	ResourcePotentialOutputJson,
	StatefulStrategyJson,
	CreationContextJson,
} from '@toolproof-npm/schema';
import { resolveResourceChain } from './resourceChain.js';

export function bindInputRes(
	statefulStrategy: StatefulStrategyJson,
	target: CreationContextJson,
	resource: ResourceJson
): StatefulStrategyJson {
	const strategyState = statefulStrategy.strategyState;
	const bucket = strategyState[target.executionRef] ?? {};

	return {
		...statefulStrategy,
		strategyState: {
			...strategyState,
			[target.executionRef]: {
				...bucket,
				[target.resourceRoleRef]: resource,
			},
		},
	};
}

export function bindInputRef(
	statefulStrategy: StatefulStrategyJson,
	target: CreationContextJson,
	source: CreationContextJson
): StatefulStrategyJson {
	const strategyState = statefulStrategy.strategyState;
	const sourceEntry = strategyState?.[source.executionRef]?.[source.resourceRoleRef] as (
		| ResourceJson
		| ResourceMissingJson
		| ResourcePotentialInputJson
		| ResourcePotentialOutputJson
		| undefined
	);
	if (!sourceEntry) throw new Error(`resourceEntry not found for source (${source.executionRef}, ${source.resourceRoleRef})`);

	const bucket = strategyState[target.executionRef] ?? {};
	const result = resolveResourceChain(strategyState, { executionRef: source.executionRef, resourceRoleRef: source.resourceRoleRef });

	if (result.status === 'materialized') {
		return {
			...statefulStrategy,
			strategyState: {
				...strategyState,
				[target.executionRef]: {
					...bucket,
					[target.resourceRoleRef]: result.entry,
				},
			},
		};
	}

	if (result.status === 'missing') {
		const missing = result.entry;
		const reusedMissing: ResourceMissingJson = {
			identity: missing.identity,
			resourceTypeRef: missing.resourceTypeRef,
			kind: 'missing',
		};

		return {
			...statefulStrategy,
			strategyState: {
				...strategyState,
				[target.executionRef]: {
					...bucket,
					[target.resourceRoleRef]: reusedMissing,
				},
			},
		};
	}

	if (result.status === 'potential-output') {
		const potentialInput: ResourcePotentialInputJson = {
			identity: sourceEntry.identity,
			resourceTypeRef: sourceEntry.resourceTypeRef,
			creationContext: { executionRef: source.executionRef, resourceRoleRef: source.resourceRoleRef },
			kind: 'potential-input',
		};

		return {
			...statefulStrategy,
			strategyState: {
				...strategyState,
				[target.executionRef]: {
					...bucket,
					[target.resourceRoleRef]: potentialInput,
				},
			},
		};
	}

	throw new Error(`Failed to resolve resource chain: ${result.reason}`);
}

export function clearInputBinding(
	statefulStrategy: StatefulStrategyJson,
	target: CreationContextJson
): StatefulStrategyJson {
	const strategyState = statefulStrategy.strategyState;
	const bucket = strategyState?.[target.executionRef];
	if (!bucket?.[target.resourceRoleRef]) return statefulStrategy;

	const nextBucket = { ...bucket } as Record<string, unknown>;
	delete nextBucket[target.resourceRoleRef];

	return {
		...statefulStrategy,
		strategyState: {
			...strategyState,
			[target.executionRef]: nextBucket as any,
		},
	};
}