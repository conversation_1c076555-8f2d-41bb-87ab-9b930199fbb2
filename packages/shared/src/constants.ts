
export const CONSTANTS = {
    SCHEMA: { // ATTENTION: should be generated from GenesisSchema.$defs
        ResourceFormat: 'ResourceFormat',
        ResourceType: 'ResourceType',
        Execution: 'Execution',
        StrategyState: 'StrategyState',
    },
    STORAGE: {
        BUCKETS: {
            tp_resources: 'tp-resources',
        },
        COLLECTIONS: {
            resources: 'resources',
            members: 'members',
        },
    },
    TERMINALS: {
        format: 'format' as const,
        type: 'type' as const,
        role: 'role' as const,
        job: 'job' as const,
        execution: 'executions' as const,
        resource: 'resource' as const,
        stateless_strategy: 'stateless_strategy' as const,
        stateful_strategy: 'stateful_strategy' as const,
        strategy_thread: 'strategy_thread' as const,
        strategy_run: 'strategy_run' as const,
    },
    STEPS: {
        work: 'work',
        branch: 'branch',
        while: 'while',
        for: 'for',
    },
    ENGINE: {
        GraphRunStrategy: 'GraphRunStrategy',
    },
    SPECIALS: {
        FORMAT_ApplicationJson: 'FORMAT-ApplicationJson',
        FORMAT_ApplicationJob: 'FORMAT-ApplicationJob',
        TYPE_Boolean: 'TYPE-Boolean',
        TYPE_Natural: 'TYPE-Natural',
        TYPE_ResourceFormat: 'TYPE-ResourceFormat',
        TYPE_ResourceType: 'TYPE-ResourceType',
        TYPE_Job: 'TYPE-Job',
        TYPE_StatelessStrategy: 'TYPE-StatelessStrategy',
        TYPE_StatefulStrategy: 'TYPE-StatefulStrategy',
        ROLE_Manual: 'ROLE-Manual',
        JOB_Engine: 'JOB-Engine',
        BOOLEAN_false: 'RESOURCE-vPFDZA2rMIUoWAu6oClO',
        BOOLEAN_true: 'RESOURCE-Om4l1XGTAddJdaJjYzuQ',
    },
    TESTING: {
        Natural_Zero: 'TYPE-Natural/3335e31095a13a9a2b0ea41ca7d92a458780cd5671dc0a440a72cc1b1c4f2c81',
    }
} as const;