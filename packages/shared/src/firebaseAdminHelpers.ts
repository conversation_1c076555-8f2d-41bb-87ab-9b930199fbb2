import type { ResourceTypeIdentityJson, ResourceJson } from '@toolproof-npm/schema';
import type { StepConst, ResourceMap, TerminalConst } from './_lib/types.js';
import { CONSTANTS } from './constants.js';
import { dbAdmin, storageAdmin } from "./firebaseAdminInit.js";


export function getNewIdentity(identifiable: TerminalConst | StepConst) {
    const base = identifiable.toUpperCase();
    const normalized = base.endsWith('S') ? base.slice(0, -1) : base;
    const prefix = normalized + '-';
    const docRef = dbAdmin.collection(CONSTANTS.STORAGE.COLLECTIONS.resources).doc(identifiable).collection(CONSTANTS.STORAGE.COLLECTIONS.members).doc();
    return prefix + docRef.id;
}

export async function listResources( // ATTENTION: must clean up
    resourceTypeRefs: ResourceTypeIdentityJson[]
): Promise<ResourceMap> {
    const bucket = storageAdmin.bucket(CONSTANTS.STORAGE.BUCKETS.tp_resources);

    async function fetchFilesUnder(resourceTypeRef: string): Promise<Array<{ data: unknown; meta: any; name: string }>> {
        const prefix = `${resourceTypeRef}/`;
        const [found] = await bucket.getFiles({ prefix });
        const files = found.filter(f => {
            const name = f.name || '';
            if (!name || name.endsWith('/')) return false;
            return true;
        });
        if (!files.length) return [];
        const items = await Promise.all(files.map(async (file) => {
            try {
                const [buf] = await file.download();
                const meta = file.metadata || (await file.getMetadata())[0];
                const data = JSON.parse(buf.toString('utf8')) as unknown;
                return { data, meta, name: file.name };
            } catch {
                return null as unknown as { data: unknown; meta: any; name: string };
            }
        }));
        return items.filter(Boolean) as Array<{ data: unknown; meta: any; name: string }>;
    }

    const entries = await Promise.all(
        resourceTypeRefs.map(async (resourceTypeRef) => {
            const rows = await fetchFilesUnder(resourceTypeRef);
            const items: ResourceJson[] = rows.map(({ data, meta, name }) => {
                const flat = meta?.metadata ?? {};
                // Reconstruct nested object from flattened keys (dot and array index notation)
                const root: any = {};
                for (const [k, vRaw] of Object.entries(flat)) {
                    if (typeof vRaw !== 'string') continue; // GCS should store only strings
                    const vStr = vRaw.trim();
                    // Attempt JSON parse for non-simple primitives
                    let value: any = vStr;
                    if ((vStr.startsWith('{') && vStr.endsWith('}')) || (vStr.startsWith('[') && vStr.endsWith(']'))) {
                        try { value = JSON.parse(vStr); } catch { value = vStr; }
                    }
                    // Split by '.' while preserving array indices
                    const segments = k.split('.');
                    let cursor = root;
                    for (let i = 0; i < segments.length; i++) {
                        const seg = segments[i];
                        const arrIdxMatch = seg.match(/^(.*)\[(\d+)\]$/);
                        if (arrIdxMatch) {
                            const base = arrIdxMatch[1];
                            const idx = parseInt(arrIdxMatch[2], 10);
                            if (!cursor[base]) cursor[base] = [];
                            if (!Array.isArray(cursor[base])) cursor[base] = [];
                            while (cursor[base].length <= idx) cursor[base].push(undefined);
                            if (i === segments.length - 1) {
                                cursor[base][idx] = value;
                            } else {
                                if (!cursor[base][idx]) cursor[base][idx] = {};
                                cursor = cursor[base][idx];
                            }
                        } else {
                            if (i === segments.length - 1) {
                                cursor[seg] = value;
                            } else {
                                if (!cursor[seg] || typeof cursor[seg] !== 'object') cursor[seg] = {};
                                cursor = cursor[seg];
                            }
                        }
                    }
                }
                const identity = root.identity;
                // const resourceTypeRef = root.resourceTypeRef; // we already have this
                const resourceRoleRef = root.creationContext.resourceRoleRef;
                const executionRef = root.creationContext.executionRef;
                const kind = root.kind;
                const path = root.path;
                const timestamp = root.timestamp;

                const missing = [
                    ['identity', identity],
                    ['resourceRoleRef', resourceRoleRef],
                    ['executionRef', executionRef],
                    ['kind', kind],
                    ['timestamp', timestamp],
                    ['path', path],
                ].filter(([_, v]) => typeof v !== 'string' || (v as string).length === 0) as Array<[string, string | undefined]>;

                if (missing.length) {
                    const keys = missing.map(([k]) => k).join(', ');
                    throw new Error(`Missing required metadata keys [${keys}] for resource file: ${name}`);
                }

                return {
                    identity,
                    resourceTypeRef,
                    creationContext: {
                        resourceRoleRef,
                        executionRef,
                    },
                    kind: kind as string,
                    path: path as string,
                    timestamp: timestamp as string,
                    extractedData: data as any,
                } as unknown as ResourceJson;
            });
            return [resourceTypeRef, items] as const;
        })
    );
    return Object.fromEntries(entries) as ResourceMap;
}


