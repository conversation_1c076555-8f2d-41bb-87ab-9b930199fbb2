import type { ExtractionSche<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Execution<PERSON><PERSON>, StrategyStateJson } from '@toolproof-npm/schema';
import type { UIContext } from './types/types.js';
import type { ErrorObject } from 'ajv';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { getGenesisValidator, getGenesisRef, getResourceValidator } from './compilers.js';
import { generateExecutionSchemaOverlay, generateStrategyStateSchemaOverlay } from './overlays.js';


export function validateSpecial(defsPointer: string, extractedData: unknown, uiContext: UIContext) {
    try {
        // Compile by absolute $ref rather than compiling the raw subschema object
        const ref = getGenesisRef(defsPointer); // e.g., https://.../Genesis.json#/$defs/ResourceFormatData

        const schema = { $ref: ref };
        const { validate } = getGenesisValidator(schema, `__ref:${ref}`);
        const ok = validate.call(
            uiContext,
            extractedData
        );
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
}

export function validateResource(
    extractionSchemaValue: ExtractionSchemaJson,
    extractedData: unknown
) {
    try {
        const validate = getResourceValidator(extractionSchemaValue);
        const ok = validate(extractedData);
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
}

export function validateExecution(execution: ExecutionJson | null, job: JobJson | null, uiContext: UIContext) {
    if (!execution) {
        return { isValid: false, errors: [{ message: 'Execution is null' }] };
    }
    if (!job) {
        return { isValid: false, errors: [{ message: 'Job is null' }] };
    }
    try {
        // Start from a wrapper that references the base Execution schema
        const baseRef = getGenesisRef(CONSTANTS.SCHEMA.Execution);
        // console.log(`Execution baseRef: ${baseRef}`);
        const base = { allOf: [{ $ref: baseRef }] };

        // Apply overlay by appending constraints to allOf
        const ExecutionSchemaOverlay = generateExecutionSchemaOverlay(base, job);

        // console.log('job:', JSON.stringify(job, null, 2));
        // console.log('execution:', JSON.stringify(execution, null, 2));
        // console.log('ExecutionSchemaOverlay:', JSON.stringify(ExecutionSchemaOverlay, null, 2));

        // Build a cache key that changes when execution or job changes
        const overlayKey = `__executionOverlay:${execution.identity}:${execution.jobRef}`;

        const { validate } = getGenesisValidator(ExecutionSchemaOverlay, overlayKey);
        const ok = validate.call(
            uiContext,
            execution
        );
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
}

export function validateStrategyState(strategyState: StrategyStateJson, execution: ExecutionJson | null, uiContext: UIContext) {
    if (!execution) {
        return { isValid: false, errors: [{ message: 'Execution is null' }] };
    }
    try {
        const baseRef = getGenesisRef(CONSTANTS.SCHEMA.StrategyState);
        const base = { allOf: [{ $ref: baseRef }] };
        const StrategyStateSchemaOverlay = generateStrategyStateSchemaOverlay(base, execution);
        // console.log('StrategyStateSchemaOverlay:', JSON.stringify(StrategyStateSchemaOverlay, null, 2));
        const overlayKey = `__strategyStateOverlay:${execution.identity}:${execution.jobRef}`;
        const { validate } = getGenesisValidator(StrategyStateSchemaOverlay, overlayKey);
        const ok = validate.call(uiContext, strategyState);
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
}
