{"name": "@toolproof/console", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@google-cloud/storage": "^7.16.0", "@heroicons/react": "^2.0.18", "@langchain/core": "^0.3.55", "@langchain/langgraph": "^0.2.72", "@langchain/langgraph-sdk": "^0.0.74", "@pinecone-database/pinecone": "^2.0.1", "@react-three/drei": "^10.7.7", "@react-three/fiber": "^9.4.2", "@reduxjs/toolkit": "^2.0.1", "@toolproof-npm/schema": "^0.1.65", "@toolproof-npm/shared": "^0.1.72", "@toolproof-npm/validation": "^0.1.32", "@toolproof-npm/visualization": "workspace:*", "@types/bcryptjs": "^3.0.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "autoprefixer": "10.4.15", "bcryptjs": "^3.0.3", "dotenv": "^16.4.7", "eslint": "8.48.0", "eslint-config-next": "13.4.19", "firebase": "^10.13.1", "json-schema-to-ts": "^3.1.1", "next": "^16.0.10", "next-auth": "^4.23.1", "openid-client": "^5.7.1", "postcss": "8.4.29", "react": "19.2.3", "react-dom": "19.2.3", "react-firebase-hooks": "^5.1.1", "react-force-graph-3d": "^1.26.1", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-redux": "^9.1.0", "stripe": "^20.0.0", "styled-jsx": "^5.1.1", "tailwindcss": "3.3.3", "three": "^0.177.0", "typescript": "5.2.2", "uuid": "^11.1.0", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.3"}, "devDependencies": {"@types/react": "19.2.3", "@types/react-dom": "19.2.3", "@types/three": "^0.177.0", "@typescript-eslint/eslint-plugin": "^6.6.0", "@typescript-eslint/parser": "^6.6.0", "json-schema-to-typescript": "^15.0.4", "ts-node": "^10.9.2"}}