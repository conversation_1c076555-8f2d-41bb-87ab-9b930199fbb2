const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
      },
    ],
  },
  serverExternalPackages: ['firebase-admin'],
  turbopack: {
    resolveAlias: {
      'shared': path.resolve(__dirname, '../shared'),
    },
  },
};

module.exports = nextConfig;
