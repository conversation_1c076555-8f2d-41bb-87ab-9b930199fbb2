import type { ResourceJson, ResourceTypeIdentityJson } from '@toolproof-npm/schema';
import type { ResourceMap } from '@toolproof-npm/shared/types';
import { useEffect, useMemo, useState } from 'react';
import { collection, onSnapshot } from 'firebase/firestore';
import { db } from '../setup/firebaseWebInit';

type ExtractedCacheEntry = {
  timestamp?: string;
  data: unknown;
};

// Module-level state to reduce bucket re-fetches across tab/route switches.
const extractedDataCache = new Map<string, ExtractedCacheEntry>();

// Global state for items and loading status to persist across hook unmounts/re-mounts
const globalItemsByType: Record<string, ResourceJson[]> = {};
const globalLoadingByType: Record<string, boolean> = {};
const globalSubscriptions: Record<string, { unsub: () => void; count: number }> = {};
// Store listeners as functions that take the latest data
const globalListeners: Record<string, Set<(items: ResourceJson[], loading: boolean, error?: Error) => void>> = {};

function makeCacheKey(path: string, timestamp?: string): string {
  return `${path}@@${timestamp ?? ''}`;
}

async function fetchExtractedData(path: string, timestamp?: string): Promise<unknown> {
  const cacheKey = makeCacheKey(path, timestamp);
  const cached = extractedDataCache.get(cacheKey);
  if (cached) return cached.data;

  const url = `/api/resources/extracted-data?path=${encodeURIComponent(path)}${timestamp ? `&timestamp=${encodeURIComponent(timestamp)}` : ''}`;
  const res = await fetch(url, { cache: 'no-store' });
  if (!res.ok) {
    throw new Error(`Failed to fetch resource data for ${path} via ${url}: ${res.status} ${res.statusText}`);
  }

  const contentType = res.headers.get('content-type') || '';
  const data = contentType.includes('application/json')
    ? ((await res.json()) as unknown)
    : ((await res.text()) as unknown);

  extractedDataCache.set(cacheKey, { timestamp, data });
  return data;
}

async function mapWithConcurrency<TInput, TOutput>(
  items: TInput[],
  concurrency: number,
  mapper: (item: TInput) => Promise<TOutput>
): Promise<TOutput[]> {
  const limit = Math.max(1, Math.floor(concurrency));
  const results: TOutput[] = new Array(items.length);
  let nextIndex = 0;

  async function worker(): Promise<void> {
    while (true) {
      const index = nextIndex;
      nextIndex += 1;
      if (index >= items.length) return;
      results[index] = await mapper(items[index]);
    }
  }

  const workers = Array.from({ length: Math.min(limit, items.length) }, () => worker());
  await Promise.all(workers);
  return results;
}

function requireString(value: unknown, fieldName: string): string {
  if (typeof value !== 'string' || value.length === 0) {
    throw new Error(`Invalid or missing required field: ${fieldName}`);
  }
  return value;
}

function parseResourceMetaFromDoc(_resourceTypeRefFromPath: string, docId: string, raw: Record<string, unknown>) {
  const identity = (typeof raw.identity === 'string' && raw.identity.length > 0) ? raw.identity : docId;
  const resourceTypeRefField = requireString(raw.resourceTypeRef, 'resourceTypeRef');

  const kind = requireString(raw.kind, 'kind');
  if (kind !== 'materialized') {
    throw new Error(`Invalid kind for resource doc (expected materialized): ${kind}`);
  }

  const creationContext = raw.creationContext;
  if (!creationContext || typeof creationContext !== 'object') {
    throw new Error('Invalid or missing required field: creationContext');
  }

  const resourceRoleRef = creationContext instanceof Map
    ? requireString(creationContext.get('resourceRoleRef'), 'creationContext.resourceRoleRef')
    : requireString((creationContext as Record<string, unknown>).resourceRoleRef, 'creationContext.resourceRoleRef');

  const executionRef = creationContext instanceof Map
    ? requireString(creationContext.get('executionRef'), 'creationContext.executionRef')
    : requireString((creationContext as Record<string, unknown>).executionRef, 'creationContext.executionRef');

  const timestamp = requireString(raw.timestamp, 'timestamp');
  const path = requireString(raw.path, 'path');

  return {
    meta: {
      identity,
      resourceTypeRef: resourceTypeRefField,
      creationContext: {
        resourceRoleRef,
        executionRef,
      },
      kind,
      path,
      timestamp,
    } as unknown as Omit<ResourceJson, 'extractedData'>,
    extractedDataFromDoc: raw.extractedData,
  };
}

function notifyListeners(resourceTypeRef: string, items: ResourceJson[], loading: boolean, error?: Error) {
  const listeners = globalListeners[resourceTypeRef];
  if (listeners) {
    listeners.forEach((l) => l(items, loading, error));
  }
}

// Simplified resources data hook: only Types -> Resources
export function useResources(
  resourceTypeRefs: ResourceTypeIdentityJson[],
  options?: { debug?: boolean }
): { items: ResourceMap; loading: boolean; error: Error | undefined } {
  const debug = !!options?.debug;
  const resourceTypeRefsKey = useMemo(() => [...resourceTypeRefs].sort().join('|'), [resourceTypeRefs]);

  // Initialize from global state to avoid flashing "loading" state
  const [itemsByType, setItemsByType] = useState<ResourceMap>(() => {
    const initial: ResourceMap = {};
    resourceTypeRefs.forEach(ref => {
      if (globalItemsByType[ref]) initial[ref] = globalItemsByType[ref];
    });
    return initial;
  });

  const [loadingByType, setLoadingByType] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    resourceTypeRefs.forEach(ref => {
      initial[ref] = globalLoadingByType[ref] ?? true;
    });
    return initial;
  });

  const [loading, setLoading] = useState(() => {
    if (resourceTypeRefs.length === 0) return false;
    return resourceTypeRefs.some(ref => globalLoadingByType[ref] ?? true);
  });

  const [error, setError] = useState<Error | undefined>(undefined);

  useEffect(() => {
    const refs = resourceTypeRefsKey ? resourceTypeRefsKey.split('|').filter(Boolean) : [];
    if (refs.length === 0) {
      setItemsByType({});
      setLoadingByType({});
      setLoading(false);
      setError(undefined);
      return;
    }

    let mounted = true;

    // Helper to update local state and global cache
    const updateState = (ref: string, items: ResourceJson[], isLoading: boolean, err?: Error) => {
      globalItemsByType[ref] = items;
      globalLoadingByType[ref] = isLoading;
      if (mounted) {
        setItemsByType((prev) => ({ ...prev, [ref]: items }));
        setLoadingByType((prev) => ({ ...prev, [ref]: isLoading }));
        if (err) setError(err);
      }
      notifyListeners(ref, items, isLoading, err);
    };

    // Subscribe to each ref
    const cleanups = refs.map((resourceTypeRef) => {
      // Add listener for this ref to get updates from shared subscription
      if (!globalListeners[resourceTypeRef]) {
        globalListeners[resourceTypeRef] = new Set();
      }

      const listener = (items: ResourceJson[], isLoading: boolean, err?: Error) => {
        if (!mounted) return;
        setItemsByType((prev) => ({ ...prev, [resourceTypeRef]: items }));
        setLoadingByType((prev) => ({ ...prev, [resourceTypeRef]: isLoading }));
        if (err) setError(err);
      };

      globalListeners[resourceTypeRef].add(listener);

      // Initialize global subscription if not already present
      if (!globalSubscriptions[resourceTypeRef]) {
        if (debug) console.debug('[useResources] creating new subscription for', resourceTypeRef);

        globalLoadingByType[resourceTypeRef] = true;
        setLoadingByType((prev) => ({ ...prev, [resourceTypeRef]: true }));

        const snapshotSeqByType = { seq: 0 };

        const unsub = onSnapshot(
          collection(db, 'resources', resourceTypeRef, 'members'),
          (snap) => {
            const currentSeq = ++snapshotSeqByType.seq;
            const docs = snap.docs;
            const parseErrors: string[] = [];
            const baseResources: Array<{ resource: ResourceJson; needsHydration: boolean }> = [];

            for (const d of docs) {
              try {
                const raw = d.data() as Record<string, unknown>;
                const parsed = parseResourceMetaFromDoc(resourceTypeRef, d.id, raw);
                const extractedDataFromDoc = parsed.extractedDataFromDoc;
                const hasExtractedData = (
                  typeof extractedDataFromDoc === 'object'
                  && extractedDataFromDoc !== null
                  && Object.keys(extractedDataFromDoc as Record<string, unknown>).length > 0
                );
                baseResources.push({
                  resource: {
                    ...parsed.meta,
                    extractedData: (hasExtractedData ? extractedDataFromDoc : {}) as ResourceJson['extractedData'],
                  } as ResourceJson,
                  needsHydration: !hasExtractedData,
                });
              } catch (e) {
                const message = e instanceof Error ? e.message : String(e);
                parseErrors.push(`${resourceTypeRef}/${d.id}: ${message}`);
              }
            }

            const currentItems = baseResources.map((x) => x.resource);
            let currentErr: Error | undefined = undefined;
            if (parseErrors.length) {
              currentErr = new Error(`Some resource docs could not be parsed:\n${parseErrors.slice(0, 10).join('\n')}${parseErrors.length > 10 ? `\n...and ${parseErrors.length - 10} more` : ''}`);
            }

            updateState(resourceTypeRef, currentItems, false, currentErr);

            // Hydrate extractedData
            const toHydrate = baseResources
              .filter((x) => x.needsHydration && typeof x.resource.path === 'string' && x.resource.path.length > 0)
              .map((x) => ({ identity: x.resource.identity, path: x.resource.path as string, timestamp: x.resource.timestamp }));

            if (toHydrate.length > 0) {
              void (async () => {
                try {
                  const hydrated = await mapWithConcurrency(toHydrate, 8, async (item) => {
                    const data = await fetchExtractedData(item.path, item.timestamp);
                    return { identity: item.identity, extractedData: data };
                  });

                  if (snapshotSeqByType.seq !== currentSeq) return;

                  const existing = globalItemsByType[resourceTypeRef] ?? [];
                  if (!existing.length) return;
                  const byId = new Map(existing.map((r) => [r.identity, r]));
                  for (const h of hydrated) {
                    const prior = byId.get(h.identity);
                    if (!prior) continue;
                    byId.set(h.identity, { ...prior, extractedData: h.extractedData as ResourceJson['extractedData'] } as ResourceJson);
                  }

                  updateState(resourceTypeRef, Array.from(byId.values()), false);
                } catch (e) {
                  if (debug) console.error('[useResources] extractedData hydration failed', e);
                  updateState(resourceTypeRef, globalItemsByType[resourceTypeRef] || [], false, e as Error);
                }
              })();
            }
          },
          (err) => {
            updateState(resourceTypeRef, globalItemsByType[resourceTypeRef] || [], false, err);
          }
        );

        globalSubscriptions[resourceTypeRef] = { unsub, count: 1 };
      } else {
        globalSubscriptions[resourceTypeRef].count++;
        // If we already have data, update local state immediately
        if (globalItemsByType[resourceTypeRef]) {
          setItemsByType(prev => ({ ...prev, [resourceTypeRef]: globalItemsByType[resourceTypeRef] }));
          setLoadingByType(prev => ({ ...prev, [resourceTypeRef]: false }));
        }
      }

      return () => {
        globalListeners[resourceTypeRef].delete(listener);
        globalSubscriptions[resourceTypeRef].count--;
        if (globalSubscriptions[resourceTypeRef].count === 0) {
          if (debug) console.debug('[useResources] closing subscription for', resourceTypeRef);
          globalSubscriptions[resourceTypeRef].unsub();
          delete globalSubscriptions[resourceTypeRef];
          // We don't delete globalItemsByType or globalLoadingByType to persist data for future re-mounts
        }
      };
    });

    return () => {
      mounted = false;
      cleanups.forEach((c) => c());
    };
  }, [resourceTypeRefsKey, debug]);

  // Derive overall loading status
  useEffect(() => {
    const refs = resourceTypeRefsKey ? resourceTypeRefsKey.split('|').filter(Boolean) : [];
    if (refs.length === 0) {
      setLoading(false);
      return;
    }
    const anyLoading = refs.some((ref) => loadingByType[ref]);
    setLoading(anyLoading);
  }, [resourceTypeRefsKey, loadingByType]);

  return { items: itemsByType, loading, error };
}