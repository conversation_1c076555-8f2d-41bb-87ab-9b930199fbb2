import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const initialState = {
    userEmail: '',
    isApproved: true,
    showSideBar: true,
    mockModeEnabled: true,
    // Read-only feature flag: when true, the Run button switches into Explorer/Runner mode.
    // When false, <PERSON> executes in-place (no runner visualization rendering).
    showExplorerOnRun: false,
    useLocalLanggraph: true,
    writeStrategyExecutionFile: false,
    hideErrorOutputRoles: true,
    // When true, /builders and /admin require session.user.isTeamMember.
    // Intended as a dev toggle; server-side enforcement also gates this by NODE_ENV.
    enforceTeamRoutes: false,
    initialDemoStageIndex: 4 as 0 | 1 | 2 | 3 | 4 | 5,
};

const configSlice = createSlice({
    name: 'config',
    initialState,
    reducers: {
        setUserEmail: (state, action: PayloadAction<string>) => {
            state.userEmail = action.payload;
            // The only way to not be approved is to be signed in with a non-approved email
            state.isApproved = action.payload === '<EMAIL>' || action.payload === '';
        },
        setShowSideBar: (state, action: PayloadAction<boolean>) => {
            state.showSideBar = action.payload;
        },
        setMockModeEnabled: (state, action: PayloadAction<boolean>) => {
            state.mockModeEnabled = action.payload;
        },
        toggleMockMode: (state) => {
            state.mockModeEnabled = !state.mockModeEnabled;
        },
        setUseLocalLanggraph: (state, action: PayloadAction<boolean>) => {
            state.useLocalLanggraph = action.payload;
        },
        setWriteStrategyExecutionFile: (state, action: PayloadAction<boolean>) => {
            state.writeStrategyExecutionFile = action.payload;
        },
        setHideErrorOutputRoles: (state, action: PayloadAction<boolean>) => {
            state.hideErrorOutputRoles = action.payload;
        },
        toggleHideErrorOutputRoles: (state) => {
            state.hideErrorOutputRoles = !state.hideErrorOutputRoles;
        },
        setEnforceTeamRoutes: (state, action: PayloadAction<boolean>) => {
            state.enforceTeamRoutes = action.payload;
        },
        toggleEnforceTeamRoutes: (state) => {
            state.enforceTeamRoutes = !state.enforceTeamRoutes;
        },
        setInitialDemoStageIndex: (state, action: PayloadAction<0 | 1 | 2 | 3 | 4 | 5>) => {
            state.initialDemoStageIndex = action.payload;
        },
    },
});

export const { setUserEmail, setShowSideBar, setMockModeEnabled, toggleMockMode, setUseLocalLanggraph, setWriteStrategyExecutionFile, setHideErrorOutputRoles, toggleHideErrorOutputRoles, setEnforceTeamRoutes, toggleEnforceTeamRoutes, setInitialDemoStageIndex } = configSlice.actions;
export default configSlice.reducer;
