import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { ResourceIdentityJson, ResourceJson } from '@toolproof-npm/schema';

export interface MockModeState {
    mockNaturals: Record<ResourceIdentityJson, ResourceJson>;
}

const initialState: MockModeState = {
    mockNaturals: {},
};

const mockModeSlice = createSlice({
    name: 'mockMode',
    initialState,
    reducers: {
        addMockNatural: (state, action: PayloadAction<ResourceJson>) => {
            const resource = action.payload;
            state.mockNaturals[resource.identity as ResourceIdentityJson] = resource;
        },
        clearMockNaturals: (state) => {
            state.mockNaturals = {};
        },
    },
});

export const { addMockNatural, clearMockNaturals } = mockModeSlice.actions;
export default mockModeSlice.reducer;
