/**
 * Global cache manager with request deduplication and stale-while-revalidate
 * Prevents duplicate requests and provides better UX
 */

import { getCachedData, setCachedData } from './dataCache';

// Track in-flight requests to prevent duplicates
const pendingRequests = new Map<string, Promise<unknown>>();

// Track cache timestamps for stale-while-revalidate
const cacheTimestamps = new Map<string, number>();

const STALE_THRESHOLD_MS = 12 * 60 * 60 * 1000; // 12 hours (half of cache expiry)

interface CacheResult<T> {
  data: T | null;
  isStale: boolean;
  fromCache: boolean;
}

/**
 * Get cached data with staleness info
 */
export function getCachedDataWithStale<T>(key: string): CacheResult<T> {
  const cached = getCachedData<T>(key);
  if (!cached) {
    return { data: null, isStale: true, fromCache: false };
  }

  const timestamp = cacheTimestamps.get(key) || 0;
  const age = Date.now() - timestamp;
  const isStale = age > STALE_THRESHOLD_MS;

  return { data: cached, isStale, fromCache: true };
}

/**
 * Fetch data with deduplication and caching
 */
export async function fetchWithCache<T>(
  key: string,
  fetcher: () => Promise<T>
): Promise<T> {
  // Check if request is already in-flight
  const pending = pendingRequests.get(key);
  if (pending) {
    return pending as Promise<T>;
  }

  // Create new request
  const request = fetcher()
    .then((data) => {
      // Cache the result
      setCachedData(key, data);
      cacheTimestamps.set(key, Date.now());
      pendingRequests.delete(key);
      return data;
    })
    .catch((error) => {
      pendingRequests.delete(key);
      throw error;
    });

  pendingRequests.set(key, request);
  return request;
}

/**
 * Check if a request is pending
 */
export function isRequestPending(key: string): boolean {
  return pendingRequests.has(key);
}

/**
 * Clear pending requests (useful for testing)
 */
export function clearPendingRequests(): void {
  pendingRequests.clear();
}

