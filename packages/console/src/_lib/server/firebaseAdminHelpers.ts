'use server';

import { ResourceTypeIdentityJson, ResourcePotentialOutputJson, JsonData<PERSON>son } from '@toolproof-npm/schema';
import type { TerminalConst, StepConst } from '@toolproof-npm/shared/types';
import { getNewIdentity as getNewIdentity_shared } from '@toolproof-npm/shared';
import { RESOURCE_CREATION } from '@toolproof-npm/shared';
import path from 'path';
import { existsSync } from 'fs';


// Ensure Application Default Credentials are resolvable for libraries that rely on ADC
// (e.g., gcs-utils via google-auth-library). Prefer an explicit env var, else fallback
// to the local gcp-key.json if present.
function ensureGcpADC() {
  if (!process.env.GOOGLE_APPLICATION_CREDENTIALS || !process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {
    const localKeyPath = path.join(process.cwd(), 'gcp-key.json');
    if (existsSync(localKeyPath)) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = localKeyPath;
    }
  }
}

// Initialize once at module load so downstream libs can discover credentials
ensureGcpADC();

export async function getNewIdentity(identifiable: TerminalConst | StepConst): Promise<string> {
  return getNewIdentity_shared(identifiable);
}

// ATTENTION: hardcoded
const CAFS_BASE_URL = process.env.CAFS_BASE_URL || 'http://34.39.50.174/api/cafs';


/** Upload a resource via CAFS */
export async function uploadResource(
  _potentialOutput: Omit<ResourcePotentialOutputJson, 'kind'>,
  content: JsonDataJson,
  timestamp?: string,
  storageType?: string
) {
  try {
    // Adding 'kind' to match ResourcePotentialOutputJson type
    const potentialOutput: import('@toolproof-npm/schema').ResourcePotentialOutputJson = {
      ..._potentialOutput,
      kind: 'potential-output'
    };

    // Create the full resource object using shared utility
    const resource = RESOURCE_CREATION.createMaterializedResource(
      potentialOutput,
      content,
      timestamp
    );

    const requestBody: any = {
      resource
    };

    if (storageType) {
      requestBody.storageType = storageType;
    }

    const response = await fetch(`${CAFS_BASE_URL}/store`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      let responseBody: string | undefined;
      try {
        const contentType = response.headers.get('content-type') || '';
        if (contentType.includes('application/json')) {
          responseBody = JSON.stringify(await response.json());
        } else {
          responseBody = await response.text();
        }
      } catch {
        responseBody = undefined;
      }

      const details = responseBody ? `; body: ${responseBody}` : '';
      throw new Error(`HTTP error! status: ${response.status}${details}`);
    }

    return await response.json();

  } catch (error) {
    throw new Error(`Failed to write file: ${error}`);
  }
}

export async function deleteResource(path: string, resourceTypeRefs: ResourceTypeIdentityJson[]) {
  try {
    const requestBody = {
      path,
      resourceTypeRefs
    };

    const response = await fetch(`${CAFS_BASE_URL}/delete`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      let responseBody: string | undefined;
      try {
        const contentType = response.headers.get('content-type') || '';
        if (contentType.includes('application/json')) {
          responseBody = JSON.stringify(await response.json());
        } else {
          responseBody = await response.text();
        }
      } catch {
        responseBody = undefined;
      }

      const details = responseBody ? `; body: ${responseBody}` : '';
      throw new Error(`HTTP error! status: ${response.status}${details}`);
    }

    return await response.json();

  } catch (error) {
    throw new Error(`Failed to delete resource: ${error}`);
  }
}