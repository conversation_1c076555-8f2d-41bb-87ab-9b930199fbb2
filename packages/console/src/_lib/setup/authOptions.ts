import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';
import { NextAuthOptions } from 'next-auth';
import bcrypt from 'bcryptjs';
import { dbAdmin } from '@toolproof-npm/shared';

// Validate NEXTAUTH_SECRET is set
if (!process.env.NEXTAUTH_SECRET) {
  console.error('⚠️  NEXTAUTH_SECRET is not set in environment variables!');
  console.error('⚠️  This will cause JWT decryption errors.');
  console.error('⚠️  Please set NEXTAUTH_SECRET in your .env.local file.');
  console.error('⚠️  You can generate one with: openssl rand -base64 32');
}

export const authOptions: NextAuthOptions = {
  providers: [
    // Email/Password Authentication
    CredentialsProvider({
      id: 'credentials',
      name: 'Email and Password',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
        name: { label: 'Name', type: 'text' },
        mode: { label: 'Mode', type: 'text' } // 'login' or 'signup'
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email and password are required');
        }

        const { email, password, name, mode } = credentials;

        try {
          // Check if user exists
          const usersRef = dbAdmin.collection('users');
          const userQuery = await usersRef.where('email', '==', email).get();

          if (mode === 'signup') {
            // Sign up flow
            if (!userQuery.empty) {
              throw new Error('User already exists with this email');
            }

            if (!name || name.trim().length === 0) {
              throw new Error('Name is required for signup');
            }

            // Hash password
            const hashedPassword = await bcrypt.hash(password, 12);

            // Create new user
            const newUserRef = usersRef.doc();
            const userData = {
              id: newUserRef.id,
              email: email.toLowerCase(),
              name: name.trim(),
              password: hashedPassword,
              createdAt: new Date(),
              updatedAt: new Date(),
              provider: 'credentials',
              isTeamMember: false
            };

            await newUserRef.set(userData);

            return {
              id: newUserRef.id,
              email: userData.email,
              name: userData.name,
              isTeamMember: false
            };
          } else {
            // Login flow
            if (userQuery.empty) {
              throw new Error('No user found with this email');
            }

            const userDoc = userQuery.docs[0];
            const userData = userDoc.data();

            // Check if user signed up with credentials (has password)
            if (!userData.password) {
              throw new Error('Please sign in with the method you used to create your account');
            }

            // Verify password
            const isValidPassword = await bcrypt.compare(password, userData.password);
            if (!isValidPassword) {
              throw new Error('Invalid password');
            }

            // Update last login
            await userDoc.ref.update({
              lastLoginAt: new Date(),
              updatedAt: new Date()
            });

            // Read isTeamMember from database, default to false if not present
            const isTeamMember = userData.isTeamMember ?? false;

            return {
              id: userData.id,
              email: userData.email,
              name: userData.name,
              isTeamMember
            };
          }
        } catch (error) {
          console.error('Auth error:', error);
          throw error;
        }
      }
    }),

    // Google OAuth (existing)
    GoogleProvider({
      clientId: process.env.GOOGLE_ID!,
      clientSecret: process.env.GOOGLE_SECRET!,
    }),
  ],

  callbacks: {
    async signIn({ user, account, profile }) {
      // Handle Google OAuth user creation/update
      if (account?.provider === 'google' && user.email) {
        try {
          const usersRef = dbAdmin.collection('users');
          const userQuery = await usersRef.where('email', '==', user.email.toLowerCase()).get();

          if (userQuery.empty) {
            // Create new user for Google OAuth
            const newUserRef = usersRef.doc();
            await newUserRef.set({
              id: newUserRef.id,
              email: user.email.toLowerCase(),
              name: user.name || profile?.name || '',
              createdAt: new Date(),
              updatedAt: new Date(),
              provider: 'google',
              isTeamMember: false
            });
          } else {
            // Update existing user (ensure isTeamMember field exists)
            const userDoc = userQuery.docs[0];
            const userData = userDoc.data();
            const updateData: any = {
              updatedAt: new Date(),
              lastLoginAt: new Date()
            };
            
            // Only set isTeamMember if it doesn't exist (preserve existing true values)
            if (userData.isTeamMember === undefined) {
              updateData.isTeamMember = false;
            }
            
            await userDoc.ref.update(updateData);
          }
        } catch (error) {
          console.error('Error handling Google OAuth user:', error);
          // Don't block sign-in, but log the error
        }
      }
      
      return true;
    },

    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
        // Include isTeamMember from user object (set in authorize callback or signIn callback)
        token.isTeamMember = (user as any).isTeamMember ?? false;
        console.log('JWT callback: user present, isTeamMember from user:', token.isTeamMember);
      }
      
      // Ensure isTeamMember is always set - fetch from database if missing
      if (token.isTeamMember === undefined && token.email) {
        console.log('JWT callback: isTeamMember undefined, fetching from DB for email:', token.email);
        try {
          const usersRef = dbAdmin.collection('users');
          const userQuery = await usersRef.where('email', '==', (token.email as string).toLowerCase()).get();
          if (!userQuery.empty) {
            const userData = userQuery.docs[0].data();
            token.isTeamMember = userData.isTeamMember ?? false;
            console.log('JWT callback: fetched isTeamMember from DB:', token.isTeamMember);
          } else {
            token.isTeamMember = false;
            console.log('JWT callback: user not found in DB, setting isTeamMember to false');
          }
        } catch (error) {
          console.error('Error fetching isTeamMember:', error);
          token.isTeamMember = false;
        }
      }
      
      // Default to false if still undefined
      if (token.isTeamMember === undefined) {
        token.isTeamMember = false;
        console.log('JWT callback: isTeamMember still undefined, defaulting to false');
      }
      
      console.log('JWT callback: final token.isTeamMember:', token.isTeamMember);
      return token;
    },

    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.isTeamMember = token.isTeamMember as boolean ?? false;
      }
      return session;
    }
  },

  pages: {
    signIn: '/auth',
    error: '/auth'
  },

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  secret: process.env.NEXTAUTH_SECRET,
};
