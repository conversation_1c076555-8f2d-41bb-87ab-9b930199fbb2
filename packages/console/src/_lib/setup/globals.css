@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-friendly base styles */
@layer base {
  html {
    overflow-x: hidden !important;
    -webkit-text-size-adjust: 100%;
    width: 100%;
    max-width: 100vw;
  }
  
  body {
    overflow-x: hidden !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    width: 100%;
    max-width: 100vw;
    position: relative;
  }
  
  * {
    box-sizing: border-box;
  }
  
  /* Prevent horizontal overflow */
  #__next,
  [data-nextjs-scroll-focus-boundary] {
    overflow-x: hidden !important;
    max-width: 100vw;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer components {
  .infoText {
    @apply p-4 bg-gray-700/50 rounded-lg max-w-[300px];
  }

  .baseBackground {
    @apply bg-[#fee987];
  }

  #__next {
    height: 100%;
  }

  /* ToolProof Brand Colors */
  .tp-brand-accent-bg {
    @apply bg-[#FFCC33];
  }

  .tp-header {
    @apply flex items-center justify-between px-4 sm:px-6 py-3 sm:py-4 bg-white;
  }

  .tp-brand-heading {
    @apply text-xl sm:text-2xl font-bold text-[#7A0019] no-underline;
  }

  .tp-brand-btn {
    @apply bg-[#7A0019] text-white rounded-md no-underline hover:bg-[#5A0013] transition-colors;
  }

  .tp-subnav-bg {
    @apply bg-white;
  }

  .tp-brand-border {
    @apply border-[#FFCC33] border-2;
  }

  .tp-subnav-link {
    @apply text-[#7A0019] no-underline hover:text-[#5A0013] transition-colors font-medium;
  }

  .tp-subnav-link-active {
    @apply bg-[#7A0019] text-white px-3 py-1 rounded-md font-normal;
  }

  .tp-subnav-link-active:hover {
    @apply bg-[#5A0013] text-white;
  }

  .tp-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6;
  }

  .tp-footer-link {
    @apply text-xs sm:text-sm text-[#7A0019] no-underline hover:text-[#5A0013] transition-colors font-medium px-3 py-1 rounded-md;
  }

  .tp-footer-link-active {
    @apply bg-[#7A0019] text-white font-normal;
  }

  .tp-footer-link-active:hover {
    @apply bg-[#5A0013] text-white;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }

}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}