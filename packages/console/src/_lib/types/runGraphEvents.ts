import type {
  StatefulStrategy<PERSON><PERSON>,
  StrategyRun<PERSON><PERSON>,
  StrategyRunUpdateJson,
} from '@toolproof-npm/schema';

export type RunGraphDebugEvent = {
  kind: 'run_graph_debug';
  label: string;
  apiUrl?: string;
  graphId?: string;
  threadId?: string;
  nodeKeys?: string[];
  event?: unknown;
};

export type RunGraphInterruptEvent = {
  kind: 'interrupt';
  message?: string;
  interruptData?: Record<string, unknown>;
  threadId?: string;
};

export type RunGraphErrorEvent = {
  kind: 'error';
  message: string;
};

export type GraphStartEvent = {
  kind: 'graph_start';
  totalSteps: number;
  statefulStrategy: StatefulStrategyJson;
};

export type StrategyRunEvent = {
  kind: 'strategy_run';
  nodeName?: string;
  strategyRun: StrategyRunJson;
};

export type StepCompleteEvent = {
  kind: 'step_complete';
  nodeName?: string;
  strategyRunUpdate: StrategyRunUpdateJson;
};

export type GraphEndEvent = {
  kind: 'graph_end';
};

export type ResumeStartEvent = {
  kind: 'resume_start';
  threadId: string;
};

export type ResumeGraphDebugEvent = {
  kind: 'resume_graph_debug';
  label: string;
  nodeKeys?: string[];
};

export type GraphUpdateEvent = {
  kind: 'graph_update';
  event: Record<string, unknown>;
};

export type RunGraphNdjsonEvent =
  | RunGraphDebugEvent
  | RunGraphInterruptEvent
  | RunGraphErrorEvent
  | GraphStartEvent
  | StrategyRunEvent
  | StepCompleteEvent
  | GraphEndEvent
  | ResumeStartEvent
  | ResumeGraphDebugEvent
  | GraphUpdateEvent;

export function getRunGraphEventKind(event: unknown): string | null {
  if (!event || typeof event !== 'object') return null;
  const rec = event as Record<string, unknown>;
  const kind = rec.kind;
  if (typeof kind === 'string') return kind;
  return null;
}
