[{"identity": "RESOURCE-0", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/0", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 0}}, {"identity": "RESOURCE-1", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/1", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 1}}, {"identity": "RESOURCE-2", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/2", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 2}}, {"identity": "RESOURCE-3", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/3", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 3}}, {"identity": "RESOURCE-4", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/4", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 4}}, {"identity": "RESOURCE-5", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/5", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 5}}, {"identity": "RESOURCE-6", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/6", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 6}}, {"identity": "RESOURCE-7", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/7", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 7}}, {"identity": "RESOURCE-8", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/8", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 8}}, {"identity": "RESOURCE-9", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/9", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 9}}]