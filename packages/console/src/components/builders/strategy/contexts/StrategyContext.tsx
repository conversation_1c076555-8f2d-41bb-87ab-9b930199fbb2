import React, { createContext, useContext } from 'react';
import type { StatefulStrategyJson, ExecutionIdentityJson, ExecutionJson, ResourceRoleIdentityJson } from '@toolproof-npm/schema';
import type { Role } from '@toolproof-npm/shared/types';

export interface StrategyContextValue {
  statefulStrategy: StatefulStrategyJson;
  strategyName: string;
  roleMap: Map<ResourceRoleIdentityJson, Role>;
  executionMap: Map<ExecutionIdentityJson, ExecutionJson>;
}

const StrategyContext = createContext<StrategyContextValue | undefined>(undefined);

export const StrategyProvider: React.FC<React.PropsWithChildren<{ value: StrategyContextValue }>> = ({ value, children }) => (
  <StrategyContext.Provider value={value}>{children}</StrategyContext.Provider>
);

export function useStrategyContext(): StrategyContextValue {
  const ctx = useContext(StrategyContext);
  if (!ctx) throw new Error('useStrategyContext must be used within a StrategyProvider');
  return ctx;
}
