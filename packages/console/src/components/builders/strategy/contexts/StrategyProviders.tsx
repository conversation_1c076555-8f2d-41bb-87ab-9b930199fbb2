
import type { SelectionContextValue } from '@/builders/strategy/contexts/SelectionContext';
import type { StrategyContextValue } from '@/builders/strategy/contexts/StrategyContext';
import type { BindingsContextValue } from '@/builders/strategy/contexts/BindingsContext';
import type { DragContextValue } from '@/builders/strategy/contexts/DragContext';
import type { StrategyActionsContextValue } from '@/builders/strategy/contexts/StrategyActionsContext';

import { SelectionProvider } from '@/builders/strategy/contexts/SelectionContext';
import { StrategyProvider } from '@/builders/strategy/contexts/StrategyContext';
import { BindingsProvider } from '@/builders/strategy/contexts/BindingsContext';
import { DragProvider } from '@/builders/strategy/contexts/DragContext';
import { StrategyActionsProvider } from '@/builders/strategy/contexts/StrategyActionsContext';


interface StrategyProvidersProps {
  selection: SelectionContextValue;
  strategy: StrategyContextValue;
  bindings: BindingsContextValue;
  drag?: DragContextValue;
  actions: StrategyActionsContextValue;
  children: React.ReactNode;
}

export function StrategyProviders({ selection, strategy, bindings, drag, actions, children }: StrategyProvidersProps) {
  const content = (
    <SelectionProvider value={selection}>
      <StrategyProvider value={strategy}>
        <BindingsProvider value={bindings}>
          <StrategyActionsProvider value={actions}>
            {children}
          </StrategyActionsProvider>
        </BindingsProvider>
      </StrategyProvider>
    </SelectionProvider>
  );

  // If drag context is provided, wrap with DragProvider (for UI implementations)
  if (drag) {
    return (
      <SelectionProvider value={selection}>
        <StrategyProvider value={strategy}>
          <BindingsProvider value={bindings}>
            <DragProvider value={drag}>
              <StrategyActionsProvider value={actions}>
                {children}
              </StrategyActionsProvider>
            </DragProvider>
          </BindingsProvider>
        </StrategyProvider>
      </SelectionProvider>
    );
  }

  return content;
}

export default StrategyProviders;