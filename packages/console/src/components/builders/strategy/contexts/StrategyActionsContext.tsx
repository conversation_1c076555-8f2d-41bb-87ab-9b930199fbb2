import { createContext, useContext } from 'react';
import type { SelectedIndex } from '@/builders/strategy/_lib/types';

export interface StrategyActionsContextValue {
    // Runtime state (not derived from strategy)
    interruptData: { threadId: string; context: Record<string, unknown> } | null;

    // Callbacks
    onStrategyNameChange: (name: string) => void;
    onLog: () => void;
    onRun: () => Promise<void>;
    onDispatch: () => Promise<void>;
    onSelectStep: (index: SelectedIndex) => void;
    onClearSelection: () => void;
    onDeleteStep: (stepIndex: number) => void;
    onBuildWorkStep: (jobId: string) => Promise<void>;
    onBuildBranchStep: (jobIds: string[]) => Promise<void>;
    onAddCaseToBranchStep: (stepIndex: number, jobId: string) => Promise<void>;
    onBuildWhileStep: (jobId: string) => Promise<void>;
    onBuildForStep: (jobId: string) => Promise<void>;
    onResumeGraph: (threadId: string, value: { identity: unknown; path: string; key: string }) => Promise<void>;
    setInterruptData: (data: { threadId: string; context: Record<string, unknown> } | null) => void;
}

const StrategyActionsContext = createContext<StrategyActionsContextValue | undefined>(undefined);

export const StrategyActionsProvider: React.FC<React.PropsWithChildren<{ value: StrategyActionsContextValue }>> = ({ value, children }) => (
  <StrategyActionsContext.Provider value={value}>{children}</StrategyActionsContext.Provider>
);

export function useStrategyActionsContext(): StrategyActionsContextValue {
  const ctx = useContext(StrategyActionsContext);
  if (!ctx) throw new Error('useStrategyActionsContext must be used within a StrategyActionsProvider');
  return ctx;
}
