import React, { createContext, useContext } from 'react';
import type { ResourceRoleIdentityJson, ResourceJson, ExecutionIdentityJson } from '@toolproof-npm/schema';

export interface BindingsContextValue {
  onbindInputRes: (executionRef: ExecutionIdentityJson, resourceRoleIdentity: ResourceRoleIdentityJson, resource: ResourceJson) => void;
  onbindInputRef: (executionRef: ExecutionIdentityJson, resourceRoleIdentity: ResourceRoleIdentityJson, source: { executionRef: ExecutionIdentityJson; resourceRoleRef: ResourceRoleIdentityJson }) => void;
  onClearInputBinding: (executionRef: ExecutionIdentityJson, resourceRoleIdentity: ResourceRoleIdentityJson) => void;
}

const BindingsContext = createContext<BindingsContextValue | undefined>(undefined);

export const BindingsProvider: React.FC<React.PropsWithChildren<{ value: BindingsContextValue }>> = ({ value, children }) => (
  <BindingsContext.Provider value={value}>{children}</BindingsContext.Provider>
);

export function useBindingsContext(): BindingsContextValue {
  const ctx = useContext(BindingsContext);
  if (!ctx) throw new Error('useBindingsContext must be used within a BindingsProvider');
  return ctx;
}