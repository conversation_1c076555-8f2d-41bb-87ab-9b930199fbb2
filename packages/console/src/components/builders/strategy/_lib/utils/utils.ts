import type { ResourceRoleIdentityJson, ExecutionIdentity<PERSON>son, ExecutionJson, StrategyStateJson, JobJson, RoleBindingMapJson, ResourceJson, ResourceMissingJson, ResourcePotentialInputJson, ResourcePotentialOutputJson } from '@toolproof-npm/schema';


// Ensure nested execution map entry exists
export function ensureExecution(strategyState: StrategyStateJson, executionIdentity: ExecutionIdentityJson) {
    if (!strategyState[executionIdentity]) {
        strategyState[executionIdentity] = {} as Record<ResourceRoleIdentityJson, ResourceMissingJson | ResourceJson | ResourcePotentialInputJson | ResourcePotentialOutputJson>;
    }
    return strategyState[executionIdentity];
}

function getUnboundRoles(roleBindingMap: RoleBindingMapJson, execution: ExecutionJson, strategyState: StrategyStateJson) {
    const unboundKeys: string[] = [];
    const execBucket = strategyState[execution.identity as ExecutionIdentityJson] || {};
    for (const roleKey of Object.keys(roleBindingMap)) {
        if (!(roleKey in execBucket)) {
            unboundKeys.push(roleKey);
        }
    }
    return unboundKeys;
}

export function getUnboundInputs(execution: ExecutionJson, strategyState: StrategyStateJson) {
    return getUnboundRoles(execution.roleBindings.inputBindingMap, execution, strategyState);
}

export function getUnboundOutputs(execution: ExecutionJson, strategyState: StrategyStateJson) {
    return getUnboundRoles(execution.roleBindings.outputBindingMap, execution, strategyState);
}

// Produce nested outputs under executionRef -> resourceRoleRef
export function bindOutputs(execution: ExecutionJson, job: JobJson): StrategyStateJson {
    const bound: StrategyStateJson = {} as StrategyStateJson;
    const execId = execution.identity as ExecutionIdentityJson;
    bound[execId] = {} as Record<ResourceRoleIdentityJson, ResourcePotentialOutputJson>;
    const outputBindingMap = execution.roleBindings.outputBindingMap;
    for (const [resourceRoleIdentity, resourceIdentity] of Object.entries(outputBindingMap)) {
        const role = job.roles.outputMap?.[resourceRoleIdentity as ResourceRoleIdentityJson];
        if (!role) throw new Error(`Cannot bind output for resourceRoleRef ${resourceRoleIdentity}: role not found`);
        (bound[execId] as Record<ResourceRoleIdentityJson, ResourcePotentialOutputJson>)[resourceRoleIdentity as ResourceRoleIdentityJson] = {
            identity: resourceIdentity,
            resourceTypeRef: role.resourceTypeRef,
            creationContext: { resourceRoleRef: resourceRoleIdentity as ResourceRoleIdentityJson, executionRef: execId },
            kind: 'potential-output'
        };
    }
    return bound;
}