import type { StatefulStrategy<PERSON><PERSON>, Execution<PERSON><PERSON>, JobIdentity<PERSON>son, ExecutionIdentityJson, ResourceIdentityJson, WorkStepJson, BranchStepJson, WhileStepJson, ForStepJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';

// Collect all executions from the strategy spec (including nested wrappers)
export function collectExecutions(spec: StatefulStrategyJson | null): ExecutionJson[] {
  if (!spec) return [];
  const executions: ExecutionJson[] = [];
  for (const step of spec.statelessStrategy.steps) {
    if (!step) continue;
    switch (step.kind) {
      case CONSTANTS.STEPS.work: {
        const exec = (step as WorkStepJson).execution as ExecutionJson | undefined;
        if (exec) executions.push(exec);
        break;
      }
      case CONSTANTS.STEPS.branch: {
        const branch = step as BranchStepJson;
        for (const cw of branch.cases ?? []) {
          const whenExec = cw?.when?.execution as ExecutionJson | undefined;
          const whatExec = cw?.what?.execution as ExecutionJson | undefined;
          if (whenExec) executions.push(whenExec);
          if (whatExec) executions.push(whatExec);
        }
        break;
      }
      case CONSTANTS.STEPS.while:
      case CONSTANTS.STEPS.for: {
        const wrapper = (step as WhileStepJson).case || (step as ForStepJson).case;
        const whenExec = wrapper?.when?.execution as ExecutionJson | undefined;
        const whatExec = wrapper?.what?.execution as ExecutionJson | undefined;
        if (whenExec) executions.push(whenExec);
        if (whatExec) executions.push(whatExec);
        break;
      }
      default: break;
    }
  }
  return executions;
}

// Collect all jobIds referenced by executions
export function collectJobRefs(spec: StatefulStrategyJson | null): JobIdentityJson[] {
  const executions = collectExecutions(spec);
  const ids = new Set<JobIdentityJson>();
  for (const exec of executions) {
    if (exec?.jobRef) ids.add(exec.jobRef);
  }
  return Array.from(ids);
}

// Build a map of executionRef -> execution for quick lookup
export function buildExecutionMap(spec: StatefulStrategyJson | null): Map<ExecutionIdentityJson, ExecutionJson> {
  const map = new Map<ExecutionIdentityJson, ExecutionJson>();
  for (const exec of collectExecutions(spec)) {
    if (exec?.identity) map.set(exec.identity as ExecutionIdentityJson, exec);
  }
  return map;
}
