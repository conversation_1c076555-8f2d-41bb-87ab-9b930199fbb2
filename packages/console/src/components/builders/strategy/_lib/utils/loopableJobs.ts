import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ResourceTypeIdentityJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';

/**
 * Returns job identities that can be used in a `for` loop.
 *
 * A job is "loopable" if:
 * - it has exactly one input role
 * - it has exactly one output role, excluding `ROLE-ErrorOutput`
 * - the input/output roles share the same `resourceTypeRef`
 */
export function getLoopableJobIdentities(jobMap: Map<JobIdentityJson, JobJson>): JobIdentityJson[] {
    const errorRoleId = CONSTANTS.SPECIALS.ROLE_ErrorOutput;

    const candidates: Array<{ id: JobIdentityJson; name: string }> = [];

    for (const job of jobMap.values()) {
        const inputEntries = Object.entries(job.roles?.inputMap ?? {});
        if (inputEntries.length !== 1) continue;

        const outputEntries = Object.entries(job.roles?.outputMap ?? {}).filter(([roleId]) => roleId !== errorRoleId);
        if (outputEntries.length !== 1) continue;

        const inputRole = inputEntries[0][1] as { resourceTypeRef?: ResourceTypeIdentityJson };
        const outputRole = outputEntries[0][1] as { resourceTypeRef?: ResourceTypeIdentityJson };

        const inputType = inputRole?.resourceTypeRef;
        const outputType = outputRole?.resourceTypeRef;

        if (!inputType || !outputType) continue;
        if (inputType !== outputType) continue;

        candidates.push({ id: job.identity as JobIdentityJson, name: job.name ?? '' });
    }

    candidates.sort((a, b) => a.name.localeCompare(b.name) || a.id.localeCompare(b.id));
    return candidates.map((c) => c.id);
}
