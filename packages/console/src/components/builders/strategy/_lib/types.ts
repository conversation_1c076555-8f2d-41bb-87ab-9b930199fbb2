import type { ExecutionIdentityJson, ResourceRoleIdentityJson } from '@toolproof-npm/schema';


// UI helper types (local)
export type SelectedIndex = {
    // Index into statefulStrategy.statelessStrategy.steps
    stepIndex: number;
    // For BranchStep only; otherwise has no meaning
    caseIndex: number | null;
};


export interface DragSource {
    executionRef: ExecutionIdentityJson;
    resourceRoleRef: ResourceRoleIdentityJson;
}