import { useMemo } from 'react';
import type { Stateful<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Exec<PERSON><PERSON><PERSON>, Branch<PERSON><PERSON><PERSON><PERSON>, WhileStep<PERSON>son, ForStep<PERSON>son, JobIdentityJson, Job<PERSON>son } from '@toolproof-npm/schema';
import type { SelectedIndex } from '@/builders/strategy/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';

interface SelectionResult {
  activeStep: StepJson | null;
  activeExecution: ExecutionJson | null;
  activeJob: JobJson | null;
}

export function useStrategySelection(
  statefulStrategy: StatefulStrategyJson | null,
  selectedIndex: SelectedIndex | null,
  jobMap: Map<JobIdentityJson, JobJson>
): SelectionResult {
  return useMemo(() => {
    if (!statefulStrategy || selectedIndex == null) {
      return { activeStep: null, activeExecution: null, activeJob: null };
    }
    const step = statefulStrategy.statelessStrategy.steps[selectedIndex.stepIndex] || null;
    if (!step) {
      return { activeStep: null, activeExecution: null, activeJob: null };
    }

    let execution: ExecutionJson | null = null;
    switch (step.kind) {
      case CONSTANTS.STEPS.work: {
        execution = step.execution as ExecutionJson;
        break;
      }
      case CONSTANTS.STEPS.branch: {
        const cases = (step as BranchStepJson).cases ?? [];
        const rawIdx = selectedIndex.caseIndex ?? 0;
        const caseIdx = Math.max(0, Math.min(rawIdx, Math.max(cases.length - 1, 0)));
        execution = (cases?.[caseIdx]?.what.execution as ExecutionJson) ?? null;
        break;
      }
      case CONSTANTS.STEPS.while:
      case CONSTANTS.STEPS.for: {
        const wrapper = (step as WhileStepJson).case || (step as ForStepJson).case;
        execution = (wrapper?.what.execution as ExecutionJson) ?? null;
        break;
      }
      default: {
        execution = null;
      }
    }

    const job = execution ? (jobMap.get(execution.jobRef as JobIdentityJson) ?? null) : null;
    return { activeStep: step, activeExecution: execution, activeJob: job };
  }, [statefulStrategy, selectedIndex, jobMap]);
}
