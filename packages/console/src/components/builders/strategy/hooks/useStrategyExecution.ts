import type {
  Execution<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ec<PERSON><PERSON><PERSON>,
  <PERSON>son<PERSON><PERSON><PERSON><PERSON>,
  ResourceIdentityJson,
  ResourceJson,
  StatefulStrategyJson,
  StrategyStateJson,
} from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { getNewIdentity, uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import type React from 'react';
import { useCallback } from 'react';

type InterruptData = { threadId: string; context: Record<string, unknown> } | null;

type ResumeValue = { identity: unknown; path: string; key: string };

type GraphEvent = Record<string, unknown>;

function asRecord(value: unknown): Record<string, unknown> | null {
  if (typeof value !== 'object' || value === null) return null;
  return value as Record<string, unknown>;
}

function getEventType(event: unknown): string | null {
  const rec = asRecord(event);
  if (!rec) return null;
  const t = rec.type;
  return typeof t === 'string' ? t : null;
}

function snapshotEvent<T>(obj: T): T {
  // Ensure console shows the event as it was at log time (<PERSON>Tool<PERSON> otherwise keeps a live reference).
  try {
    // structuredClone is supported in modern browsers; fall back to JSON clone.
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return structuredClone(obj);
  } catch {
    try {
      return JSON.parse(JSON.stringify(obj)) as T;
    } catch {
      return obj;
    }
  }
}

function extractInterruptContext(messageRaw: unknown): { message: string; interruptContext: Record<string, unknown> } {
  const msg = typeof messageRaw === 'string' ? messageRaw : String(messageRaw ?? '');
  const key = 'interruptContext: ';

  if (!msg.includes(key)) {
    return { message: msg, interruptContext: {} };
  }

  const [prefix, jsonPart] = msg.split(key);
  const message = (prefix ?? '').trim();
  try {
    const parsed = JSON.parse((jsonPart ?? '').trim()) as Record<string, unknown>;
    return { message, interruptContext: parsed };
  } catch {
    return { message, interruptContext: {} };
  }
}

function extractInterruptPayload(event: unknown): { threadId: string; context: Record<string, unknown> } {
  const rec = asRecord(event) ?? {};
  const messageRaw = rec.message;
  const threadIdRaw = rec.threadId;
  const threadId = typeof threadIdRaw === 'string' ? threadIdRaw : String(threadIdRaw ?? '');

  // Prefer structured interrupt payload forwarded by the server.
  const interruptDataRec = asRecord(rec.interruptData);
  if (interruptDataRec) {
    const message = typeof interruptDataRec.message === 'string'
      ? interruptDataRec.message
      : (typeof messageRaw === 'string' ? messageRaw : String(messageRaw ?? ''));
    return { threadId, context: { ...interruptDataRec, message } };
  }

  // Fallback: legacy messages that embed `interruptContext: { ... }`.
  const { message, interruptContext } = extractInterruptContext(messageRaw);
  return { threadId, context: { message, ...interruptContext } };
}

async function consumeNdjsonStream(opts: {
  body: ReadableStream<Uint8Array>;
  onEvent: (event: unknown) => void;
  onParseError?: (err: unknown) => void;
}) {
  const reader = opts.body.getReader();
  const decoder = new TextDecoder();
  let buffer = '';

  const flushLine = (line: string) => {
    const trimmed = line.trim();
    if (!trimmed) return;
    try {
      const event = JSON.parse(trimmed);
      opts.onEvent(event);
    } catch (e) {
      opts.onParseError?.(e);
    }
  };

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    buffer += decoder.decode(value, { stream: true });
    let idx;
    while ((idx = buffer.indexOf('\n')) >= 0) {
      const line = buffer.slice(0, idx);
      buffer = buffer.slice(idx + 1);
      flushLine(line);
    }
  }

  // Flush any trailing data
  if (buffer.trim()) {
    flushLine(buffer);
  }
}

export function useStrategyExecution(opts: {
  statefulStrategy: StatefulStrategyJson | null;
  setStatefulStrategy: React.Dispatch<React.SetStateAction<StatefulStrategyJson | null>>;
  populateLastStepWithResourceMissing: (strategy: StatefulStrategyJson) => Promise<StatefulStrategyJson>;

  mockModeEnabled: boolean;
  useLocalLanggraph: boolean;
  writeStrategyExecutionFile: boolean;

  onPersistMockNatural?: (resource: ResourceJson) => void;
  setInterruptData: (d: InterruptData) => void;
}) {
  const {
    statefulStrategy,
    setStatefulStrategy,
    populateLastStepWithResourceMissing,
    mockModeEnabled,
    useLocalLanggraph,
    writeStrategyExecutionFile,
    onPersistMockNatural,
    setInterruptData,
  } = opts;

  const forwardGraphEvent = useCallback((event: unknown) => {
    const type = getEventType(event);
    const shouldForward = type === 'strategy_run' || type === 'step_complete';
    if (!shouldForward) return;
    if (typeof window === 'undefined') return;
    window.dispatchEvent(new CustomEvent('toolproof:graphEvent', { detail: event }));
  }, []);

  const persistMockNaturalsFromEvent = useCallback(
    (event: unknown) => {
      if (!mockModeEnabled) return;
      if (!onPersistMockNatural) return;

      const rec = asRecord(event);
      if (!rec) return;

      const stateToProcess = rec.strategyStateDelta ?? rec.strategyState;
      if (!stateToProcess) return;

      const strategyState = stateToProcess as StrategyStateJson;
      Object.values(strategyState).forEach((executionBucket) => {
        if (typeof executionBucket !== 'object' || executionBucket === null) return;
        Object.values(executionBucket).forEach((resource) => {
          const r = asRecord(resource);
          if (!r) return;
          if (r.resourceTypeRef !== 'TYPE-Natural') return;
          if (typeof r.path !== 'string') return;
          if (!r.path.startsWith('mock://natural/')) return;
          onPersistMockNatural(r as unknown as ResourceJson);
        });
      });
    },
    [mockModeEnabled, onPersistMockNatural]
  );

  const handleInterrupt = useCallback(
    (event: unknown) => {
      const { threadId, context } = extractInterruptPayload(event);
      setInterruptData({ threadId, context });
    },
    [setInterruptData]
  );

  const prepareStrategyForExecution = useCallback(async (): Promise<StatefulStrategyJson | null> => {
    if (!statefulStrategy) return null;

    // If the last step still has unbound inputs, materialize ResourceMissing entries
    // so dispatch payload is consistent (Engine will prompt for real inputs).
    const updated = await populateLastStepWithResourceMissing(statefulStrategy);
    if (updated !== statefulStrategy) {
      setStatefulStrategy(updated);
    }

    return updated;
  }, [statefulStrategy, populateLastStepWithResourceMissing, setStatefulStrategy]);

  const onLog = useCallback(() => {
    if (!statefulStrategy) return;
    // eslint-disable-next-line no-console
    console.log('StatefulStrategy:', JSON.stringify(statefulStrategy, null, 2));
  }, [statefulStrategy]);

  const onRun = useCallback(async () => {
    const statefulStrategyForExecution = await prepareStrategyForExecution();
    if (!statefulStrategyForExecution) return;

    try {
      // eslint-disable-next-line no-console
      console.log('[FETCH] Starting request to /api/run-graph');
      const res = await fetch('/api/run-graph', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          statefulStrategy: statefulStrategyForExecution,
          mockMode: mockModeEnabled,
          useLocalLanggraph,
          writeStrategyExecutionFile,
        }),
      });

      // eslint-disable-next-line no-console
      console.log('[FETCH] Response received:', res.status, res.statusText);
      if (!res.ok) {
        // eslint-disable-next-line no-console
        console.error('[FETCH] Response not OK:', res.status, await res.text());
        return;
      }
      if (!res.body) {
        // eslint-disable-next-line no-console
        console.warn('[FETCH] No response body for streaming run-graph');
        return;
      }

      // eslint-disable-next-line no-console
      console.log('[FETCH] Starting to read stream...');

      await consumeNdjsonStream({
        body: res.body,
        onEvent: (event) => {
          // eslint-disable-next-line no-console
          console.log('[GRAPH EVENT]', getEventType(event), snapshotEvent(event));

          persistMockNaturalsFromEvent(event);

          if (getEventType(event) === 'interrupt') {
            // eslint-disable-next-line no-console
            console.log('🔴 Interrupt detected:', event);
            handleInterrupt(event);
            return;
          }

          forwardGraphEvent(event);
        },
        onParseError: (e) => {
          // eslint-disable-next-line no-console
          console.warn('Failed to parse graph event line:', e);
        },
      });

      // eslint-disable-next-line no-console
      console.log('[STREAM] Complete');
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('[FETCH ERROR] Streaming /api/run-graph failed:', e);
    }
  }, [
    prepareStrategyForExecution,
    mockModeEnabled,
    useLocalLanggraph,
    writeStrategyExecutionFile,
    persistMockNaturalsFromEvent,
    handleInterrupt,
    forwardGraphEvent,
  ]);

  const onResumeGraph = useCallback(
    async (threadId: string, value: ResumeValue) => {
      try {
        const res = await fetch('/api/resume-graph', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            threadId,
            resumeValue: value,
            useLocalLanggraph,
          }),
        });

        if (!res.ok) {
          // eslint-disable-next-line no-console
          console.error('[FETCH] Resume response not OK:', res.status);
          return;
        }
        if (!res.body) return;

        await consumeNdjsonStream({
          body: res.body,
          onEvent: (event) => {
            // eslint-disable-next-line no-console
            console.log('[RESUME EVENT]', getEventType(event), event);

            if (getEventType(event) === 'interrupt') {
              // eslint-disable-next-line no-console
              console.log('🔴 Recursive Interrupt detected:', event);
              handleInterrupt(event);
              return;
            }

            forwardGraphEvent(event);
          },
          onParseError: (e) => {
            // eslint-disable-next-line no-console
            console.warn('Failed to parse resume event line:', e);
          },
        });
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error('Failed to resume graph:', e);
      }
    },
    [handleInterrupt, forwardGraphEvent, useLocalLanggraph]
  );

  const onDispatch = useCallback(async () => {
    const statefulStrategyForDispatch = await prepareStrategyForExecution();
    if (!statefulStrategyForDispatch) return;

    const resourceIdentity = (await getNewIdentity(CONSTANTS.TERMINALS.resource)) as ResourceIdentityJson;
    const executionIdentity = (await getNewIdentity(CONSTANTS.TERMINALS.execution)) as ExecutionIdentityJson;

    try {
      const res = await uploadResource(
        {
          identity: resourceIdentity,
          resourceTypeRef: CONSTANTS.SPECIALS.TYPE_StatefulStrategy,
          creationContext: {
            resourceRoleRef: CONSTANTS.SPECIALS.ROLE_Manual,
            executionRef: executionIdentity,
          },
        },
        statefulStrategyForDispatch as unknown as JsonDataJson
      );

      if (res.success) {
        // eslint-disable-next-line no-console
        console.log(`✓ Saved resource ${statefulStrategyForDispatch.identity} successfully.`);
      } else {
        // eslint-disable-next-line no-console
        console.error(`✗ Save failed: ${res.message}`);
      }
    } catch {
      // ignore
    }

    // eslint-disable-next-line no-console
    console.log('Dispatch clicked');
  }, [prepareStrategyForExecution]);

  return { onLog, onRun, onDispatch, onResumeGraph };
}
