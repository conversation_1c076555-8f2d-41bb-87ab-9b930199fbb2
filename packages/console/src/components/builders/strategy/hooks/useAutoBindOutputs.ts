import { useEffect } from 'react';
import type { Job<PERSON><PERSON>ity<PERSON><PERSON>, Job<PERSON><PERSON>, StatefulStrategyJson, ExecutionJson, StrategyStateJson, ExecutionIdentityJson } from '@toolproof-npm/schema';
import { getUnboundInputs, getUnboundOutputs, bindOutputs, ensureExecution } from '@/components/builders/strategy/_lib/utils/utils';

interface Params {
    statefulStrategy: StatefulStrategyJson | null;
    activeExecution: ExecutionJson | null;
    jobMap: Map<JobIdentityJson, JobJson>;
    setStatefulStrategy: React.Dispatch<React.SetStateAction<StatefulStrategyJson | null>>;
}

// Automatically bind outputs for an execution once all inputs are bound.
export function useAutoBindOutputs({ statefulStrategy, activeExecution, jobMap, setStatefulStrategy }: Params) {
    useEffect(() => {
        if (!statefulStrategy) return;
        if (!activeExecution) return;
        const strategyState = statefulStrategy.strategyState;
        const unboundInputs = getUnboundInputs(activeExecution, strategyState);
        if (unboundInputs.length !== 0) return;

        const unboundOutputs = getUnboundOutputs(activeExecution, strategyState);
        if (unboundOutputs.length === 0) return; // Already bound

        const job = jobMap.get(activeExecution.jobRef as JobIdentityJson);
        if (!job) return;

        const boundOutputs = bindOutputs(activeExecution, job);

        setStatefulStrategy(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.strategyState ?? {}) } as StrategyStateJson;
            const execId = activeExecution.identity as ExecutionIdentityJson;
            const execBucket = ensureExecution(resMap, execId);
            const newBucket = boundOutputs[execId] || {};
            Object.assign(execBucket, newBucket);
            return { ...prev, strategyState: resMap };
        });
    }, [statefulStrategy, activeExecution, jobMap, setStatefulStrategy]);
}
