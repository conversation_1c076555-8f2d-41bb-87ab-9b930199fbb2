import { useCallback } from 'react';
import type { StatefulStrategyJson, ExecutionJson, ResourceRoleIdentityJson, ResourceJson, ExecutionIdentityJson, ResourceMissingJson, ResourcePotentialInputJson, ResourcePotentialOutputJson } from '@toolproof-npm/schema';
import { resolveResourceChain } from '@toolproof-npm/shared/utils';
import { ensureExecution } from '@/components/builders/strategy/_lib/utils/utils';

interface Params {
    statefulStrategy: StatefulStrategyJson | null;
    activeExecution: ExecutionJson | null;
    setStatefulStrategy: React.Dispatch<React.SetStateAction<StatefulStrategyJson | null>>;
}

export function useResourceBindings({ statefulStrategy, activeExecution, setStatefulStrategy }: Params) {
    const bindInputPath = useCallback((resourceRoleIdentity: ResourceRoleIdentityJson, resource: ResourceJson) => {
        if (!statefulStrategy) throw new Error('statefulStrategy not found');
        if (!activeExecution) throw new Error('activeExecution not found');
        setStatefulStrategy(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.strategyState) };
            const bucket = ensureExecution(resMap, activeExecution.identity as ExecutionIdentityJson);
            activeExecution.roleBindings.inputBindingMap[resourceRoleIdentity] = resource.identity;
            bucket[resourceRoleIdentity] = resource;
            return { ...prev, strategyState: resMap };
        });
    }, [statefulStrategy, activeExecution, setStatefulStrategy]);

    const bindInputRef = useCallback((resourceRoleIdentity: ResourceRoleIdentityJson, source: { executionRef: ExecutionIdentityJson; resourceRoleRef: ResourceRoleIdentityJson }) => {
        if (!statefulStrategy) throw new Error('statefulStrategy not found');
        if (!activeExecution) throw new Error('activeExecution not found');
        const targetExecId = activeExecution.identity as ExecutionIdentityJson;
        const resMap0 = statefulStrategy.strategyState;
        const sourceEntry = resMap0?.[source.executionRef]?.[source.resourceRoleRef] as (
            | ResourceJson
            | ResourceMissingJson
            | ResourcePotentialInputJson
            | ResourcePotentialOutputJson
            | undefined
        );
        if (!sourceEntry) throw new Error(`resourceEntry not found for source (${source.executionRef}, ${source.resourceRoleRef})`);
        setStatefulStrategy(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.strategyState) };
            const bucket = ensureExecution(resMap, targetExecId);
            // Try to resolve the chain starting from the source socket
            const result = resolveResourceChain(resMap, { executionRef: source.executionRef, resourceRoleRef: source.resourceRoleRef });
            if (result.status === 'materialized') {
                const materialized = result.entry;
                activeExecution.roleBindings.inputBindingMap[resourceRoleIdentity] = materialized.identity;
                bucket[resourceRoleIdentity] = materialized;
            } else if (result.status === 'missing') {
                // Missing should stay missing when reused: identity-sharing means "provide once, reuse later"
                // without inventing a (possibly misleading) creationContext.
                const missing = result.entry;
                const reusedMissing: ResourceMissingJson = {
                    identity: missing.identity,
                    resourceTypeRef: missing.resourceTypeRef,
                    kind: 'missing',
                };
                activeExecution.roleBindings.inputBindingMap[resourceRoleIdentity] = reusedMissing.identity;
                bucket[resourceRoleIdentity] = reusedMissing;
            } else {
                // Fallback to potential-input pointing backward
                const potentialInput: ResourcePotentialInputJson = {
                    identity: sourceEntry.identity,
                    resourceTypeRef: sourceEntry.resourceTypeRef,
                    creationContext: { executionRef: source.executionRef, resourceRoleRef: source.resourceRoleRef },
                    kind: 'potential-input',
                };
                activeExecution.roleBindings.inputBindingMap[resourceRoleIdentity] = potentialInput.identity;
                bucket[resourceRoleIdentity] = potentialInput;
            }
            return { ...prev, strategyState: resMap };
        });
    }, [statefulStrategy, activeExecution, setStatefulStrategy]);

    const clearInputBinding = useCallback((resourceRoleRef: ResourceRoleIdentityJson) => {
        if (!activeExecution) throw new Error('activeExecution not found');
        setStatefulStrategy(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.strategyState) };
            const bucket = resMap[activeExecution.identity];
            if (bucket && resourceRoleRef in bucket) {
                delete bucket[resourceRoleRef];
            }
            return { ...prev, strategyState: resMap };
        });
    }, [activeExecution, setStatefulStrategy]);

    return { bindInputPath, bindInputRef, clearInputBinding };
}
