import type { ResourceRole<PERSON>dent<PERSON><PERSON><PERSON>, ResourceIdent<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ExecutionIdentity<PERSON>son, Execution<PERSON>son, ResourceMissing<PERSON>son, ResourcePotentialInput<PERSON>son, Step<PERSON>son, WorkStep<PERSON>son, WhileStep<PERSON>son, ForStep<PERSON>son, BranchStep<PERSON>son, JobIdentity<PERSON>son, <PERSON><PERSON><PERSON> } from '@toolproof-npm/schema';
import type { Role } from '@toolproof-npm/shared/types';
import type { DragSource } from '@/builders/strategy/_lib/types';
import type { SelectedIndex } from '@/builders/strategy/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import CircleBadge from '@/builders/strategy/dom/CircleBadge';
import StrategyStatePanel from '@/builders/strategy/dom/StrategyStatePanel';
import { useStrategyContext } from '@/builders/strategy/contexts/StrategyContext';
import { useStrategyActionsContext } from '@/builders/strategy/contexts/StrategyActionsContext';
import { useCosmosData } from '@/explorer/spaces/cosmos/CosmosDataProvider';
import { useDragContext } from '@/builders/strategy/contexts/DragContext';
import { useBindingsContext } from '@/builders/strategy/contexts/BindingsContext';
import { useRef, useEffect, useState } from 'react';
import { useAppSelector } from '@/_lib/client/redux/hooks';


interface StepsAndBindingsPanelProps {
    steps: StepJson[];
    selectedIndex: SelectedIndex | null;
    jobMap: Map<JobIdentityJson, JobJson>;
    excludeCaseStepIds: string[];
    onSelect: (index: SelectedIndex) => void;
    onDeleteStep: (stepIndex: number) => void;
}

export default function StepsAndBindingsPanel({ steps, selectedIndex, jobMap, excludeCaseStepIds, onSelect, onDeleteStep }: StepsAndBindingsPanelProps) {
    const { strategyState } = useStrategyContext();
    const { onAddCaseToBranchStep } = useStrategyActionsContext();
    const { cosmosData } = useCosmosData();
    const resourceMap = cosmosData.resourceMap;
    const { dragSource, setDragSource } = useDragContext();
    const { onbindInputRes, onbindInputRef, onClearInputBinding } = useBindingsContext();
    const resourcesData: ResourceJson[] = Object.values(resourceMap).flat();
    const hideErrorOutputRoles = useAppSelector((state) => state.config.hideErrorOutputRoles);

    const [pickerRole, setPickerRole] = useState<string | null>(null);
    const [pickerStepIndex, setPickerStepIndex] = useState<number | null>(null);
    const [pickerCaseIndex, setPickerCaseIndex] = useState<number | null>(null);
    const [isDraggingOverBranch, setIsDraggingOverBranch] = useState(false);

    const readDragSourceFromEvent = (e: DragEvent | React.DragEvent<Element> | undefined): DragSource | null => {
        const dt = (e as React.DragEvent<Element> | undefined)?.dataTransfer;
        if (!dt) return null;
        const raw = dt.getData('application/toolproof-drag-source') || dt.getData('text/plain');
        if (!raw) return null;
        try {
            const parsed = JSON.parse(raw) as Partial<DragSource>;
            if (parsed && typeof parsed.executionRef === 'string' && typeof parsed.resourceRoleRef === 'string') {
                return { executionRef: parsed.executionRef as ExecutionIdentityJson, resourceRoleRef: parsed.resourceRoleRef as ResourceRoleIdentityJson };
            }
        } catch {
            return null;
        }
        return null;
    };

    const handleDropOnInput = (executionRef: ExecutionIdentityJson, resourceRoleIdentity: ResourceRoleIdentityJson, inputRoles: Role[], e?: DragEvent | React.DragEvent<Element>) => {
        const source: DragSource | null = dragSource ?? readDragSourceFromEvent(e);
        if (source == null) return;

        const inputRole = inputRoles.find(r => r.identity === resourceRoleIdentity);
        const inputType = inputRole?.resourceTypeRef;
        const draggedResource = strategyState?.[source.executionRef]?.[source.resourceRoleRef] as (ResourceJson | undefined);
        const draggedResourceTypeRef = draggedResource?.resourceTypeRef;
        if (inputType && draggedResourceTypeRef && inputType !== draggedResourceTypeRef) {
            setDragSource(null);
            return; // types mismatch
        }
        onbindInputRef(executionRef, resourceRoleIdentity as ResourceRoleIdentityJson, { executionRef: source.executionRef as ExecutionIdentityJson, resourceRoleRef: source.resourceRoleRef as ResourceRoleIdentityJson });
        setDragSource(null);
    };

    // Handle dropping a job onto a branch step to add a new case
    const handleDropJobOnBranchStep = async (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDraggingOverBranch(false);

        const jobId = e.dataTransfer.getData('application/toolproof-job-id');
        if (jobId && isBranchStep) {
            await onAddCaseToBranchStep(activeStepIndex, jobId);
        }
    };

    const handleDragOverBranchStep = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        const jobId = e.dataTransfer.types.includes('application/toolproof-job-id');
        if (jobId) {
            setIsDraggingOverBranch(true);
        }
    };

    const handleDragLeaveBranchStep = () => {
        setIsDraggingOverBranch(false);
    };

    // Close picker when clicking outside
    const panelRef = useRef<HTMLDivElement | null>(null);
    useEffect(() => {
        const onDocClick = (e: MouseEvent) => {
            if (panelRef.current && !panelRef.current.contains(e.target as Node)) {
                setPickerRole(null);
                setPickerStepIndex(null);
            }
        };
        document.addEventListener('mousedown', onDocClick);
        return () => {
            document.removeEventListener('mousedown', onDocClick);
        };
    }, []);

    if (steps.length === 0) {
        return (
            <div className="h-full flex flex-col bg-white">
                <div className="p-4 border-b border-gray-200 bg-gray-50 flex-shrink-0">
                    <h2 className="text-lg font-semibold text-gray-900 mb-1">Strategy</h2>
                    <p className="text-sm text-gray-600">Drag a job to add steps.</p>
                </div>
                <div className="flex-1 flex items-center justify-center p-8">
                    <div className="text-sm text-gray-500 text-center">No steps yet. Drag a job to add it.</div>
                </div>
            </div>
        );
    }

    // Get the active step
    const activeStepIndex = selectedIndex?.stepIndex ?? steps.length - 1;
    const activeStep = steps[activeStepIndex];

    if (!activeStep) {
        return (
            <div className="h-full flex flex-col bg-white">
                <div className="p-4 border-b border-gray-200 bg-gray-50 flex-shrink-0">
                    <h2 className="text-lg font-semibold text-gray-900 mb-1">Strategy</h2>
                    <p className="text-sm text-gray-600">Select a step.</p>
                </div>
            </div>
        );
    }

    // Handle different step types
    const isLoopStep = activeStep.kind === CONSTANTS.STEPS.for || activeStep.kind === CONSTANTS.STEPS.while;
    const isWorkStep = activeStep.kind === CONSTANTS.STEPS.work;
    const isBranchStep = activeStep.kind === CONSTANTS.STEPS.branch;

    // TypeScript note: activeStep is StepJson (WorkStep | BranchStep | WhileStep | ForStep)
    // After checking the three supported types, if none match, we have an unknown step type
    if (!isWorkStep && !isLoopStep && !isBranchStep) {
        const unknownStep = activeStep as StepJson; // Cast for access to shared properties
        return (
            <div className="h-full flex flex-col bg-white">
                <div className="p-4 border-b border-gray-200 bg-gray-50 flex-shrink-0">
                    <h2 className="text-lg font-semibold text-gray-900 mb-1">Strategy</h2>
                    <p className="text-sm text-gray-600">{unknownStep.kind} (not yet implemented)</p>
                </div>
                <div className="flex-1 p-4">
                    <div className="p-4 border border-gray-300 rounded-lg bg-gray-100">
                        <div className="text-sm text-gray-600">
                            {unknownStep.kind} (not yet implemented)
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Extract execution(s) and job(s) based on step type
    let executions: { execution: ExecutionJson; job: JobJson; label?: string; caseIndex?: number }[] = [];

    if (isWorkStep) {
        const workStep = activeStep as WorkStepJson;
        const execution = workStep.execution;
        const job = jobMap.get(execution?.jobRef);
        if (!job) return null;
        executions = [{ execution, job }];
    } else if (isLoopStep) {
        const loopStep = activeStep as WhileStepJson | ForStepJson;
        const whenExec = loopStep.case?.when?.execution;
        const whatExec = loopStep.case?.what?.execution;

        if (!whenExec || !whatExec) return null;

        const whenJob = jobMap.get(whenExec.jobRef);
        const whatJob = jobMap.get(whatExec.jobRef);

        if (!whenJob || !whatJob) return null;

        executions = [
            { execution: whenExec, job: whenJob, label: 'Condition' },
            { execution: whatExec, job: whatJob, label: 'Body' }
        ];
    } else if (isBranchStep) {
        const branchStep = activeStep as BranchStepJson;
        const cases = branchStep.cases ?? [];

        // Extract all executions from all cases
        for (let i = 0; i < cases.length; i++) {
            const caseItem = cases[i];
            const whenExec = caseItem?.when?.execution;
            const whatExec = caseItem?.what?.execution;

            if (!whenExec || !whatExec) continue;

            const whenJob = jobMap.get(whenExec.jobRef);
            const whatJob = jobMap.get(whatExec.jobRef);

            if (!whenJob || !whatJob) continue;

            executions.push(
                { execution: whenExec, job: whenJob, label: `Case ${i + 1} - Condition`, caseIndex: i },
                { execution: whatExec, job: whatJob, label: `Case ${i + 1} - Body`, caseIndex: i }
            );
        }

        if (executions.length === 0) return null;
    }

    // Helper to render a job's inputs and outputs
    const renderJobInputsOutputs = (execution: ExecutionJson, job: JobJson, label?: string, caseIndex?: number) => {
        const inputRoles: Role[] = Object.entries(job.roles.inputMap ?? {}).map(([identity, role]) => ({ identity, ...role } as Role));

        const outputRoles: Role[] = (() => {
            const outputsObj = job.roles.outputMap ?? {};
            const entries = Object.entries(outputsObj);
            const filtered = hideErrorOutputRoles
                ? entries.filter(([identity]) => identity !== 'ROLE-ErrorOutput')
                : entries;
            return filtered.map(([identity, role]) => ({ identity, ...role } as Role));
        })();

        return (
            <div key={execution.identity} className={`${label ? 'p-3 border-2 border-purple-400 rounded-lg bg-purple-50' : ''}`}>
                {label && (
                    <div className="mb-2 pb-2 border-b border-purple-300">
                        <div className="text-xs font-bold text-purple-700 uppercase tracking-wide">{label}</div>
                        <div className="text-xs text-purple-600 mt-0.5">{job.name}</div>
                    </div>
                )}

                {/* Inputs and Outputs in One Row */}
                <div className="flex gap-6">
                    {/* Inputs Section */}
                    <div className="flex-1">
                        <div className="text-xs font-semibold text-gray-700 uppercase tracking-wide mb-2">Inputs</div>
                        <div className="flex flex-wrap gap-3">
                            {inputRoles.length === 0 ? (
                                <div className="text-xs text-gray-500">No inputs</div>
                            ) : (
                                inputRoles.map((input, i) => {
                                    // Check if this is the special DynamicSource role (system-managed, non-bindable)
                                    const isDynamicSource = input.identity === CONSTANTS.SPECIALS.ROLE_DynamicSource;

                                    const inputBindingId = execution.roleBindings.inputBindingMap?.[input.identity];
                                    const inputEntry = (strategyState?.[execution.identity]?.[input.identity] as ResourceJson | ResourceMissingJson | ResourcePotentialInputJson | undefined) ?? undefined;
                                    const isBound = !!inputBindingId && !!inputEntry;
                                    const isMissing = inputEntry?.kind === 'missing';

                                    // Special styling for dynamic source (gray, non-interactive)
                                    const circleCls = isDynamicSource
                                        ? 'bg-gray-400 text-white border-gray-500 cursor-default'
                                        : isBound
                                            ? (isMissing ? 'bg-green-300 text-white border-green-400' : 'bg-green-500 text-white border-green-600')
                                            : 'bg-white text-green-600 border-green-500 hover:bg-green-50';

                                    let val: string = isDynamicSource ? 'System-managed' : '<UNBOUND>';
                                    if (!isDynamicSource && isBound) {
                                        const semId = (inputEntry as ResourceJson | undefined)?.extractedData?.identity;
                                        val = semId != null ? String(semId) : '<UNBOUND>';
                                    }

                                    return (
                                        <div key={`in-${input.name}-${i}`} className="flex flex-col items-center relative">
                                            <CircleBadge
                                                as={isDynamicSource ? "div" : "button"}
                                                className={`border-2 ${circleCls} ring-2 ring-blue-200 transition-all`}
                                                title={val}
                                                onClick={isDynamicSource ? undefined : (e) => {
                                                    e.stopPropagation();
                                                    const isSamePicker = pickerRole === input.name && pickerStepIndex === activeStepIndex && pickerCaseIndex === caseIndex;
                                                    setPickerRole(isSamePicker ? null : input.name);
                                                    setPickerStepIndex(isSamePicker ? null : activeStepIndex);
                                                    setPickerCaseIndex(isSamePicker ? null : caseIndex ?? null);
                                                }}
                                                onDragOver={isDynamicSource ? undefined : (e) => {
                                                    const source = dragSource ?? readDragSourceFromEvent(e);
                                                    if (!source) return;
                                                    const dragged = strategyState?.[source.executionRef]?.[source.resourceRoleRef] as ResourceJson | undefined;
                                                    if (!dragged?.resourceTypeRef || !input.resourceTypeRef || dragged.resourceTypeRef === input.resourceTypeRef) {
                                                        e.preventDefault();
                                                    }
                                                }}
                                                onDrop={isDynamicSource ? undefined : (e) => {
                                                    e.preventDefault();
                                                    handleDropOnInput(execution.identity as ExecutionIdentityJson, input.identity as ResourceRoleIdentityJson, inputRoles, e);
                                                    setPickerRole(null);
                                                    setPickerStepIndex(null);
                                                    setPickerCaseIndex(null);
                                                }}
                                                onContextMenu={isDynamicSource ? undefined : (e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    if (isBound && inputBindingId) {
                                                        onClearInputBinding(execution.identity as ExecutionIdentityJson, input.identity as ResourceRoleIdentityJson);
                                                    }
                                                }}
                                            >
                                                <span className="text-xs font-semibold">IN</span>
                                            </CircleBadge>
                                            <div className="text-xs text-gray-700 mt-1 font-medium">{input.name}</div>

                                            {/* File Picker Popup - not shown for dynamic source */}
                                            {!isDynamicSource && pickerRole === input.name && pickerStepIndex === activeStepIndex && pickerCaseIndex === caseIndex && (
                                                <div className="absolute z-50 top-14 left-full ml-3 w-72 bg-white border border-gray-300 rounded-lg shadow-xl" onClick={(e) => e.stopPropagation()}>
                                                    <div className="sticky top-0 bg-gray-50 border-b border-gray-200 px-3 py-2 rounded-t-lg">
                                                        <div className="text-xs font-semibold text-gray-700 uppercase tracking-wide">Choose a Natural</div>
                                                    </div>
                                                    <ul className="py-1 max-h-64 overflow-auto">
                                                        {resourcesData.filter((resource) => resource.resourceTypeRef === input.resourceTypeRef).length === 0 ? (
                                                            <li className="px-3 py-2 text-sm text-gray-500">No Naturals available</li>
                                                        ) : (
                                                            resourcesData.filter((resource) => resource.resourceTypeRef === input.resourceTypeRef).map((resource, i) => (
                                                                <li key={i}>
                                                                    <button
                                                                        className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-[#7A0019] hover:text-white transition-colors font-mono"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            onbindInputRes(execution.identity as ExecutionIdentityJson, input.identity as ResourceRoleIdentityJson, resource);
                                                                            setPickerRole(null);
                                                                            setPickerStepIndex(null);
                                                                            setPickerCaseIndex(null);
                                                                        }}
                                                                    >
                                                                        {String(resource.extractedData.identity)}
                                                                    </button>
                                                                </li>
                                                            ))
                                                        )}
                                                    </ul>
                                                    <div className="border-t border-gray-200 bg-gray-50 px-3 py-2 rounded-b-lg">
                                                        <div className="text-xs text-gray-500">Right-click input to clear</div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })
                            )}
                        </div>
                    </div>

                    {/* Outputs Section */}
                    <div className="flex-1">
                        <div className="text-xs font-semibold text-gray-700 uppercase tracking-wide mb-2">Outputs</div>
                        <div className="flex flex-wrap gap-3">
                            {outputRoles.length === 0 ? (
                                <div className="text-xs text-gray-500">No outputs</div>
                            ) : (
                                outputRoles.map((output, idx) => {
                                    const outputBindingId = execution.roleBindings.outputBindingMap?.[output.identity] as ResourceIdentityJson;
                                    const isBound = !!outputBindingId && !!strategyState?.[execution.identity]?.[output.identity as ResourceRoleIdentityJson];
                                    const circleCls = isBound
                                        ? 'bg-red-500 text-white border-red-600'
                                        : 'bg-white text-red-600 border-red-500 hover:bg-red-50';
                                    let val: string = '<UNBOUND>';
                                    if (isBound && outputBindingId) {
                                        const outputEntry = (strategyState?.[execution.identity]?.[output.identity as ResourceRoleIdentityJson] as ResourceJson | undefined) ?? undefined;
                                        const semId = outputEntry?.extractedData?.identity;
                                        val = semId != null ? String(semId) : '<UNBOUND>';
                                    }

                                    return (
                                        <div key={`out-${output.name}-${idx}`} className="flex flex-col items-center">
                                            <CircleBadge
                                                as="div"
                                                draggable={isBound}
                                                onDragStart={() => {
                                                    if (!outputBindingId) return;
                                                    const dragPayload: DragSource = { executionRef: execution.identity as ExecutionIdentityJson, resourceRoleRef: output.identity as ResourceRoleIdentityJson };
                                                    setDragSource(dragPayload);
                                                }}
                                                onDragEnd={() => setDragSource(null)}
                                                className={`border-2 ${circleCls} ring-2 ring-blue-200 transition-all ${isBound ? 'cursor-move' : ''}`}
                                                title={val}
                                            >
                                                <span className="text-xs font-semibold">OUT</span>
                                            </CircleBadge>
                                            <div className="text-xs text-gray-700 mt-1 font-medium">{output.name}</div>
                                        </div>
                                    );
                                })
                            )}
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="h-full flex flex-col bg-white" ref={panelRef}>
            <div className="p-4 border-b border-gray-200 bg-gray-50 flex-shrink-0">
                <h2 className="text-lg font-semibold text-gray-900 mb-1">Strategy</h2>
                <p className="text-sm text-gray-600">Step {activeStepIndex + 1} of {steps.length}</p>
            </div>
            <div className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0">
                {/* Active Step */}
                <div className="border-2 border-blue-500 rounded-lg p-4 bg-blue-50 shadow-md">
                    {/* Step Header with Delete Button and Step Navigation */}
                    <div className="flex items-start justify-between mb-3 pb-2 border-b border-gray-200">
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                                <button
                                    onClick={() => {
                                        if (activeStepIndex > 0) {
                                            onSelect({ stepIndex: activeStepIndex - 1, caseIndex: null });
                                        }
                                    }}
                                    disabled={activeStepIndex === 0}
                                    className="p-1 text-gray-600 hover:bg-gray-200 rounded transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
                                    title="Previous step"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                    </svg>
                                </button>
                                <div className="text-sm font-semibold text-gray-900 truncate">
                                    {isLoopStep ? (
                                        <span className="uppercase">{(activeStep as ForStepJson | WhileStepJson).kind} Loop</span>
                                    ) : isBranchStep ? (
                                        <span className="uppercase">Branch Step</span>
                                    ) : (
                                        executions[0]?.job.name
                                    )}
                                </div>
                                <button
                                    onClick={() => {
                                        if (activeStepIndex < steps.length - 1) {
                                            onSelect({ stepIndex: activeStepIndex + 1, caseIndex: null });
                                        }
                                    }}
                                    disabled={activeStepIndex === steps.length - 1}
                                    className="p-1 text-gray-600 hover:bg-gray-200 rounded transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
                                    title="Next step"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                </button>
                            </div>
                            <div className="text-xs text-gray-500 font-mono truncate mt-0.5">{activeStep.identity}</div>
                        </div>
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                onDeleteStep(activeStepIndex);
                            }}
                            className="ml-2 p-1.5 text-red-600 hover:bg-red-50 rounded transition-colors flex-shrink-0"
                            title="Delete step"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* Render Job(s) - single for WorkStep, dual for ForStep/WhileStep, multiple pairs for BranchStep */}
                    <div
                        className={`space-y-3 ${isLoopStep ? 'pl-2 border-l-4 border-purple-500' : ''} ${isBranchStep ? 'pl-2 border-l-4 border-orange-500' : ''}`}
                        onDrop={isBranchStep ? handleDropJobOnBranchStep : undefined}
                        onDragOver={isBranchStep ? handleDragOverBranchStep : undefined}
                        onDragLeave={isBranchStep ? handleDragLeaveBranchStep : undefined}
                    >
                        {isBranchStep ? (
                            <>
                                {/* Group executions by case */}
                                {(() => {
                                    const branchStep = activeStep as BranchStepJson;
                                    const cases = branchStep.cases ?? [];
                                    const selectedCaseIdx = selectedIndex?.caseIndex ?? 0;

                                    return cases.map((caseItem, caseIdx) => {
                                        const whenExec = caseItem?.when?.execution;
                                        const whatExec = caseItem?.what?.execution;
                                        if (!whenExec || !whatExec) return null;

                                        const whenJob = jobMap.get(whenExec.jobRef);
                                        const whatJob = jobMap.get(whatExec.jobRef);
                                        if (!whenJob || !whatJob) return null;

                                        const isSelected = caseIdx === selectedCaseIdx;
                                        const borderClass = isSelected ? 'border-blue-500 bg-blue-50' : 'border-orange-300 bg-orange-50';
                                        const headerClass = isSelected ? 'text-blue-700 border-blue-300' : 'text-orange-700 border-orange-300';

                                        return (
                                            <div
                                                key={`case-${caseIdx}`}
                                                className={`p-3 border-2 ${borderClass} rounded-lg space-y-3 cursor-pointer transition-colors hover:shadow-md`}
                                                onClick={() => onSelect({ stepIndex: activeStepIndex, caseIndex: caseIdx })}
                                            >
                                                <div className={`text-xs font-bold ${headerClass} uppercase tracking-wide border-b pb-2`}>
                                                    Case {caseIdx + 1} {isSelected && '(Selected)'}
                                                </div>
                                                {renderJobInputsOutputs(whenExec, whenJob, 'Condition', caseIdx)}
                                                {renderJobInputsOutputs(whatExec, whatJob, 'Body', caseIdx)}
                                            </div>
                                        );
                                    });
                                })()}

                                {/* Drop zone indicator */}
                                <div className={`mt-3 p-4 border-2 border-dashed rounded-lg transition-colors ${isDraggingOverBranch
                                    ? 'border-orange-500 bg-orange-100'
                                    : 'border-orange-300 bg-orange-50'
                                    }`}>
                                    <div className="text-xs text-orange-700 text-center font-medium">
                                        {isDraggingOverBranch ? '↓ Drop job to add new case' : 'Drag a job here to add a new case'}
                                    </div>
                                </div>
                            </>
                        ) : (
                            <>
                                {executions.map(({ execution, job, label }) => renderJobInputsOutputs(execution, job, label, undefined))}
                            </>
                        )}
                    </div>

                    {/* Help Tip */}
                    <div className="mt-3 pt-3 border-t border-gray-200">
                        <div className="text-xs text-gray-600 leading-relaxed">
                            <span className="font-semibold">Tip:</span> Drag outputs to inputs or click an input to choose a Natural. Right-click to clear.
                            {isBranchStep && <span className="block mt-1">Drag jobs onto the branch step to add more cases.</span>}
                        </div>
                    </div>
                </div>

                {/* Previous Steps via StrategyStatePanel */}
                {activeStepIndex > 0 && (
                    <div className="mt-4">
                        <h3 className="text-sm font-semibold text-gray-700 mb-2">Previous Steps</h3>
                        <StrategyStatePanel excludeStepIds={excludeCaseStepIds} />
                    </div>
                )}
            </div>
        </div>
    );
}