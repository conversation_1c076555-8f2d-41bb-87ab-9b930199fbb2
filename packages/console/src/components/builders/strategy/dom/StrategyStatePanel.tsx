import type { Resource<PERSON>son, ExecutionIdentity<PERSON>son, ResourceRoleIdentityJson, ResourceMissing<PERSON>son, ResourcePotentialInputJson, ResourcePotentialOutputJson, ExecutionJson, JobIdentityJson } from '@toolproof-npm/schema';
import type { DragSource } from '@/builders/strategy/_lib/types';
import CircleBadge from '@/builders/strategy/dom/CircleBadge';
import { useSelectionContext } from '@/builders/strategy/contexts/SelectionContext';
import { useStrategyContext } from '@/builders/strategy/contexts/StrategyContext';
import { useDragContext } from '@/builders/strategy/contexts/DragContext';
import { useCosmosDataView } from '@/explorer/spaces/cosmos/CosmosDataViewProvider';
import { useMemo } from 'react';
import { useAppSelector } from '@/_lib/client/redux/hooks';

interface StrategyStatePanelProps { excludeStepIds?: string[] }

interface GroupedResource {
    executionRef: ExecutionIdentityJson;
    resourceRoleRef: ResourceRoleIdentityJson;
    r: ResourceMissingJson | ResourcePotentialInputJson | ResourcePotentialOutputJson | ResourceJson;
    isInput: boolean;
    roleName: string;
    titleText: string;
    isMissing: boolean;
}

export default function StrategyStatePanel({ excludeStepIds = [] }: StrategyStatePanelProps) {
    const { activeExecution } = useSelectionContext();
    const { statefulStrategy, roleMap, executionMap } = useStrategyContext();
    const strategyState = statefulStrategy.strategyState;
    const { cosmosData } = useCosmosDataView();
    const jobMap = cosmosData.jobMap;
    const { setDragSource } = useDragContext();
    const hideErrorOutputRoles = useAppSelector((state) => state.config.hideErrorOutputRoles);
    const cancelDrag = () => setDragSource(null);
    const excludeSet = new Set<string>(excludeStepIds.filter(Boolean));

    // Group resources by execution and separate inputs/outputs
    const groupedResources = useMemo(() => {
        const execEntries = Object.entries(strategyState || {});
        if (execEntries.length === 0) return [];
        const groups = new Map<ExecutionIdentityJson, { inputs: GroupedResource[]; outputs: GroupedResource[]; execution: ExecutionJson | null }>();

        for (const [execId, roleMapObj] of execEntries) {
            if (!roleMapObj) continue;
            if (activeExecution?.identity === execId) continue; // exclude active execution resources

            const executionRef = execId as ExecutionIdentityJson;
            const originExec = executionMap.get(executionRef);
            const inputs: GroupedResource[] = [];
            const outputs: GroupedResource[] = [];

            for (const [resourceRoleRef, r] of Object.entries(roleMapObj as Record<string, ResourceMissingJson | ResourcePotentialInputJson | ResourcePotentialOutputJson | ResourceJson>)) {
                if (!r) continue;
                if (hideErrorOutputRoles && resourceRoleRef === 'ROLE-ErrorOutput') continue;

                const role = roleMap.get(resourceRoleRef as ResourceRoleIdentityJson);
                if (!role) {
                    console.warn(`Role not found for resourceRoleRef: ${resourceRoleRef}`);
                    continue;
                }

                let isInput: boolean | undefined = undefined;
                if (originExec) {
                    const inIds = Object.keys(originExec.roleBindings.inputBindingMap ?? {});
                    const outIds = Object.keys(originExec.roleBindings.outputBindingMap ?? {});
                    if (inIds.includes(resourceRoleRef)) {
                        isInput = true;
                    } else if (outIds.includes(resourceRoleRef)) {
                        isInput = false;
                    }
                }
                if (isInput === undefined) isInput = false;

                let titleText: string = role.name;
                if (r.kind === 'materialized') {
                    const ed = (r as ResourceJson).extractedData;
                    titleText = String(ed.identity);
                }

                const resource: GroupedResource = {
                    executionRef,
                    resourceRoleRef: resourceRoleRef as ResourceRoleIdentityJson,
                    r,
                    isInput,
                    roleName: role.name,
                    titleText,
                    isMissing: r.kind === 'missing'
                };

                if (isInput) {
                    inputs.push(resource);
                } else {
                    outputs.push(resource);
                }
            }

            if (inputs.length > 0 || outputs.length > 0) {
                groups.set(executionRef, { inputs, outputs, execution: originExec || null });
            }
        }

        return Array.from(groups.entries()).map(([execRef, data]) => ({
            executionRef: execRef,
            ...data
        })).reverse(); // Reverse to show most recent step at top
    }, [strategyState, activeExecution, executionMap, roleMap, hideErrorOutputRoles]);

    // Get job name from jobMap
    const getJobName = (execution: ExecutionJson | null): string => {
        if (!execution) return 'Unknown Job';
        const jobId = (execution as unknown as { jobRef?: string }).jobRef;
        if (!jobId) return 'Unknown Job';
        const job = jobMap.get(jobId as JobIdentityJson);
        return job?.name || jobId;
    };

    if (groupedResources.length === 0) return null;

    return (
        <div className='bg-gray-50 rounded-lg border border-gray-200 p-3'>
            <div className="text-xs font-semibold text-gray-700 uppercase tracking-wide mb-3">Available Resources</div>
            <div className="space-y-3">
                {groupedResources.map(({ executionRef, inputs, outputs, execution }) => {
                    const jobName = getJobName(execution);
                    const hasResources = inputs.length > 0 || outputs.length > 0;
                    if (!hasResources) return null;

                    return (
                        <div key={executionRef} className="border border-gray-300 rounded-lg bg-white p-3 shadow-sm">
                            {/* Step Header */}
                            <div className="mb-2 pb-2 border-b border-gray-200">
                                <div className="flex items-center gap-1.5">
                                    <div className="w-1.5 h-1.5 rounded-full bg-blue-500"></div>
                                    <div className="text-xs font-semibold text-gray-800">{jobName}</div>
                                    {/* <div className="text-xs text-gray-500 font-mono">{executionRef.slice(0, 8)}...</div> */}
                                </div>
                            </div>

                            {/* Inputs and Outputs in a single row */}
                            <div className="flex flex-wrap gap-3 items-start">
                                {/* Inputs */}
                                {inputs.length > 0 && (
                                    <div className="flex-1 min-w-0">
                                        <div className="text-[10px] font-semibold text-green-700 uppercase tracking-wide mb-1.5 flex items-center gap-1.5">
                                            <span>Inputs</span>
                                            <span className="text-gray-400">({inputs.length})</span>
                                        </div>
                                        <div className="flex flex-wrap gap-2">
                                            {inputs.map((resource) => {
                                                const dragPayload: DragSource = { executionRef, resourceRoleRef: resource.resourceRoleRef };
                                                const badgeCls = resource.isMissing
                                                    ? 'bg-green-300 text-white border-green-400'
                                                    : 'bg-green-500 text-white border-green-600';

                                                return (
                                                    <div key={`${executionRef}__${resource.resourceRoleRef}`} className="flex flex-col items-center">
                                                        <CircleBadge
                                                            as="div"
                                                            draggable
                                                            onDragStart={(e) => {
                                                                setDragSource(dragPayload);
                                                                try {
                                                                    const raw = JSON.stringify(dragPayload);
                                                                    e.dataTransfer?.setData('application/toolproof-drag-source', raw);
                                                                    e.dataTransfer?.setData('text/plain', raw);
                                                                } catch { /* ignore */ }
                                                            }}
                                                            onDragEnd={() => cancelDrag()}
                                                            className={`border-2 ${badgeCls} cursor-move transition-all hover:scale-110 hover:shadow-md`}
                                                            title={resource.titleText}
                                                        >
                                                            <span className="text-xs font-semibold">IN</span>
                                                        </CircleBadge>
                                                        <div
                                                            className="text-[10px] text-gray-700 mt-1 truncate max-w-32 font-medium"
                                                            title={resource.titleText}
                                                        >
                                                            {resource.roleName}
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                )}

                                {/* Outputs */}
                                {outputs.length > 0 && (
                                    <div className="flex-1 min-w-0">
                                        <div className="text-[10px] font-semibold text-red-700 uppercase tracking-wide mb-1.5 flex items-center gap-1.5">
                                            <span>Outputs</span>
                                            <span className="text-gray-400">({outputs.length})</span>
                                        </div>
                                        <div className="flex flex-wrap gap-2">
                                            {outputs.map((resource) => {
                                                const dragPayload: DragSource = { executionRef, resourceRoleRef: resource.resourceRoleRef };
                                                const badgeCls = 'bg-red-500 text-white border-red-600';

                                                return (
                                                    <div key={`${executionRef}__${resource.resourceRoleRef}`} className="flex flex-col items-center">
                                                        <CircleBadge
                                                            as="div"
                                                            draggable
                                                            onDragStart={(e) => {
                                                                setDragSource(dragPayload);
                                                                try {
                                                                    const raw = JSON.stringify(dragPayload);
                                                                    e.dataTransfer?.setData('application/toolproof-drag-source', raw);
                                                                    e.dataTransfer?.setData('text/plain', raw);
                                                                } catch { /* ignore */ }
                                                            }}
                                                            onDragEnd={() => cancelDrag()}
                                                            className={`border-2 ${badgeCls} cursor-move transition-all hover:scale-110 hover:shadow-md`}
                                                            title={resource.titleText}
                                                        >
                                                            <span className="text-xs font-semibold">OUT</span>
                                                        </CircleBadge>
                                                        <div
                                                            className="text-[10px] text-gray-700 mt-1 truncate max-w-32 font-medium"
                                                            title={resource.titleText}
                                                        >
                                                            {resource.roleName}
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
}