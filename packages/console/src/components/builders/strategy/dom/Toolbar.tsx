import { PlusIcon, PlayIcon, DocumentArrowDownIcon, CubeIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

interface ValidationResult {
    isValid: boolean;
    initialInputs: string[];
}

interface ToolbarProps {
    strategyName: string;
    onStrategyNameChange: (name: string) => void;
    onLog: () => void;
    onRun: () => void;
    onDispatch: () => void;
    showExplorerToggle: boolean;
    isExplorerVisible: boolean;
    onToggleExplorer: () => void;
    validationResult: ValidationResult | null;
    mockModeEnabled: boolean;
    writeStrategyExecutionFile: boolean;
    onToggleWriteStrategyExecutionFile: () => void;
}

export default function Toolbar({
    strategyName,
    onStrategyNameChange,
    onLog,
    onRun,
    onDispatch,
    showExplorerToggle,
    isExplorerVisible,
    onToggleExplorer,
    validationResult,
    mockModeEnabled,
    writeStrategyExecutionFile,
    onToggleWriteStrategyExecutionFile
}: ToolbarProps) {
    return (
        <div id="strategy-toolbar" className="px-3 sm:px-4 md:px-6 py-3 sm:py-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
                <div className="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0 w-full sm:w-auto">
                    <input
                        type="text"
                        value={strategyName}
                        onChange={(e) => onStrategyNameChange(e.target.value)}
                        className="text-base sm:text-lg md:text-xl font-semibold text-gray-900 bg-transparent border-none outline-none focus:bg-gray-50 focus:ring-2 focus:ring-[#7A0019] focus:ring-opacity-20 px-2 py-1 rounded flex-1 min-w-0 transition-all"
                        placeholder="Strategy Name"
                    />

                    {validationResult && (
                        <div className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm whitespace-nowrap flex-shrink-0 font-medium ${validationResult.isValid
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                            }`}>
                            {validationResult.isValid ? '✓ Valid' : '⚠ Invalid'}
                        </div>
                    )}
                </div>

                <div className="flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto">
                    {showExplorerToggle ? (
                        <button
                            onClick={onToggleExplorer}
                            aria-pressed={isExplorerVisible}
                            className="flex items-center space-x-2 px-3 sm:px-4 py-2 bg-gray-900 text-white rounded-md hover:bg-gray-800 transition-colors text-sm sm:text-base flex-1 sm:flex-initial justify-center shadow-sm hover:shadow font-medium mr-2 sm:mr-4"
                        >
                            <CubeIcon className="w-4 h-4" />
                            <span className="hidden sm:inline">{isExplorerVisible ? 'Builder' : 'Explorer'}</span>
                        </button>
                    ) : null}

                    {(mockModeEnabled || true) && ( // ATTENTION
                        <>
                            <button
                                onClick={onLog}
                                className="flex items-center space-x-2 px-3 sm:px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm sm:text-base flex-1 sm:flex-initial justify-center shadow-sm hover:shadow font-medium"
                            >
                                <DocumentTextIcon className="w-4 h-4" />
                                <span className="hidden sm:inline">Log</span>
                            </button>

                            <button
                                onClick={onRun}
                                className="flex items-center space-x-2 px-3 sm:px-4 py-2 bg-[#7A0019] text-white rounded-md hover:bg-[#5A0013] transition-colors text-sm sm:text-base flex-1 sm:flex-initial justify-center shadow-sm hover:shadow font-medium"
                            >
                                <PlayIcon className="w-4 h-4" />
                                <span className="hidden sm:inline">Run</span>
                            </button>
                        </>
                    )}
                    {
                        !mockModeEnabled && (
                            <button
                                onClick={onDispatch}
                                className="flex items-center space-x-2 px-3 sm:px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm sm:text-base flex-1 sm:flex-initial justify-center shadow-sm hover:shadow font-medium"
                            >
                                <DocumentArrowDownIcon className="w-4 h-4" />
                                <span className="hidden sm:inline">Dispatch</span>
                            </button>
                        )
                    }

                </div>
            </div>

            <div className="mt-3 flex items-center">
                <label className="flex items-center space-x-2 text-sm text-gray-700 select-none">
                    <input
                        type="checkbox"
                        checked={writeStrategyExecutionFile}
                        onChange={onToggleWriteStrategyExecutionFile}
                        className="h-4 w-4 rounded border-gray-300"
                    />
                    <span>Write strategyExecution.json</span>
                </label>
            </div>

            {validationResult && !validationResult.isValid && (
                <div className="mt-3 p-4 bg-red-50 border-l-4 border-red-400 rounded-r-lg shadow-sm">
                    <p className="text-sm font-semibold text-red-800">
                        Please fix validation issues before dispatching.
                    </p>
                    {validationResult.initialInputs.length > 0 && (
                        <p className="text-sm text-red-600 mt-1">
                            Required initial inputs: {validationResult.initialInputs.join(', ')}
                        </p>
                    )}
                </div>
            )}
        </div>
    );
}
