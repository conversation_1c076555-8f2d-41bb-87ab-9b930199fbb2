import type { JobIdentity<PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@toolproof-npm/schema';
import { useRef, useMemo, useLayoutEffect, useState } from 'react';

interface JobCanvasProps {
    jobMap: Map<JobIdentityJson, JobJson>;
    onJobClick: (job: JobJson) => void;
}

export default function JobCanvas({
    jobMap,
    onJobClick
}: JobCanvasProps) {
    const canvasRef = useRef<HTMLDivElement>(null);
    const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });
    const [draggedJobId, setDraggedJobId] = useState<string | null>(null);
    const [hoveredJobId, setHoveredJobId] = useState<string | null>(null);

    const availableJobs = useMemo(() => Array.from(jobMap.values()).filter(job => job.identity !== 'JOB-LessThan' as JobIdentityJson), [jobMap]); // ATTENTION: hardcoded filter

    useLayoutEffect(() => {
        const element = canvasRef.current;
        if (!element) return;

        const update = () => {
            // Use getBoundingClientRect to get accurate dimensions accounting for zoom
            const rect = element.getBoundingClientRect();
            setCanvasSize({ width: rect.width, height: rect.height });
        };

        update();

        // Handle resize and zoom changes
        const ro = new ResizeObserver(update);
        ro.observe(element);

        // Also listen for zoom changes
        window.addEventListener('resize', update);

        return () => {
            ro.disconnect();
            window.removeEventListener('resize', update);
        };
    }, []);

    // Add padding to prevent nodes from being cut off (node width w-24 = 96px, half is 48px + margin)
    // These values scale with zoom automatically since we use getBoundingClientRect
    const NODE_HALF_WIDTH = 50;
    const PADDING = 16; // p-4 = 16px

    const center = useMemo(() => ({
        x: canvasSize.width / 2,
        y: canvasSize.height / 2 // Center vertically for better distribution
    }), [canvasSize]);

    const positions = useMemo(() => {
        const count = availableJobs.length;
        if (count === 0 || canvasSize.width === 0 || canvasSize.height === 0) return [];

        // Calculate available space in both directions with more conservative margins
        const availableWidth = Math.max(0, canvasSize.width - (PADDING * 2) - (NODE_HALF_WIDTH * 2));
        const availableHeight = Math.max(0, canvasSize.height - (PADDING * 2) - (NODE_HALF_WIDTH * 2));

        // Calculate maximum radius considering both horizontal and vertical constraints
        // Use a more conservative safety margin (0.75 instead of 0.85) to handle zoom better
        const maxRadiusX = availableWidth / 2;
        const maxRadiusY = availableHeight / 2;

        // Use the smaller radius with a conservative safety margin
        // This ensures nodes fit even at higher zoom levels
        const minDimension = Math.min(maxRadiusX, maxRadiusY);
        const radius = Math.max(40, minDimension * 0.75);

        const angleStep = (2 * Math.PI) / count;
        return availableJobs.map((job, i) => {
            const angle = i * angleStep - Math.PI / 2; // start at top
            const x = center.x + radius * Math.cos(angle);
            const y = center.y + radius * Math.sin(angle);

            // Ensure nodes stay within bounds with extra safety margin
            const minX = PADDING + NODE_HALF_WIDTH;
            const maxX = canvasSize.width - PADDING - NODE_HALF_WIDTH;
            const minY = PADDING + NODE_HALF_WIDTH;
            const maxY = canvasSize.height - PADDING - NODE_HALF_WIDTH;

            const clampedX = Math.max(minX, Math.min(maxX, x));
            const clampedY = Math.max(minY, Math.min(maxY, y));

            return { job, x: clampedX, y: clampedY };
        });
    }, [availableJobs, center, canvasSize]);

    return (
        <div
            ref={canvasRef}
            className="h-full w-full bg-gray-100 relative overflow-hidden p-4"
        >
            {/* Grid background */}
            <div
                className="absolute inset-0 opacity-20"
                style={{
                    backgroundImage: `
                        linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                        linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
                    `,
                    backgroundSize: '12px 12px'
                }}
            />

            {/* Central engine node */}
            <div
                className="absolute flex items-center justify-center rounded-full shadow-md bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-300 transition-all duration-200 hover:shadow-lg hover:scale-110 hover:border-blue-400"
                style={{
                    left: center.x - 30,
                    top: center.y - 30,
                    width: 60,
                    height: 60
                }}
            >
                <div className="text-center">
                    <div className="text-[10px] text-blue-700 font-semibold">Engine</div>
                </div>
            </div>

            {/* Spokes from engine to jobs */}
            <svg className="absolute inset-0 pointer-events-none" width="100%" height="100%">
                {positions.map(({ job, x, y }, idx) => {
                    const isHovered = hoveredJobId === job.identity;
                    const isDragged = draggedJobId === job.identity;
                    return (
                        <line
                            key={`line-${idx}`}
                            x1={center.x}
                            y1={center.y}
                            x2={x}
                            y2={y}
                            stroke={isHovered || isDragged ? "#3b82f6" : "#93c5fd"}
                            strokeWidth={isHovered || isDragged ? 2 : 1.5}
                            strokeDasharray={isHovered || isDragged ? "4 2" : "3 3"}
                            className="transition-all duration-200"
                        />
                    );
                })}
            </svg>

            {/* Job nodes */}
            {positions.map(({ job, x, y }) => {
                const isDragged = draggedJobId === job.identity;
                const isHovered = hoveredJobId === job.identity;
                return (
                    <div
                        key={job.identity}
                        className="absolute -translate-x-1/2 -translate-y-1/2 transition-all duration-200"
                        style={{
                            left: x,
                            top: y,
                            opacity: isDragged ? 0.5 : 1,
                            transform: isDragged ? 'translate(-50%, -50%) scale(0.95)' : isHovered ? 'translate(-50%, -50%) scale(1.05)' : 'translate(-50%, -50%) scale(1)',
                            zIndex: isDragged || isHovered ? 10 : 1
                        }}
                    >
                        <button
                            draggable
                            onDragStart={(e) => {
                                setDraggedJobId(job.identity);
                                // store job id for drop target
                                e.dataTransfer?.setData('application/toolproof-job-id', job.identity);
                                // Set drag image to be semi-transparent
                                e.dataTransfer.effectAllowed = 'move';
                            }}
                            onDragEnd={() => {
                                setDraggedJobId(null);
                            }}
                            onMouseEnter={() => setHoveredJobId(job.identity)}
                            onMouseLeave={() => setHoveredJobId(null)}
                            className={`w-24 text-left rounded-lg bg-white border-2 shadow-md transition-all duration-200 p-1.5 cursor-grab active:cursor-grabbing ${isHovered
                                    ? 'border-blue-400 shadow-lg bg-blue-50'
                                    : isDragged
                                        ? 'border-gray-400 shadow-sm'
                                        : 'border-gray-300 hover:border-gray-400 hover:shadow-lg'
                                }`}
                            onClick={() => onJobClick?.(job)}
                            title={job.name}
                        >
                            <div className={`text-[10px] font-medium truncate transition-colors ${isHovered ? 'text-blue-800' : 'text-gray-800'
                                }`}>{job.name}</div>
                            <div className={`text-[8px] line-clamp-1 mt-0.5 transition-colors ${isHovered ? 'text-blue-600' : 'text-gray-500'
                                }`}>
                                {((job.description as unknown as { description?: string })?.description) || 'Job'}
                            </div>
                        </button>
                    </div>
                );
            })}

            {/* Empty state */}
            {availableJobs.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                        <div className="text-gray-400 text-6xl mb-4">🧭</div>
                        <h3 className="text-lg font-medium text-gray-500 mb-2">No jobs available</h3>
                        <p className="text-gray-400">Provide jobs to visualize around the engine</p>
                    </div>
                </div>
            )}
        </div>
    );
}
