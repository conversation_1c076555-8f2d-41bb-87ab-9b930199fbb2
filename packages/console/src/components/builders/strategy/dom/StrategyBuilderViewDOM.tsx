'use client';

import type { JobIdentity<PERSON>son, ResourceJson } from '@toolproof-npm/schema';
import type { DragSource } from '@/builders/strategy/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import Toolbar from '@/builders/strategy/dom/Toolbar';
import StepsAndBindingsPanel from '@/builders/strategy/dom/StepsAndBindingsPanel';
import JobCanvas from './JobCanvas';
import { useState, useRef, useLayoutEffect, useEffect, useMemo } from 'react';
import { useAppSelector, useAppDispatch } from '@/_lib/client/redux/hooks';
import { setWriteStrategyExecutionFile } from '@/_lib/client/redux/features/configSlice';
import { useSelectionContext } from '@/builders/strategy/contexts/SelectionContext';
import { useStrategyContext } from '@/builders/strategy/contexts/StrategyContext';
import { DragProvider } from '@/builders/strategy/contexts/DragContext';
import { useStrategyActionsContext } from '@/builders/strategy/contexts/StrategyActionsContext';
import { useCosmosDataView } from '@/explorer/spaces/cosmos/CosmosDataViewProvider';

export default function StrategyBuilderViewDOM(
    props: { onRunRequested?: () => void; renderExplorer?: () => React.ReactNode } = {}
) {
    // DOM-specific UI state
    const [dragSource, setDragSource] = useState<DragSource | null>(null);

    // Consume contexts
    const { activeStep, activeExecution, activeJob, selectedIndex, excludeCaseStepIds } = useSelectionContext();
    const { statefulStrategy, strategyName } = useStrategyContext();
    const { cosmosData } = useCosmosDataView();
    const jobMap = cosmosData.jobMap;
    const resourceMap = cosmosData.resourceMap;
    const loopableJobSet = useMemo(() => new Set(cosmosData.loopableJobs), [cosmosData.loopableJobs]);
    const {
        interruptData,
        onStrategyNameChange,
        onLog,
        onRun,
        onDispatch,
        onSelectStep,
        onDeleteStep,
        onBuildWorkStep,
        onBuildBranchStep,
        onBuildWhileStep,
        onBuildForStep,
        onResumeGraph,
        setInterruptData
    } = useStrategyActionsContext();

    const dispatch = useAppDispatch();
    const mockModeEnabled = useAppSelector((state) => state.config.mockModeEnabled);
    const writeStrategyExecutionFile = useAppSelector((state) => state.config.writeStrategyExecutionFile);
    const showExplorerOnRun = useAppSelector((state) => state.config.showExplorerOnRun);

    const [hasRunRequested, setHasRunRequested] = useState(false);
    const [isExplorerVisible, setIsExplorerVisible] = useState(false);
    const [draggedOverStep, setDraggedOverStep] = useState<string | null>(null);
    const [isDraggingJob, setIsDraggingJob] = useState(false);

    const handleRun = () => {
        setHasRunRequested(true);
        if (props.onRunRequested && showExplorerOnRun) {
            props.onRunRequested();
            return;
        }
        void onRun();
    };

    const toolbarRef = useRef<HTMLDivElement>(null);
    const [totalExtraHeight, setTotalExtraHeight] = useState<number>(0);

    useLayoutEffect(() => {
        const calculateExtraHeight = () => {
            let totalHeight = 0;

            if (toolbarRef.current) {
                totalHeight += toolbarRef.current.offsetHeight;
            }

            const strategyToolbar = document.getElementById('strategy-toolbar');
            if (strategyToolbar) {
                totalHeight += strategyToolbar.offsetHeight;
            }

            const buildersEntryHeader = document.getElementById('builders-entry-header');
            if (buildersEntryHeader) {
                totalHeight += buildersEntryHeader.offsetHeight;
            }

            const homeTopHeader = document.getElementById('home-top-header');
            if (homeTopHeader) {
                totalHeight += homeTopHeader.offsetHeight;
            }

            const homeFooter = document.getElementById('home-footer');
            if (homeFooter) {
                totalHeight += homeFooter.offsetHeight;
            }

            totalHeight += 80;

            setTotalExtraHeight(totalHeight);
        };

        calculateExtraHeight();
        window.addEventListener('resize', calculateExtraHeight);
        return () => window.removeEventListener('resize', calculateExtraHeight);
    }, []);

    useEffect(() => {
        const handleDragStart = (e: DragEvent) => {
            if (e.dataTransfer?.types.includes('application/toolproof-job-id')) {
                setIsDraggingJob(true);
            }
        };

        const handleDragEnd = () => {
            setIsDraggingJob(false);
            setDraggedOverStep(null);
        };

        document.addEventListener('dragstart', handleDragStart);
        document.addEventListener('dragend', handleDragEnd);
        return () => {
            document.removeEventListener('dragstart', handleDragStart);
            document.removeEventListener('dragend', handleDragEnd);
        };
    }, []);

    const dragValue = useMemo(() => ({ dragSource, setDragSource }), [dragSource]);

    return (
        <DragProvider value={dragValue}>
            <div className="flex flex-col bg-white h-full overflow-hidden">
                {/* Toolbar */}
                <div ref={toolbarRef} className="border-b border-gray-200 bg-white">
                    <Toolbar
                        strategyName={strategyName}
                        onStrategyNameChange={onStrategyNameChange}
                        onLog={onLog}
                        onRun={handleRun}
                        onDispatch={onDispatch}
                        showExplorerToggle={false}
                        isExplorerVisible={isExplorerVisible}
                        onToggleExplorer={() => setIsExplorerVisible((prev) => !prev)}
                        validationResult={null}
                        mockModeEnabled={mockModeEnabled}
                        writeStrategyExecutionFile={writeStrategyExecutionFile}
                        onToggleWriteStrategyExecutionFile={() => dispatch(setWriteStrategyExecutionFile(!writeStrategyExecutionFile))}
                    />
                </div>

                <div className="flex flex-row flex-1 min-h-0 overflow-hidden">
                    {isExplorerVisible ? (
                        <div className="flex-1 relative min-w-0 overflow-hidden">
                            {props.renderExplorer ? props.renderExplorer() : null}
                        </div>
                    ) : (
                        <>
                            {/* Column 1: Smaller canvas with step buttons - no scroll */}
                            <div className="flex-1 md:flex-[0.6] flex flex-col border-b md:border-b-0 md:border-r border-gray-200 bg-white overflow-visible md:overflow-hidden min-w-0 min-h-[300px] md:min-h-0">
                                <div className="flex-1 min-h-0 overflow-visible md:overflow-hidden w-full h-full">
                                    <JobCanvas
                                        jobMap={jobMap}
                                        loopableJobs={cosmosData.loopableJobs}
                                        onJobClick={() => { }}
                                    />
                                </div>
                                {/* Step buttons integrated at bottom - ultra compact */}
                                <div className="bg-white border-t border-[#FFCC33]/50 flex-shrink-0 p-1 sm:p-2">
                                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-1 sm:gap-0.5 w-full">
                                        {['WorkStep', 'BranchStep', 'WhileStep', 'ForStep'].map((name) => {
                                            const isDraggedOver = draggedOverStep === name;
                                            return (
                                                <div
                                                    key={name}
                                                    onDragOver={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        const canDrop = name === 'ForStep' || name === 'WhileStep'
                                                            ? e.dataTransfer?.types.includes('application/toolproof-job-loopable')
                                                            : e.dataTransfer?.types.includes('application/toolproof-job-id');

                                                        if (canDrop) {
                                                            setDraggedOverStep(name);
                                                            e.dataTransfer.dropEffect = 'move';
                                                        }
                                                    }}
                                                    onDragLeave={(e) => {
                                                        const rect = e.currentTarget.getBoundingClientRect();
                                                        const x = e.clientX;
                                                        const y = e.clientY;
                                                        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
                                                            setDraggedOverStep(null);
                                                        }
                                                    }}
                                                    onDrop={async (e) => {
                                                        e.preventDefault();
                                                        setDraggedOverStep(null);
                                                        setIsDraggingJob(false);
                                                        const jobIdentity = (e.dataTransfer?.getData('application/toolproof-job-id') ?? '') as JobIdentityJson;
                                                        if (!jobIdentity) return;
                                                        const job = jobMap.get(jobIdentity);
                                                        if (!job) {
                                                            console.warn('Dropped job id not found:', jobIdentity);
                                                            return;
                                                        }
                                                        if (name === 'WorkStep') {
                                                            await onBuildWorkStep(job.identity);
                                                        } else if (name === 'BranchStep') {
                                                            await onBuildBranchStep([job.identity]);
                                                        } else if (name === 'WhileStep') {
                                                            if (!loopableJobSet.has(job.identity)) {
                                                                console.warn('Dropped job is not loopable; ignoring drop:', job.identity);
                                                                return;
                                                            }
                                                            await onBuildWhileStep(job.identity);
                                                        } else if (name === 'ForStep') {
                                                            if (!loopableJobSet.has(job.identity)) {
                                                                console.warn('Dropped job is not loopable; ignoring drop:', job.identity);
                                                                return;
                                                            }
                                                            await onBuildForStep(job.identity);
                                                        }
                                                    }}
                                                    className={`h-10 sm:h-12 flex items-center justify-center rounded-md border-2 transition-all duration-200 cursor-pointer ${isDraggedOver
                                                        ? 'border-[#7A0019] bg-[#7A0019]/10 shadow-md scale-105 border-solid'
                                                        : isDraggingJob
                                                            ? 'border-dashed border-[#FFCC33] bg-[#FFCC33]/10 hover:border-[#7A0019] hover:bg-[#FFCC33]/20'
                                                            : 'border-dashed border-gray-300 bg-white hover:border-[#7A0019] hover:bg-gray-50'
                                                        }`}
                                                >
                                                    <div className={`text-xs sm:text-sm font-semibold text-center px-1 sm:px-2 transition-colors ${isDraggedOver ? 'text-[#7A0019]' : 'text-[#7A0019]'
                                                        }`}>
                                                        {isDraggedOver ? `Drop ${name.replace('Step', '')}` : name.replace('Step', '')}
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>
                            {/* Column 2: Steps and Bindings panel - takes more space */}
                            <div className="flex-1 md:flex-[1.4] bg-white md:border-r border-gray-200 min-h-[400px] md:min-h-0 md:h-full overflow-hidden flex flex-col min-w-0">
                                <StepsAndBindingsPanel
                                    steps={statefulStrategy.statelessStrategy.steps}
                                    selectedIndex={selectedIndex}
                                    jobMap={jobMap}
                                    excludeCaseStepIds={excludeCaseStepIds}
                                    onSelect={onSelectStep}
                                    onDeleteStep={onDeleteStep}
                                />
                            </div>
                        </>
                    )}
                </div>

                {/* Interrupt Modal */}
                {interruptData && (
                    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg shadow-2xl max-w-2xl w-full mx-4 p-6 max-h-[80vh] overflow-y-auto">
                            <h2 className="text-2xl font-bold mb-4 text-gray-900">Execution Interrupted</h2>
                            <div className="mb-6">
                                {(() => {
                                    const rawMessage = interruptData.context.message;
                                    const message = typeof rawMessage === 'string' ? rawMessage : 'No message provided';
                                    return <p className="text-gray-700">{message}</p>;
                                })()}
                            </div>
                            <h3 className="text-lg font-semibold mb-3 text-gray-900">Select a Resource:</h3>
                            <div className="border border-gray-200 rounded-lg overflow-hidden max-h-96 overflow-y-auto">
                                {(() => {
                                    const rawTypeRef = interruptData.context.resourceTypeRef;
                                    const resourceTypeRef = typeof rawTypeRef === 'string' ? rawTypeRef : null;
                                    const rawKey = interruptData.context.key;
                                    const key = typeof rawKey === 'string' ? rawKey : '';

                                    return (Object.values(resourceMap).flat() as ResourceJson[])
                                        .filter(r => !resourceTypeRef || r.resourceTypeRef === resourceTypeRef)
                                        .map(r => (
                                            <button
                                                key={r.extractedData.identity}
                                                className="w-full text-left p-3 hover:bg-blue-50 border-b last:border-b-0 border-gray-100 transition-colors"
                                                onClick={() => {
                                                    onResumeGraph(interruptData.threadId, { identity: r.extractedData.identity, path: r.path, key });
                                                    setInterruptData(null);
                                                }}
                                            >
                                                {String(r.extractedData.identity)}
                                            </button>
                                        ));
                                })()}
                            </div>
                            <div className="mt-4 flex justify-end">
                                <button onClick={() => setInterruptData(null)} className="px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </DragProvider>
    );
}
