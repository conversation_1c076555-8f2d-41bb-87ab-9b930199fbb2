
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@toolproof-npm/schema';
import type { SelectedIndex } from '@/builders/strategy/_lib/types';
import StepsAndBindingsPanel from '@/builders/strategy/dom/StepsAndBindingsPanel';
import { useSelectionContext } from '@/builders/strategy/contexts/SelectionContext';

interface SidebarProps {
  steps: StepJson[];
  jobMap: Map<JobIdentityJson, JobJson>;
  selectedIndex: SelectedIndex | null;
  onSelectStep: (index: SelectedIndex) => void;
  onDeleteStep: (stepIndex: number) => void;
}

export default function Sidebar(props: SidebarProps) {
  const {
    steps,
    jobMap,
    selectedIndex,
    onSelectStep,
    onDeleteStep,
  } = props;
  const { excludeCaseStepIds } = useSelectionContext();

  return (
    <div className="flex flex-col md:flex-row h-full overflow-hidden">
      {/* Steps and bindings */}
      <div className="w-full md:w-80 bg-white border-t md:border-t-0 md:border-l border-gray-200 flex-shrink-0 h-full overflow-hidden flex flex-col">
        <StepsAndBindingsPanel
          steps={steps}
          selectedIndex={selectedIndex}
          jobMap={jobMap}
          excludeCaseStepIds={excludeCaseStepIds}
          onSelect={onSelectStep}
          onDeleteStep={onDeleteStep}
        />
      </div>
    </div>
  );
}
