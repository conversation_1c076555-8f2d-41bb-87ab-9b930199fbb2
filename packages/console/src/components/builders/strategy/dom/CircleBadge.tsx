import React from 'react';

type CircleBadgeProps = {
  as?: 'div' | 'button';
  className?: string;
  title?: string;
  draggable?: boolean;
  children?: React.ReactNode;
  onClick?: React.MouseEventHandler;
  onContextMenu?: React.MouseEventHandler;
  onDragStart?: React.DragEventHandler;
  onDragEnd?: React.DragEventHandler;
  onDrop?: React.DragEventHandler;
  onDragOver?: React.DragEventHandler;
  type?: 'button' | 'submit' | 'reset';
} & React.HTMLAttributes<HTMLElement>;

const CircleBadge = React.forwardRef<
  HTMLButtonElement | HTMLDivElement,
  CircleBadgeProps
>(({
  as = 'div',
  className = '',
  title,
  draggable,
  children,
  onClick,
  onContextMenu,
  onDragStart,
  onDragEnd,
  onDrop,
  onDragOver,
  type = 'button',
  ...rest
}, ref) => {
  const baseCls = `w-8 h-8 rounded-full flex items-center justify-center text-[10px] ${className}`.trim();

  if (as === 'button') {
    return (
      <button 
        ref={ref as React.Ref<HTMLButtonElement>}
        className={baseCls}
        title={title}
        draggable={draggable}
        onDragStart={onDragStart}
        onDragEnd={onDragEnd}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onContextMenu={onContextMenu}
        onClick={onClick} 
        type={type}
        {...rest}
      >
        {children}
      </button>
    );
  }

  return (
    <div 
      ref={ref as React.Ref<HTMLDivElement>}
      className={baseCls}
      title={title}
      draggable={draggable}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      onDrop={onDrop}
      onDragOver={onDragOver}
      onContextMenu={onContextMenu}
      {...rest}
    >
      {children}
    </div>
  );
});

CircleBadge.displayName = 'CircleBadge';

export default CircleBadge;
