'use client';

import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Execution<PERSON><PERSON>, StatefulStrategy<PERSON>son, StrategyState<PERSON>son, ResourceIdentity<PERSON>son, ExecutionIdentity<PERSON>son, ResourceRoleIdentity<PERSON>son, JobIdentityJson, JobJson, ResourceMissingJson, ResourceTypeIdentity<PERSON>son, StatefulStrategyIdentity<PERSON>son, StatelessStrategyIdentityJson } from '@toolproof-npm/schema';
import type { SelectedIndex } from '@/builders/strategy/_lib/types';
import type { Role } from '@toolproof-npm/shared/types';
import type { ReactNode } from 'react';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { usePrefetchedIdentities } from '@/builders/_lib/usePrefetchedIdentities';
import { getUnboundInputs } from '@/builders/strategy/_lib/utils/utils';
import { useStrategySelection } from '@/builders/strategy/hooks/useStrategySelection';
import { collectJobRefs, buildExecutionMap } from '@/builders/strategy/_lib/utils/strategyTraversal';
import { useAutoBindOutputs } from '@/builders/strategy/hooks/useAutoBindOutputs';
import { useResourceBindings } from '@/builders/strategy/hooks/useResourceBindings';
import { getNewIdentity, createBranchStepFromJobPairs, createWorkStepFromJob, createLoopStepFromJob } from '@/_lib/server/firebaseAdminHelpers';
import { validateExecution, validateStrategyState } from '@toolproof-npm/validation';
import { useCosmosDataView } from '@/explorer/spaces/cosmos/CosmosDataViewProvider';
import { useAppSelector, useAppDispatch } from '@/_lib/client/redux/hooks';
import { addMockNatural } from '@/_lib/client/redux/features/mockModeSlice';
import type { ResourceJson } from '@toolproof-npm/schema';
import StrategyProviders from '@/builders/strategy/contexts/StrategyProviders';
import type { StrategyContextValue } from '@/builders/strategy/contexts/StrategyContext';
import { useStrategyExecution } from '@/builders/strategy/hooks/useStrategyExecution';
import { useState, useCallback, useEffect, useMemo } from 'react';
import { produce, type Draft } from 'immer';

export interface StrategyBuilderControllerProps {
    children: ReactNode;
}

export default function StrategyBuilderController({ children }: StrategyBuilderControllerProps) {

    // Config

    const mockModeEnabled = useAppSelector((state) => state.config.mockModeEnabled);
    const useLocalLanggraph = useAppSelector((state) => state.config.useLocalLanggraph);
    const writeStrategyExecutionFile = useAppSelector((state) => state.config.writeStrategyExecutionFile); // ATTENTION: only works when running locally


    // CosmosData

    const { cosmosData } = useCosmosDataView();
    const jobMap = cosmosData.jobMap;
    const loopableJobs: JobIdentityJson[] = cosmosData.loopableJobs;

    // StatefulStrategy

    const [statefulStrategy, setStatefulStrategy] = useState<StatefulStrategyJson | null>(null);
    const { values: prefetchedIds } = usePrefetchedIdentities({
        statefulStrategyIdentity: { terminal: CONSTANTS.TERMINALS.stateful_strategy },
        statelessStrategyIdentity: { terminal: CONSTANTS.TERMINALS.stateless_strategy }, // ATTENTION: StatelessStratety is not a terminal
    });
    const statefulStrategyIdentity = prefetchedIds.statefulStrategyIdentity;
    const statelessStrategyIdentity = prefetchedIds.statelessStrategyIdentity;
    const [strategyName, setStrategyName] = useState('Untitled Strategy'); // ATTENTION: StatefulStrategy is not a Documented

    // Build a global role map across ALL jobs referenced in the strategy
    const roleMap: Map<ResourceRoleIdentityJson, Role> = useMemo(() => {
        const map = new Map<ResourceRoleIdentityJson, Role>();
        const jobRefs = collectJobRefs(statefulStrategy);
        for (const jRef of jobRefs) {
            const job = jobMap.get(jRef);
            if (!job) continue;
            Object.entries(job.roles.inputMap ?? {}).forEach(([rid, role]) => map.set(rid as ResourceRoleIdentityJson, { identity: rid as ResourceRoleIdentityJson, ...role } as Role));
            Object.entries(job.roles.outputMap ?? {}).forEach(([rid, role]) => map.set(rid as ResourceRoleIdentityJson, { identity: rid as ResourceRoleIdentityJson, ...role } as Role));
        }
        return map;
    }, [statefulStrategy, jobMap]);

    // Build a global execution map for classifying resources (input/output) by their origin
    const executionMap = useMemo(() => buildExecutionMap(statefulStrategy), [statefulStrategy]);

    // DOC: Track selected step (and optional branch case) for step navigation and binding
    const [selectedIndex, setSelectedIndex] = useState<SelectedIndex | null>(null);

    const { activeStep, activeExecution, activeJob } = useStrategySelection(statefulStrategy, selectedIndex);

    // ATTENTION: could probably be removed; use activeJob directly to show roles
    // For branch steps: compute sibling case step ids to exclude from resource panel
    const excludeCaseStepIds = useMemo(() => {
        if (!activeStep || activeStep.kind !== CONSTANTS.STEPS.branch || !selectedIndex) return [] as string[];
        const branch = activeStep as BranchStepJson;
        const cases = branch.cases ?? [];
        const rawIdx = selectedIndex.caseIndex ?? 0;
        const caseIdx = Math.max(0, Math.min(rawIdx, Math.max(cases.length - 1, 0)));
        return cases
            .map(cw => (cw?.what?.identity as string | undefined))
            .filter((id, idx) => typeof id === 'string' && idx !== caseIdx) as string[];
    }, [activeStep, selectedIndex]);

    // Resource binding helpers
    const { bindInputRes, bindInputRef, clearInputBinding } = useResourceBindings({ setStatefulStrategy });

    const [interruptData, setInterruptData] = useState<{ threadId: string; context: Record<string, unknown> } | null>(null);


    // Redux

    const dispatch = useAppDispatch();

    const persistMockNatural = useCallback(
        (resource: ResourceJson) => {
            dispatch(addMockNatural(resource));
        },
        [dispatch]
    );


    // DOC: Generate statefulStrategyId and statelessStrategyId if not present
    // (handled by usePrefetchedIdentities)

    // ATTENTION: remove
    // DOC: When both ids are available, initialize statefulStrategy
    useEffect(() => {
        let mounted = true;
        if (statefulStrategyIdentity && statelessStrategyIdentity) {
            // Only set if not already set or if ids don't match
            setStatefulStrategy((prev) => {
                if (!mounted) return prev;
                if (!prev || prev.identity !== statefulStrategyIdentity || prev.statelessStrategy?.identity !== statelessStrategyIdentity) {
                    return {
                        identity: statefulStrategyIdentity as StatefulStrategyIdentityJson,
                        statelessStrategy: {
                            identity: statelessStrategyIdentity as StatelessStrategyIdentityJson,
                            steps: []
                        },
                        strategyState: {}
                    };
                }
                return prev;
            });
        }
        return () => {
            mounted = false;
        };
    }, [statefulStrategyIdentity, statelessStrategyIdentity, setStatefulStrategy]);

    // Auto-bind outputs when all inputs are bound
    useAutoBindOutputs({ statefulStrategy, activeStep, setStatefulStrategy });

    // DOC: Shared logic for extracting executions from a step
    const extractExecutionsFromStep = useCallback((step: StepJson): ExecutionJson[] => {
        const executions: ExecutionJson[] = [];

        if (step.kind === CONSTANTS.STEPS.work) {
            executions.push((step as WorkStepJson).execution);
        } else if (step.kind === CONSTANTS.STEPS.for || step.kind === CONSTANTS.STEPS.while) {
            const loopStep = step as { case?: { when?: { execution?: ExecutionJson }, what?: { execution?: ExecutionJson } } };
            if (loopStep.case?.when?.execution) executions.push(loopStep.case.when.execution);
            if (loopStep.case?.what?.execution) executions.push(loopStep.case.what.execution);
        } else if (step.kind === CONSTANTS.STEPS.branch) {
            const branchStep = step as BranchStepJson;
            for (const caseItem of branchStep.cases ?? []) {
                if (caseItem?.when?.execution) executions.push(caseItem.when.execution);
                if (caseItem?.what?.execution) executions.push(caseItem.what.execution);
            }
        }

        return executions;
    }, []);

    // DOC: Shared logic for computing missing input assignments
    const computeMissingInputAssignments = useCallback(async (
        executions: ExecutionJson[],
        strategyState: StrategyStateJson
    ): Promise<Array<{ execRef: ExecutionIdentityJson; resourceRoleIdentity: ResourceRoleIdentityJson; resourceIdentity: ResourceIdentityJson }>> => {
        const allAssignments: Array<{ execRef: ExecutionIdentityJson; resourceRoleIdentity: ResourceRoleIdentityJson; resourceIdentity: ResourceIdentityJson }> = [];

        for (const execution of executions) {
            const unbound = getUnboundInputs(execution, strategyState);
            if (unbound.length === 0) continue;

            const assignments = await Promise.all(
                unbound.map(async (resourceRoleIdentity) => {
                    const existing = (execution.roleBindings.inputBindingMap?.[resourceRoleIdentity as ResourceRoleIdentityJson] as string | undefined) ?? '';
                    const isValid = typeof existing === 'string' && existing.startsWith('RESOURCE-');
                    const resourceIdentity = (isValid ? existing : (await getNewIdentity(CONSTANTS.TERMINALS.resource))) as ResourceIdentityJson;
                    return {
                        execRef: execution.identity as ExecutionIdentityJson,
                        resourceRoleIdentity: resourceRoleIdentity as ResourceRoleIdentityJson,
                        resourceIdentity
                    };
                })
            );

            allAssignments.push(...assignments);
        }

        return allAssignments;
    }, []);

    // DOC: Shared logic for applying missing input assignments to a draft step
    const applyMissingInputAssignments = useCallback((
        draftStep: Draft<StepJson>,
        allAssignments: Array<{ execRef: ExecutionIdentityJson; resourceRoleIdentity: ResourceRoleIdentityJson; resourceIdentity: ResourceIdentityJson }>,
        draftStrategyState: Draft<StrategyStateJson>
    ) => {
        for (const { execRef, resourceRoleIdentity, resourceIdentity } of allAssignments) {
            // Initialize strategyState bucket
            if (!draftStrategyState[execRef]) {
                draftStrategyState[execRef] = {};
            }

            // Find and update the execution's roleBindings based on step kind
            let targetExecution: ExecutionJson | null = null;

            if (draftStep.kind === CONSTANTS.STEPS.work) {
                targetExecution = (draftStep as WorkStepJson).execution;
            } else if (draftStep.kind === CONSTANTS.STEPS.for || draftStep.kind === CONSTANTS.STEPS.while) {
                const loopStep = draftStep as { case?: { when?: { execution?: ExecutionJson }, what?: { execution?: ExecutionJson } } };
                if (loopStep.case?.when?.execution?.identity === execRef) {
                    targetExecution = loopStep.case.when.execution;
                } else if (loopStep.case?.what?.execution?.identity === execRef) {
                    targetExecution = loopStep.case.what.execution;
                }
            } else if (draftStep.kind === CONSTANTS.STEPS.branch) {
                const branchStep = draftStep as BranchStepJson;
                for (const caseItem of branchStep.cases ?? []) {
                    if (caseItem?.when?.execution?.identity === execRef) {
                        targetExecution = caseItem.when.execution;
                        break;
                    } else if (caseItem?.what?.execution?.identity === execRef) {
                        targetExecution = caseItem.what.execution;
                        break;
                    }
                }
            }

            if (!targetExecution) continue;

            // Update roleBindings
            targetExecution.roleBindings.inputBindingMap[resourceRoleIdentity] = resourceIdentity;

            // Create missing resource
            const role = roleMap.get(resourceRoleIdentity);
            const resourceTypeRef = (role?.resourceTypeRef ?? ('TYPE-ResourceMissing' as unknown as ResourceTypeIdentityJson)) as ResourceTypeIdentityJson;

            const missing: ResourceMissingJson = {
                identity: resourceIdentity,
                resourceTypeRef,
                kind: 'missing',
            };
            draftStrategyState[execRef][resourceRoleIdentity] = missing;
        }
    }, [roleMap]);

    const materializeMissingInputsForActiveStep = useCallback(async () => {
        if (!statefulStrategy || !activeStep) return;

        const executions = extractExecutionsFromStep(activeStep);
        if (executions.length === 0) return;

        const allAssignments = await computeMissingInputAssignments(executions, statefulStrategy.strategyState);
        if (allAssignments.length === 0) return;

        setStatefulStrategy(produce(draft => {
            if (!draft) return;

            const stepIdx = draft.statelessStrategy.steps.findIndex(s => s.identity === activeStep.identity);
            if (stepIdx === -1) return;

            applyMissingInputAssignments(draft.statelessStrategy.steps[stepIdx], allAssignments, draft.strategyState);
        }));
    }, [statefulStrategy, activeStep, setStatefulStrategy, extractExecutionsFromStep, computeMissingInputAssignments, applyMissingInputAssignments]);

    // DOC: Populate missing inputs for all executions in the last step before strategy execution
    // This ensures the engine can prompt for missing inputs during execution
    const populateLastStepWithResourceMissing = useCallback(async (strategy: StatefulStrategyJson): Promise<StatefulStrategyJson> => {
        const steps = strategy.statelessStrategy?.steps ?? [];
        if (steps.length === 0) return strategy;

        const lastStep = steps[steps.length - 1];
        const executions = extractExecutionsFromStep(lastStep);
        if (executions.length === 0) return strategy;

        const allAssignments = await computeMissingInputAssignments(executions, strategy.strategyState);
        if (allAssignments.length === 0) return strategy;

        return produce(strategy, draft => {
            const lastStepIdx = draft.statelessStrategy.steps.length - 1;
            applyMissingInputAssignments(draft.statelessStrategy.steps[lastStepIdx], allAssignments, draft.strategyState);
        });
    }, [extractExecutionsFromStep, computeMissingInputAssignments, applyMissingInputAssignments]);


    // DOC: Handler that completes the active step (materializing missing inputs), then wraps a job into a WorkStep and adds it to the strategy
    const onBuildWorkStep = useCallback(async (jobId: string) => {
        const job = jobMap.get(jobId as JobIdentityJson);
        if (!job) {
            console.warn('Job not found in jobMap:', jobId);
            return;
        }
        try {

            // If current execution still has unbound inputs, materialize ResourceMissing entries
            // so the user can continue building (Engine will prompt during execution).
            // ATTENTION: intercept here if config disallows missing inputs
            await materializeMissingInputsForActiveStep();

            const newWorkStep = await createWorkStepFromJob(job);
            if (!statefulStrategyIdentity || !statelessStrategyIdentity) return;
            setStatefulStrategy(produce(draft => {
                if (!draft) {
                    return {
                        identity: statefulStrategyIdentity as StatefulStrategyIdentityJson,
                        statelessStrategy: {
                            identity: statelessStrategyIdentity as StatelessStrategyIdentityJson,
                            steps: [newWorkStep]
                        },
                        strategyState: {}
                    };
                }
                draft.statelessStrategy.steps.push(newWorkStep);
            }));
            setSelectedIndex((prev) => (prev === null ? { stepIndex: 0, caseIndex: null } : { stepIndex: prev.stepIndex + 1, caseIndex: null }));
        } catch (err) {
            // createWorkStepFromJob already alerts for input binding issues; ignore errors
            console.warn('Failed to create workStep:', err);
        }
    }, [jobMap, materializeMissingInputsForActiveStep, statefulStrategyIdentity, statelessStrategyIdentity]);

    // DOC: Handler that completes the active step (materializing missing inputs), then wraps jobs into a BranchStep and adds it to the strategy
    const onBuildBranchStep = useCallback(async (jobIds: string[]) => {
        if (jobIds.length === 0) {
            console.warn('No jobs provided for branch step');
            return;
        }

        const whenJob = jobMap.get(CONSTANTS.SPECIALS.JOB_LessThan as JobIdentityJson);
        if (!whenJob) {
            console.warn('LessThan job not found in jobMap:', 'JOB-LessThan');
            return;
        }

        // Build job pairs for each case
        const jobPairs: { whatJob: JobJson, whenJob: JobJson }[] = [];
        for (const jobId of jobIds) {
            const whatJob = jobMap.get(jobId as JobIdentityJson);
            if (!whatJob) {
                console.warn('Job not found in jobMap:', jobId);
                continue;
            }
            jobPairs.push({ whatJob, whenJob });
        }

        if (jobPairs.length === 0) {
            console.warn('No valid jobs found for branch step');
            return;
        }

        try {
            // If current execution still has unbound inputs, materialize ResourceMissing entries
            // so the user can continue building (Engine will prompt during execution).
            // ATTENTION: intercept here if config disallows missing inputs
            await materializeMissingInputsForActiveStep();

            const newBranchStep = await createBranchStepFromJobPairs(jobPairs);

            if (!statefulStrategyIdentity || !statelessStrategyIdentity) return;

            setStatefulStrategy(produce(draft => {
                if (!draft) {
                    return {
                        identity: statefulStrategyIdentity as StatefulStrategyIdentityJson,
                        statelessStrategy: {
                            identity: statelessStrategyIdentity as StatelessStrategyIdentityJson,
                            steps: [newBranchStep]
                        },
                        strategyState: {}
                    };
                }
                draft.statelessStrategy.steps.push(newBranchStep);
            }));

            setSelectedIndex((prev) => (prev === null ? { stepIndex: 0, caseIndex: null } : { stepIndex: prev.stepIndex + 1, caseIndex: null }));
        } catch (err) {
            console.warn('Failed to create branchStep:', err);
        }
    }, [jobMap, materializeMissingInputsForActiveStep, statefulStrategyIdentity, statelessStrategyIdentity]);

    // DOC: Handler that completes the active step (materializing missing inputs), then wraps a job into a WhileStep and adds it to the strategy
    const onBuildWhileStep = useCallback(async (jobId: string) => {
        const job = jobMap.get(jobId as JobIdentityJson);
        if (!job) {
            console.warn('Job not found in jobMap:', jobId);
            return;
        }

        const whenJob = jobMap.get(CONSTANTS.SPECIALS.JOB_LessThan as JobIdentityJson);
        if (!whenJob) {
            console.warn('LessThan job not found in jobMap:', 'JOB-LessThan');
            return;
        }

        try {
            // If current execution still has unbound inputs, materialize ResourceMissing entries
            // so the user can continue building (Engine will prompt during execution).
            // ATTENTION: intercept here if config disallows missing inputs
            await materializeMissingInputsForActiveStep();

            const newWhileStep = await createLoopStepFromJob(job, whenJob, 'while');

            if (!statefulStrategyIdentity || !statelessStrategyIdentity) return;

            setStatefulStrategy(produce(draft => {
                if (!draft) {
                    return {
                        identity: statefulStrategyIdentity as StatefulStrategyIdentityJson,
                        statelessStrategy: {
                            identity: statelessStrategyIdentity as StatelessStrategyIdentityJson,
                            steps: [newWhileStep]
                        },
                        strategyState: {}
                    };
                }
                draft.statelessStrategy.steps.push(newWhileStep);
            }));

            setSelectedIndex((prev) => (prev === null ? { stepIndex: 0, caseIndex: null } : { stepIndex: prev.stepIndex + 1, caseIndex: null }));
        } catch (err) {
            console.warn('Failed to create whileStep:', err);
        }
    }, [jobMap, materializeMissingInputsForActiveStep, statefulStrategyIdentity, statelessStrategyIdentity]);

    // DOC: Handler for adding a new case to an existing BranchStep
    const onAddCaseToBranchStep = useCallback(async (stepIndex: number, jobId: string) => {
        if (!statefulStrategy) return;

        const step = statefulStrategy.statelessStrategy.steps[stepIndex];
        if (!step || step.kind !== CONSTANTS.STEPS.branch) {
            console.warn('Step is not a branch step');
            return;
        }

        const whatJob = jobMap.get(jobId as JobIdentityJson);
        if (!whatJob) {
            console.warn('Job not found in jobMap:', jobId);
            return;
        }

        const whenJob = jobMap.get(CONSTANTS.SPECIALS.JOB_LessThan as JobIdentityJson);
        if (!whenJob) {
            console.warn('LessThan job not found in jobMap:', 'JOB-LessThan');
            return;
        }

        try {
            const whatWorkStep = await createWorkStepFromJob(whatJob);
            const whenWorkStep = await createWorkStepFromJob(whenJob);

            const branchStep = step as BranchStepJson;
            const newCaseIndex = branchStep.cases.length;

            setStatefulStrategy(produce(draft => {
                if (!draft) return;
                const draftStep = draft.statelessStrategy.steps[stepIndex];
                if (draftStep.kind === CONSTANTS.STEPS.branch) {
                    const branchStep = draftStep as BranchStepJson;
                    branchStep.cases.push({
                        what: whatWorkStep,
                        when: whenWorkStep
                    });
                }
            }));

            // Auto-select the newly added case
            setSelectedIndex({ stepIndex, caseIndex: newCaseIndex });
        } catch (err) {
            console.warn('Failed to add case to branch step:', err);
        }
    }, [statefulStrategy, jobMap]);

    // DOC: Handler that completes the active step (materializing missing inputs), then wraps a job into a ForStep and adds it to the strategy
    const onBuildForStep = useCallback(async (jobId: string) => {
        const job = jobMap.get(jobId as JobIdentityJson);
        if (!job) {
            console.warn('Job not found in jobMap:', jobId);
            return;
        }

        const whenJob = jobMap.get(CONSTANTS.SPECIALS.JOB_LessThan as JobIdentityJson);
        if (!whenJob) {
            console.warn('LessThan job not found in jobMap:', 'JOB-LessThan');
            return;
        }

        try {
            // If current execution still has unbound inputs, materialize ResourceMissing entries
            // so the user can continue building (Engine will prompt during execution).
            // ATTENTION: intercept here if config disallows missing inputs
            await materializeMissingInputsForActiveStep();

            const newForStep = await createLoopStepFromJob(job, whenJob, 'for');
            console.log('Created newForStep:', newForStep);

            if (!statefulStrategyIdentity || !statelessStrategyIdentity) return;

            setStatefulStrategy(produce(draft => {
                if (!draft) {
                    return {
                        identity: statefulStrategyIdentity as StatefulStrategyIdentityJson,
                        statelessStrategy: {
                            identity: statelessStrategyIdentity as StatelessStrategyIdentityJson,
                            steps: [newForStep]
                        },
                        strategyState: {}
                    };
                }
                draft.statelessStrategy.steps.push(newForStep);
            }));

            setSelectedIndex((prev) => (prev === null ? { stepIndex: 0, caseIndex: null } : { stepIndex: prev.stepIndex + 1, caseIndex: null }));
        } catch (err) {
            console.warn('Failed to create forStep:', err);
        }
    }, [jobMap, materializeMissingInputsForActiveStep, statefulStrategyIdentity, statelessStrategyIdentity]);


    // DOC: Handler for selecting a step in the strategy
    const handleSelectStep = useCallback((index: SelectedIndex) => {
        if (!statefulStrategy) return;
        setSelectedIndex(index);
    }, [statefulStrategy]);

    const handleClearSelection = useCallback(() => {
        setSelectedIndex(null);
    }, []);

    // DOC: Handler for deleting a step from the strategy
    const handleDeleteStep = useCallback((stepIndex: number) => {
        if (!statefulStrategy) return;

        const steps = statefulStrategy.statelessStrategy.steps;
        if (stepIndex < 0 || stepIndex >= steps.length) return;

        const stepToDelete = steps[stepIndex];
        const executionId = stepToDelete.kind === CONSTANTS.STEPS.work
            ? (stepToDelete as WorkStepJson).execution?.identity
            : null;

        // Calculate new steps array before state update
        const newSteps = steps.filter((_, i) => i !== stepIndex);

        setStatefulStrategy(produce(draft => {
            if (!draft) return;

            // Remove the step
            draft.statelessStrategy.steps.splice(stepIndex, 1);

            // Clean up strategyState for the deleted execution
            if (executionId) {
                delete draft.strategyState[executionId as ExecutionIdentityJson];
            }
        }));

        // Update selectedIndex - if deleted step was selected or after it, adjust selection
        setSelectedIndex((prev) => {
            if (!prev) return null;
            if (prev.stepIndex === stepIndex) {
                // If we deleted the selected step, select the previous one or null
                const newIndex = stepIndex > 0 ? stepIndex - 1 : (newSteps.length > 0 ? 0 : null);
                return newIndex !== null ? { stepIndex: newIndex, caseIndex: null } : null;
            } else if (prev.stepIndex > stepIndex) {
                // If we deleted a step before the selected one, adjust the index
                return { stepIndex: prev.stepIndex - 1, caseIndex: prev.caseIndex };
            }
            return prev;
        });
    }, [statefulStrategy]);

    const {
        onLog: handleLog,
        onRun: handleRun,
        onDispatch: handleDispatch,
        onResumeGraph: handleResumeGraph,
    } = useStrategyExecution({
        statefulStrategy,
        setStatefulStrategy,
        populateLastStepWithResourceMissing,
        mockModeEnabled,
        useLocalLanggraph,
        writeStrategyExecutionFile,
        onPersistMockNatural: persistMockNatural,
        setInterruptData,
    });

    // Memoized context values for providers
    const selectionValue = useMemo(() => ({
        activeStep,
        activeExecution,
        activeJob,
        selectedIndex,
        excludeCaseStepIds
    }), [activeStep, activeExecution, activeJob, selectedIndex, excludeCaseStepIds]);

    const strategyValue: StrategyContextValue = useMemo(() => ({
        statefulStrategy: statefulStrategy!,
        strategyName,
        roleMap,
        executionMap
    }), [statefulStrategy, strategyName, roleMap, executionMap]);

    const bindingsValue = useMemo(() => ({
        onbindInputRes: bindInputRes,
        onbindInputRef: bindInputRef,
        onClearInputBinding: clearInputBinding
    }), [bindInputRes, bindInputRef, clearInputBinding]);

    const actionsValue = useMemo(() => ({
        interruptData,
        onStrategyNameChange: setStrategyName,
        onLog: handleLog,
        onRun: handleRun,
        onDispatch: handleDispatch,
        onSelectStep: handleSelectStep,
        onClearSelection: handleClearSelection,
        onDeleteStep: handleDeleteStep,
        onBuildWorkStep,
        onBuildBranchStep,
        onAddCaseToBranchStep,
        onBuildWhileStep,
        onBuildForStep,
        onResumeGraph: handleResumeGraph,
        setInterruptData
    }), [
        interruptData,
        handleLog,
        handleRun,
        handleDispatch,
        handleSelectStep,
        handleClearSelection,
        handleDeleteStep,
        onBuildWorkStep,
        onBuildBranchStep,
        onAddCaseToBranchStep,
        onBuildWhileStep,
        onBuildForStep,
        handleResumeGraph,
    ]);

    if (!statefulStrategy) {
        return null;
    }

    return (
        <StrategyProviders
            selection={selectionValue}
            strategy={strategyValue}
            bindings={bindingsValue}
            actions={actionsValue}
        >
            {children}
        </StrategyProviders>
    );
}
