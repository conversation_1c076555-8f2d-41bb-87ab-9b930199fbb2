'use client';

import type { ExecutionIdentityJson, ResourceTypeJson, ResourceIdentityJson, Resource_ResourceTypeJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateResource } from '@toolproof-npm/validation';
import { DropDown } from '@/builders/_lib/DropDown';
import { SampleResourceSection } from '@/builders/shared/SampleResourceSection';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import { usePrefetchedIdentities } from '@/builders/_lib/usePrefetchedIdentities';
import { uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import SaveControls from '@/builders/_lib/SaveControls';
import type { ErrorObject } from 'ajv';
import { useMemo, useState, useEffect } from 'react';


interface ResourceBuilderProps {
    resourceResourceTypeMap: Record<string, Resource_ResourceTypeJson>;
}

export default function ResourceBuilder({ resourceResourceTypeMap }: ResourceBuilderProps) {
    const { values: ids } = usePrefetchedIdentities({
        identity: { terminal: CONSTANTS.TERMINALS.resource },
        executionIdentity: { terminal: CONSTANTS.TERMINALS.execution },
    });
    const identity = ids.identity;
    const executionIdentity = ids.executionIdentity;
    const [selectedType, setSelectedType] = useState<ResourceTypeJson>();
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [resource, setResource] = useState<unknown>('');
    const [resourceText, setResourceText] = useState<string>(JSON.stringify('', null, 2));
    const [resourceParseError, setResourceParseError] = useState<string | null>(null);
    const [uploadedFileInfo, setUploadedFileInfo] = useState<{ message: string, content: string } | null>(null);

    // console.log('selectedType:', JSON.stringify(selectedType, null, 2));

    const resourceTypeResources = useMemo(() => {
        return Object.values(resourceResourceTypeMap).map((res) => ({
            ...res,
        }));
    }, [resourceResourceTypeMap]);

    // DOC: Fetch new identities (handled by usePrefetchedIdentities)

    // DOC: When dependent resourceShapes arrive, auto-select the first
    useEffect(() => {
        if (!selectedType && resourceTypeResources.length) {
            setSelectedType(resourceTypeResources[0].extractedData);
        }
    }, [resourceTypeResources, selectedType]);

    // DOC: Validate resource against extractionSchema of selectedType
    const { isValid, errors: errors } = useMemo(() => {
        if (!selectedType) {
            console.log('No selectedType, cannot validate');
            return { isValid: false, errors: null as ErrorObject[] | null };
        }
        return validateResource(selectedType.extractionSchema, resource);
    }, [resource, selectedType]);

    // DOC: Handle file upload and read content
    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        try {
            const text = await file.text();
            const lines = text.split('\n');
            const firstLine = lines[0] || '';

            const fileInfo = {
                message: `File uploaded: ${file.name} (${file.size} bytes)`,
                content: firstLine
            };

            setUploadedFileInfo(fileInfo);

            // Optionally, set the file content as the resource text
            // setResourceText(text);
            // handleResourceChange(text);
        } catch (error) {
            setUploadedFileInfo({
                message: `Error reading file: ${(error as Error).message}`,
                content: ''
            });
        }
    };

    // DOC: Update resource state on text change, with parse error handling
    const handleResourceChange = (text: string) => {
        setResourceText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setResourceParseError('resource must be a JSON object.');
                return;
            }
            setResource(parsed);
            setResourceParseError(null);
        } catch (e) {
            setResourceParseError((e as Error).message);
        }
    };

    // DOC: Upload the resource upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        if (!selectedType) return;
        setIsSaving(true);
        setSaveStatus(null);
        try {
            const res = (await uploadResource(
                {
                    identity: identity as ResourceIdentityJson,
                    resourceTypeRef: selectedType.identity,
                    creationContext: {
                        resourceRoleRef: CONSTANTS.SPECIALS.ROLE_Manual,
                        executionRef: executionIdentity as ExecutionIdentityJson,
                    }
                },
                resource
            ));
            if (res.success) {
                setSaveStatus(`✓ Successfully saved at: ${res.path}`);
            } else {
                setSaveStatus(`✗ Save failed: ${res.error ?? 'Unknown error'}`);
            }
        } catch (err) {
            setSaveStatus(`✗ Save failed: ${(err as Error).message ?? 'Unknown error'}`);
        } finally {
            setIsSaving(false);
        }
    };

    // Collect all validation errors for summary
    const allErrors = [
        ...(!selectedType ? ['Type: Please select a Type'] : []),
        ...(resourceParseError ? [`Resource JSON: ${resourceParseError}`] : []),
        ...(errors && errors.length > 0 ? errors.map(e => `Schema validation: ${e.instancePath || 'root'} ${e.message}`) : []),
    ];

    return (
        <div className="space-y-8">
            {/* Validation Summary Banner */}
            {!isValid && allErrors.length > 0 && (
                <div className="bg-red-50 border-l-4 border-red-400 rounded-r-lg p-4 shadow-sm">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3 flex-1">
                            <h3 className="text-sm font-semibold text-red-600 mb-2">
                                Please fix the following errors before saving:
                            </h3>
                            <ul className="list-disc list-inside space-y-1 text-sm text-red-600">
                                {allErrors.slice(0, 5).map((error, idx) => (
                                    <li key={idx}>{error}</li>
                                ))}
                                {allErrors.length > 5 && (
                                    <li className="text-red-600 italic">...and {allErrors.length - 5} more error(s)</li>
                                )}
                            </ul>
                        </div>
                    </div>
                </div>
            )}

            <form id="resource-form" onSubmit={handleSubmit} className="space-y-8">
                {/* Basic Information Section */}
                <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm w-full">
                    <h2 className="text-lg font-semibold text-gray-900 mb-6 pb-3 border-b border-gray-200">
                        Basic Information
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* DOC: 'executionId' is generated server-side */}
                        <ReadOnlyIdField value={executionIdentity} />
                        <DropDown
                            items={resourceTypeResources}
                            value={selectedType?.identity ?? ''}
                            label='Type'
                            loading={false}
                            onChange={(newResourceTypeId) => {
                                console.log('DropDown onChange triggered');
                                console.log('newResourceTypeId:', newResourceTypeId);
                                console.log(
                                    'Available resourceTypeResources:',
                                    resourceTypeResources.map((r) => ({
                                        identity: r.identity,
                                        extractedIdentity: r.extractedData.identity,
                                        name: r.extractedData.name,
                                    }))
                                );
                                const newResourceTypeResource = (resourceTypeResources ?? []).find(
                                    (type) => type.extractedData.identity === newResourceTypeId
                                );
                                console.log('Found newResourceTypeResource:', newResourceTypeResource?.identity);
                                console.log('extractedData to set:', newResourceTypeResource?.extractedData);
                                setSelectedType(newResourceTypeResource?.extractedData);
                            }}
                        />
                    </div>
                </section>

                {/* Resource Editor & File Upload Section */}
                <div className="grid grid-cols-1 lg:grid-cols-[1.2fr_1fr] xl:grid-cols-[1.4fr_1fr] gap-6 w-full">
                    {/* Resource Editor Section */}
                    <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                        <div className="mb-6 pb-3 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <h2 className="text-lg font-semibold text-gray-900">
                                    Resource
                                </h2>
                                {isValid && resource != null && !resourceParseError && selectedType && (
                                    <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full font-medium">
                                        ✓ Valid
                                    </span>
                                )}
                                {resourceParseError && (
                                    <span className="text-xs px-2 py-1 bg-red-100 text-red-600 rounded-full font-medium">
                                        Invalid JSON
                                    </span>
                                )}
                            </div>
                        </div>
                        <SampleResourceSection
                            legend="resource (select a Type to validate against its extractionSchema)"
                            valueText={resourceText}
                            onChangeText={handleResourceChange}
                            parseError={resourceParseError}
                            previewTitle="Preview Resource"
                            previewData={resource}
                            validationErrors={errors}
                            isValid={isValid && !!selectedType}
                            heightClass="h-64"
                        />
                    </section>

                    {/* File Upload Section */}
                    <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                        <div className="mb-6 pb-3 border-b border-gray-200">
                            <h2 className="text-lg font-semibold text-gray-900">
                                File Upload
                            </h2>
                        </div>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Upload a file
                                </label>
                                <input
                                    type="file"
                                    onChange={handleFileUpload}
                                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                    accept=".json,.txt,.js,.ts,.jsx,.tsx,.py,.md"
                                />
                            </div>
                            {uploadedFileInfo && (
                                <div className="bg-gray-100 rounded-lg border border-gray-300 p-3">
                                    <h4 className="text-sm font-semibold text-gray-700 mb-2">
                                        File Upload Info
                                    </h4>
                                    <pre className="bg-gray-200 rounded p-4 overflow-auto text-xs font-mono text-gray-600 whitespace-pre-wrap leading-relaxed">
                                        {JSON.stringify(uploadedFileInfo, null, 2)}
                                    </pre>
                                </div>
                            )}
                        </div>
                    </section>
                </div>
            </form>

            {/* Save Controls */}
            <SaveControls
                formId="resource-form"
                buttonText="Save Resource"
                disabled={!isValid || !selectedType}
                isValid={isValid}
                invalidMessage={isValid ? undefined : 'Fix errors above before saving.'}
                saveStatus={saveStatus}
                isLoading={isSaving}
                className="mt-8"
            />
        </div>
    );
}
