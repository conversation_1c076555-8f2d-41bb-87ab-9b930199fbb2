import { ResourceJson } from '@toolproof-npm/schema';

export function DropDown<T extends ResourceJson>(props: {
  items: T[];
  value: string;
  label: string
  onChange: (id: string) => void;
  loading?: boolean;
}) {
  const { items, value, label, onChange, loading } = props;
  return (
    <div>
      <label className="block text-sm font-semibold text-gray-700 mb-2">{label}</label>
      <select
        className="w-full rounded-lg border border-gray-300 px-4 py-2.5 text-sm text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-[#7A0019] focus:border-transparent transition-all appearance-none bg-[url('data:image/svg+xml;charset=UTF-8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22%3E%3Cpolyline points=%226 9 12 15 18 9%22%3E%3C/polyline%3E%3C/svg%3E')] bg-no-repeat bg-right pr-10"
        value={value}
        onChange={(e) => onChange(e.target.value)}
      >
        {loading ? (
          <option>Loading…</option>
        ) : (
          items.map((item) => (
            <option key={item.identity} value={item.extractedData.identity}>
              {String(item.extractedData.name)}
            </option>
          ))
        )}
      </select>
    </div>
  );
}
