'use client';

import type { ReactNode } from 'react';
import SaveControls, { type SaveControlsProps } from '@/builders/_lib/SaveControls';

export type BuilderShellProps = {
    formId: string;
    onSubmit: (e: React.FormEvent) => void | Promise<void>;
    children: ReactNode;
    afterForm?: ReactNode;
    save: SaveControlsProps;
    className?: string;
};

export function BuilderShell({ formId, onSubmit, children, afterForm, save, className }: BuilderShellProps) {
    return (
        <div className={className}>
            <form id={formId} onSubmit={onSubmit} className='space-y-4'>
                {children}
            </form>

            {afterForm}

            <SaveControls {...save} formId={formId} className={save.className ?? 'mt-4'} />
        </div>
    );
}
