'use client';

export type SaveControlsProps = {
    formId: string;
    buttonText: string;
    disabled: boolean;
    isValid: boolean;
    invalidMessage?: string;
    error?: string | null;
    saveStatus?: string | null;
    isLoading?: boolean;
    className?: string;
};

export default function SaveControls({
    formId,
    buttonText,
    disabled,
    isValid,
    invalidMessage,
    error,
    saveStatus,
    isLoading = false,
    className,
}: SaveControlsProps) {
    return (
        <div className={`flex flex-col sm:flex-row gap-3 items-start sm:items-center ${className ?? ''}`}>
            <button
                form={formId}
                type="submit"
                className={`px-6 py-2.5 rounded-lg font-semibold text-sm transition-all inline-flex items-center gap-2 ${
                    disabled || isLoading
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-[#7A0019] text-white hover:bg-[#5A0013] shadow-md hover:shadow-lg'
                }`}
                disabled={disabled || isLoading}
            >
                {isLoading && (
                    <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                )}
                {isLoading ? 'Saving...' : buttonText}
            </button>
            <div className="flex flex-col gap-1">
                {!isValid && invalidMessage && (
                    <span className="text-sm text-red-600 font-medium">{invalidMessage}</span>
                )}
                {error && <span className="text-sm text-red-600 font-medium">{error}</span>}
                {saveStatus && (
                    <span className={`text-sm ${saveStatus.includes('failed') ? 'text-red-600' : 'text-green-600'} font-medium`}>
                        {saveStatus}
                    </span>
                )}
            </div>
        </div>
    );
}
