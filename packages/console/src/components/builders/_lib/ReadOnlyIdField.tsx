'use client';

export type ReadOnlyIdFieldProps = {
  value: string;
  label?: string;
  generatingText?: string;
  className?: string;
  inputClassName?: string;
};

export function ReadOnlyIdField({
  value,
  label = 'ID',
  generatingText = 'Generating…',
  className,
  inputClassName,
}: ReadOnlyIdFieldProps) {
  return (
    <div className={className}>
      <label className="block text-sm font-semibold text-gray-700 mb-2">{label}</label>
      <input
        className={`w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-2.5 text-sm text-gray-600 font-mono cursor-not-allowed ${inputClassName ?? ''}`}
        value={value || generatingText}
        readOnly
      />
    </div>
  );
}

export default ReadOnlyIdField;
