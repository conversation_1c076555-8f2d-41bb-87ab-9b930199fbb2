'use client';

import { useEffect, useMemo, useState } from 'react';
import { getNewIdentity } from '@/_lib/server/firebaseAdminHelpers';

export type PrefetchedIdentityConfig = {
    terminal: Parameters<typeof getNewIdentity>[0];
    initial?: string;
};

export type UsePrefetchedIdentitiesOptions<K extends string> = Record<K, PrefetchedIdentityConfig>;

/**
 * Prefetch multiple identities (possibly from different terminals) in one hook.
 *
 * - Fetches only missing ids.
 * - Uses a cancellation guard to avoid setting state after unmount.
 */
export function usePrefetchedIdentities<K extends string>(options: UsePrefetchedIdentitiesOptions<K>) {
    const configSignature = useMemo(() => {
        const entries = Object.entries(options) as Array<[K, PrefetchedIdentityConfig]>;
        // Use a deterministic, value-based signature so effects don't retrigger on re-renders.
        return entries
            .map(([k, v]) => `${String(k)}::${String(v.terminal)}::${v.initial ?? ''}`)
            .sort()
            .join('||');
    }, [options]);

    const parsed = useMemo(() => {
        const entries = configSignature ? configSignature.split('||') : [];
        return entries
            .map((s) => {
                const [keyRaw, terminalRaw, initialRaw] = s.split('::');
                return {
                    key: keyRaw as K,
                    terminal: terminalRaw as Parameters<typeof getNewIdentity>[0],
                    initial: initialRaw ?? '',
                };
            })
            .filter((e) => Boolean(e.key) && Boolean(e.terminal));
    }, [configSignature]);

    const keys = useMemo(() => parsed.map((e) => e.key), [parsed]);

    const [values, setValues] = useState<Record<K, string>>(() => {
        const out: Record<string, string> = {};
        for (const e of parsed) out[String(e.key)] = e.initial ?? '';
        return out as Record<K, string>;
    });

    useEffect(() => {
        let cancelled = false;

        const asyncWrapper = async () => {
            const missing = keys.filter((k) => !values[k]);
            if (missing.length === 0) return;

            const terminalByKey = new Map<K, Parameters<typeof getNewIdentity>[0]>();
            for (const e of parsed) terminalByKey.set(e.key, e.terminal);

            const fetched = await Promise.all(
                missing.map(async (k) => {
                    const terminal = terminalByKey.get(k);
                    if (!terminal) return [k, ''] as const;
                    const id = await getNewIdentity(terminal);
                    return [k, id] as const;
                })
            );

            if (cancelled) return;

            setValues((prev) => {
                const next = { ...prev };
                for (const [k, id] of fetched) {
                    if (!next[k] && id) next[k] = id;
                }
                return next;
            });
        };

        asyncWrapper();

        return () => {
            cancelled = true;
        };
    }, [configSignature, keys, parsed, values]);

    return { values, setValues };
}
