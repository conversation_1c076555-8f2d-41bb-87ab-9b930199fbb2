'use client';

export type LabeledCheckboxProps = {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
};

export function LabeledCheckbox({ label, checked, onChange, className }: LabeledCheckboxProps) {
  return (
    <div className={`flex items-center space-x-3 ${className ?? ''}`}>
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        className="h-4 w-4 text-[#7A0019] border-gray-300 rounded focus:ring-[#7A0019] focus:ring-2 cursor-pointer"
      />
      <label className="text-sm font-medium text-gray-700 cursor-pointer">
        {label}
      </label>
    </div>
  );
}

export default LabeledCheckbox;
