'use client';

import type { ReactNode } from 'react';
import type { ErrorObject } from 'ajv';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';

export type BuilderPreviewProps = {
    title: ReactNode;
    value: unknown;
    loading?: boolean;
    errors?: ErrorObject[] | null | undefined;
    footer?: ReactNode;
    heightClass?: string;
    className?: string;
};

export function BuilderPreview({
    title,
    value,
    loading,
    errors,
    footer,
    heightClass,
    className,
}: BuilderPreviewProps) {
    return (
        <section className={className}>
            <h3 className='font-semibold mb-2'>{title}</h3>
            <pre className={`bg-gray-100 p-3 rounded overflow-auto text-sm ${heightClass ?? 'h-64'}`}>
                {loading ? 'Loading…' : JSON.stringify(value, null, 2)}
            </pre>
            {footer}
            <ValidationErrors errors={errors} />
        </section>
    );
}
