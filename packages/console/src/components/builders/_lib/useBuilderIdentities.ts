'use client';

import { useEffect, useState } from 'react';
import { getNewIdentity } from '@/_lib/server/firebaseAdminHelpers';

type IdentityConfig = {
    terminal: Parameters<typeof getNewIdentity>[0];
    initial?: string;
};

export type UseBuilderIdentitiesOptions = {
    identity?: IdentityConfig;
    resourceIdentity?: IdentityConfig;
    executionIdentity?: IdentityConfig;
};

export function useBuilderIdentities(options: UseBuilderIdentitiesOptions) {
    const [identity, setIdentity] = useState<string>(options.identity?.initial ?? '');
    const [resourceIdentity, setResourceIdentity] = useState<string>(options.resourceIdentity?.initial ?? '');
    const [executionIdentity, setExecutionIdentity] = useState<string>(options.executionIdentity?.initial ?? '');

    const identityTerminal = options.identity?.terminal;
    const resourceIdentityTerminal = options.resourceIdentity?.terminal;
    const executionIdentityTerminal = options.executionIdentity?.terminal;

    useEffect(() => {
        let cancelled = false;

        const asyncWrapper = async () => {
            const needsIdentity = Boolean(identityTerminal);
            const needsResourceIdentity = Boolean(resourceIdentityTerminal);
            const needsExecutionIdentity = Boolean(executionIdentityTerminal);

            const hasAll =
                (!needsIdentity || Boolean(identity)) &&
                (!needsResourceIdentity || Boolean(resourceIdentity)) &&
                (!needsExecutionIdentity || Boolean(executionIdentity));

            if (hasAll) return;

            const [newIdentity, newResourceIdentity, newExecutionIdentity] = await Promise.all([
                !needsIdentity || identity ? Promise.resolve<string | null>(null) : getNewIdentity(identityTerminal!),
                !needsResourceIdentity || resourceIdentity
                    ? Promise.resolve<string | null>(null)
                    : getNewIdentity(resourceIdentityTerminal!),
                !needsExecutionIdentity || executionIdentity
                    ? Promise.resolve<string | null>(null)
                    : getNewIdentity(executionIdentityTerminal!),
            ]);

            if (cancelled) return;

            if (!identity && newIdentity) setIdentity(newIdentity);
            if (!resourceIdentity && newResourceIdentity) setResourceIdentity(newResourceIdentity);
            if (!executionIdentity && newExecutionIdentity) setExecutionIdentity(newExecutionIdentity);
        };

        asyncWrapper();

        return () => {
            cancelled = true;
        };
    }, [
        identity,
        resourceIdentity,
        executionIdentity,
        identityTerminal,
        resourceIdentityTerminal,
        executionIdentityTerminal,
    ]);

    return {
        identity,
        setIdentity,
        resourceIdentity,
        setResourceIdentity,
        executionIdentity,
        setExecutionIdentity,
    };
}
