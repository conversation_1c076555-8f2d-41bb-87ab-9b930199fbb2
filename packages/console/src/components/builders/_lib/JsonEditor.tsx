'use client';

import { useState, useRef, useEffect } from 'react';

export type JsonEditorProps = {
  legend?: string;
  valueText: string;
  onChangeText: (v: string) => void;
  parseError: string | null;
  heightClass?: string; // Tailwind height utility, e.g., 'h-64'
};

export function JsonEditor(props: JsonEditorProps) {
  const { legend, valueText, onChangeText, parseError, heightClass = 'h-64' } = props;
  const [lineCount, setLineCount] = useState<number>(1);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const lineNumbersRef = useRef<HTMLDivElement>(null);

  // Calculate line numbers based on content
  useEffect(() => {
    const lines = valueText.split('\n').length || 1;
    setLineCount(lines);
  }, [valueText]);

  // Sync scroll between textarea and line numbers
  const handleScroll = () => {
    if (textareaRef.current && lineNumbersRef.current) {
      lineNumbersRef.current.scrollTop = textareaRef.current.scrollTop;
    }
  };

  const formatJson = () => {
    try {
      const parsed = JSON.parse(valueText);
      const pretty = JSON.stringify(parsed, null, 2);
      onChangeText(pretty);
    } catch {
      // parent shows parseError; ignore here
    }
  };

  const isEmpty = !valueText || valueText.trim() === '';

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between gap-4 min-w-0">
        <label className="text-sm font-semibold text-gray-900 truncate flex-1 min-w-0">{legend ?? 'JSON Editor'}</label>
        <div className="flex items-center gap-2">
          {!isEmpty && (
            <button
              type="button"
              className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-white text-[#7A0019] border border-[#7A0019] hover:bg-[#7A0019] hover:text-white active:bg-[#5A0013] active:border-[#5A0013] rounded-md transition-all duration-150 font-medium text-xs focus:outline-none focus:ring-2 focus:ring-[#7A0019] focus:ring-offset-1 flex-shrink-0 whitespace-nowrap"
              onClick={formatJson}
              title="Pretty-print JSON with indentation"
            >
              <svg className="w-3.5 h-3.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
              <span className="whitespace-nowrap">Format</span>
            </button>
          )}
          <div className="text-xs text-gray-500 font-medium px-2 py-1 bg-gray-100 rounded border border-gray-200">
            {isEmpty ? 'Ready to edit' : `${valueText.split('\n').length} line${valueText.split('\n').length !== 1 ? 's' : ''}`}
          </div>
        </div>
      </div>
      
      {/* Code Editor Container */}
      <div className="relative bg-white rounded-lg border border-gray-300 focus-within:border-[#7A0019] focus-within:ring-1 focus-within:ring-[#7A0019] transition-all overflow-hidden">
        {/* Line Numbers */}
        <div 
          ref={lineNumbersRef}
          className="absolute left-0 top-0 bottom-0 w-12 bg-gray-50 border-r border-gray-200 pointer-events-none z-10 overflow-y-scroll [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"
          style={{ 
            paddingTop: '0.75rem', 
            paddingRight: '0.75rem', 
            paddingBottom: '0.75rem'
          }}
        >
          <div
            className="flex flex-col"
            style={{ 
              lineHeight: '1.5rem',
              fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Menlo, Consolas, "Liberation Mono", monospace'
            }}
          >
            {Array.from({ length: lineCount }, (_, i) => (
              <div
                key={i}
                className="text-xs font-mono text-gray-400 select-none text-right"
                style={{ lineHeight: '1.5rem' }}
              >
                {i + 1}
              </div>
            ))}
          </div>
        </div>
        
        {/* Textarea */}
        <textarea
          ref={textareaRef}
          className={`font-mono w-full pl-14 pr-4 py-3 bg-transparent text-sm text-gray-900 focus:outline-none resize-none ${heightClass}`}
          value={valueText}
          onChange={(e) => onChangeText(e.target.value)}
          onScroll={handleScroll}
          spellCheck={false}
          placeholder={isEmpty ? '{\n  "example": "Start typing your JSON here..."\n}' : ''}
          style={{ 
            tabSize: 2,
            lineHeight: '1.5rem',
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Menlo, Consolas, "Liberation Mono", monospace'
          }}
        />
        
        {/* Empty State Overlay */}
        {isEmpty && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none pl-14">
            <div className="text-center px-4">
              <div className="text-gray-400 text-sm font-medium mb-1">JSON Editor</div>
              <div className="text-gray-300 text-xs">Click here to start editing</div>
            </div>
          </div>
        )}
      </div>
      
      {parseError && (
        <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md px-3 py-2 flex items-start gap-2">
          <svg className="w-4 h-4 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <span className="font-medium">Invalid JSON:</span> {parseError}
          </div>
        </div>
      )}
    </div>
  );
}
