'use client'
import { useState, useRef, useEffect } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useAppDispatch, useAppSelector } from '@/_lib/client/redux/hooks'
import { setEnforceTeamRoutes } from '@/_lib/client/redux/features/configSlice'

export function UserDropdown() {
  const { data: session } = useSession()
  const router = useRouter()
  const dispatch = useAppDispatch()
  const enforceTeamRoutes = useAppSelector((state) => state.config.enforceTeamRoutes)
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const handleLogout = async () => {
    setIsOpen(false)
    await signOut({ redirect: false })
    router.push('/auth')
    router.refresh()
  }

  const handleProfile = () => {
    setIsOpen(false)
    router.push('/profile')
  }

  // Generate initials for avatar
  const getInitials = (name: string | null | undefined, email: string | null | undefined) => {
    if (name) {
      return name
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2)
    }
    if (email) {
      return email[0].toUpperCase()
    }
    return 'U'
  }

  if (!session?.user) return null

  const initials = getInitials(session.user.name, session.user.email)
  const canAccessRestrictedRoutes = session.user.isTeamMember || (process.env.NODE_ENV !== 'production' && !enforceTeamRoutes)

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 hover:opacity-90 transition-opacity focus:outline-none"
      >
        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#7A0019] to-[#FFCC33] flex items-center justify-center text-white text-sm font-bold shadow-md hover:shadow-lg transition-shadow">
          {initials}
        </div>
        <svg
          className={`w-4 h-4 text-[#7A0019] transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg border border-[#FFCC33] z-50">
          <div className="py-2">
            {/* User Info Section */}
            <div className="px-4 py-3 border-b border-[#FFCC33]">
              <p className="text-sm font-medium text-[#7A0019]">
                {session.user?.name || 'User'}
              </p>
              <p className="text-xs text-gray-600 mt-1">
                {session.user?.email}
              </p>
            </div>

            {/* Menu Items */}
            <div className="py-1">
              <button
                onClick={handleProfile}
                className="w-full text-left px-4 py-2 text-sm text-[#7A0019] hover:bg-[#7A0019]/10 transition-colors flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Profile
              </button>

              {process.env.NODE_ENV !== 'production' && (
                <label className="w-full px-4 py-2 text-sm text-gray-700 flex items-center justify-between gap-3 select-none">
                  <span title="Dev-only: disables team-member gate for /builders and /admin">Enforce team-only routes</span>
                  <input
                    type="checkbox"
                    checked={enforceTeamRoutes}
                    onChange={(e) => dispatch(setEnforceTeamRoutes(e.target.checked))}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                </label>
              )}

              {canAccessRestrictedRoutes && (
                <>
                  <button
                    onClick={() => {
                      setIsOpen(false)
                      router.push('/builders')
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-[#7A0019] hover:bg-[#7A0019]/10 transition-colors flex items-center gap-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                    Builders
                  </button>
                  <button
                    onClick={() => {
                      setIsOpen(false)
                      router.push('/admin')
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-[#7A0019] hover:bg-[#7A0019]/10 transition-colors flex items-center gap-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Admin
                  </button>
                </>
              )}
              <button
                onClick={handleLogout}
                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Logout
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

