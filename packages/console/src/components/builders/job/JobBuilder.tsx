'use client';

import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ResourceTypeIdentity<PERSON>son, ExecutionIdentity<PERSON>son, ResourceRoleIdentity<PERSON>son, RoleMapJson, JobIdentityJson, JobJson, ResourceRoleValueJson, ExtractionSchemaJson, Resource_ResourceTypeJson } from '@toolproof-npm/schema';
import { SchemaJob } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateResource } from '@toolproof-npm/validation';
import { LabeledInput } from '../_lib/LabeledInput';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import { usePrefetchedIdentities } from '@/builders/_lib/usePrefetchedIdentities';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import { getNewIdentity, uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import SaveControls from '@/builders/_lib/SaveControls';
import type { ErrorObject } from 'ajv';
import { useState, useEffect, useMemo } from 'react';

interface JobBuilderProps {
    resourceResourceTypeMap: Record<string, Resource_ResourceTypeJson>;
}

export default function JobBuilder({ resourceResourceTypeMap }: JobBuilderProps) {
    const { values: ids } = usePrefetchedIdentities({
        identity: { terminal: CONSTANTS.TERMINALS.job },
        resourceIdentity: { terminal: CONSTANTS.TERMINALS.resource },
        executionIdentity: { terminal: CONSTANTS.TERMINALS.execution },
    });
    const identity = ids.identity;
    const resourceIdentity = ids.resourceIdentity;
    const executionIdentity = ids.executionIdentity;
    const [name, setName] = useState('');
    const [description, setDescription] = useState<string>('dummy-description');
    const [implementationUri, setImplementationUri] = useState<string>('');
    const [inputMap, setInputMap] = useState<RoleMapJson>({});
    const [outputMap, setOutputMap] = useState<RoleMapJson>({});
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [newInputResourceTypeIdentity, setNewInputResourceTypeId] = useState<string>('');
    const [newOutputResourceTypeIdentity, setNewOutputResourceTypeId] = useState<string>('');
    const [job, setJob] = useState<unknown>('');
    const [jobText, setJobText] = useState<string>(JSON.stringify('', null, 2));
    const [jobParseError, setJobParseError] = useState<string | null>(null);

    const resourceTypeResources = useMemo(() => {
        return Object.values(resourceResourceTypeMap).map((res) => ({
            ...res,
        }));
    }, [resourceResourceTypeMap]);

    // DOC: Fetch new identities (handled by usePrefetchedIdentities)

    // DOC: Initialize default selections for add-dropdowns when types load
    useEffect(() => {
        if (resourceTypeResources.length) {
            setNewInputResourceTypeId((prev) => prev || resourceTypeResources[0].extractedData.identity);
            setNewOutputResourceTypeId((prev) => prev || resourceTypeResources[0].extractedData.identity);
        }
    }, [resourceTypeResources]);

    // DOC: Validate job against extractionSchema of selectedType
    const { isValid, errors: errors } = useMemo(() => {
        return validateResource(SchemaJob as ExtractionSchemaJson, job); // ATTENTION
    }, [job]);

    // console.log('JobBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    // DOC: Add/Remove/Update role helpers for inputs and outputs
    const addRole = async (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceTypeRef: string,
    ) => {
        const resourceRoleIdentity = await getNewIdentity(CONSTANTS.TERMINALS.role) as ResourceRoleIdentityJson; // ATTENTION
        const newRole: ResourceRoleValueJson = {
            resourceTypeRef: resourceTypeRef as ResourceTypeIdentityJson,
            name: '',
            description: '',
        };
        setCurrent({ ...current, [resourceRoleIdentity]: newRole as unknown as RoleMapJson[keyof RoleMapJson] }); // ATTENTION
    };

    // Update helpers for individual fields on a role literal
    const updateRoleType = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceRoleId: ResourceRoleIdentityJson,
        resourceTypeId: ResourceTypeIdentityJson,
    ) => {
        const prev = current[resourceRoleId] as unknown as ResourceRoleValueJson | undefined;
        if (!prev) return;
        setCurrent({
            ...current,
            [resourceRoleId]: { ...prev, resourceTypeId } as unknown as RoleMapJson[keyof RoleMapJson],
        });
    };

    const updateRoleName = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceRoleIdentity: ResourceRoleIdentityJson,
        name: string,
    ) => {
        const prev = current[resourceRoleIdentity] as unknown as ResourceRoleValueJson | undefined;
        if (!prev) return;
        setCurrent({
            ...current,
            [resourceRoleIdentity]: { ...prev, name } as unknown as RoleMapJson[keyof RoleMapJson],
        });
    };

    const updateRoleDescription = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceRoleIdentity: ResourceRoleIdentityJson,
        description: string,
    ) => {
        const prev = current[resourceRoleIdentity] as unknown as ResourceRoleValueJson | undefined;
        if (!prev) return;
        setCurrent({
            ...current,
            [resourceRoleIdentity]: { ...prev, description } as unknown as RoleMapJson[keyof RoleMapJson],
        });
    };

    const removeRole = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceRoleIdentity: ResourceRoleIdentityJson,
    ) => {
        const { [resourceRoleIdentity]: _removed, ...rest } = current;
        setCurrent(rest);
    };

    // DOC: Update job state on text change, with parse error handling
    const handleJobChange = (text: string) => {
        setJobText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setJobParseError('job must be a JSON object.');
                return;
            }
            setJob(parsed);
            setJobParseError(null);
        } catch (e) {
            setJobParseError((e as Error).message);
        }
    };

    // DOC: Auto-build job JSON whenever form fields change
    useEffect(() => {
        const jobObj: JobJson = {
            identity: identity as JobIdentityJson,
            name,
            description,
            implementationUri,
            roles: {
                inputMap,
                outputMap,
            },
        };
        handleJobChange(JSON.stringify(jobObj, null, 2));
    }, [identity, name, description, implementationUri, executionIdentity, inputMap, outputMap]);

    // DOC: Upload the job (as a resource) upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        setIsSaving(true);
        setSaveStatus(null);
        try {
            const res = (await uploadResource(
                {
                    identity: resourceIdentity as ResourceIdentityJson,
                    resourceTypeRef: CONSTANTS.SPECIALS.TYPE_Job,
                    creationContext: {
                        resourceRoleRef: CONSTANTS.SPECIALS.ROLE_Manual as ResourceRoleIdentityJson,
                        executionRef: executionIdentity as ExecutionIdentityJson
                    },
                },
                job as unknown as import('@toolproof-npm/schema').JsonDataJson
            ));
            if (res.success) {
                setSaveStatus(`✓ Successfully saved at: ${res.path}`);
            } else {
                setSaveStatus(`✗ Save failed: ${res.error ?? 'Unknown error'}`);
            }
        } catch (err) {
            setSaveStatus(`✗ Save failed: ${(err as Error).message ?? 'Unknown error'}`);
        } finally {
            setIsSaving(false);
        }
    };

    // Collect all validation errors for summary
    const allErrors = [
        ...(jobParseError ? [`Job JSON: ${jobParseError}`] : []),
        ...(errors && errors.length > 0 ? errors.map(e => `Schema validation: ${e.instancePath || 'root'} ${e.message}`) : []),
    ];

    return (
        <div className="space-y-8">
            {/* Validation Summary Banner */}
            {!isValid && allErrors.length > 0 && (
                <div className="bg-red-50 border-l-4 border-red-400 rounded-r-lg p-4 shadow-sm">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3 flex-1">
                            <h3 className="text-sm font-semibold text-red-600 mb-2">
                                Please fix the following errors before saving:
                            </h3>
                            <ul className="list-disc list-inside space-y-1 text-sm text-red-600">
                                {allErrors.slice(0, 5).map((error, idx) => (
                                    <li key={idx}>{error}</li>
                                ))}
                                {allErrors.length > 5 && (
                                    <li className="text-red-600 italic">...and {allErrors.length - 5} more error(s)</li>
                                )}
                            </ul>
                        </div>
                    </div>
                </div>
            )}

            <form id="job-form" onSubmit={handleSubmit} className="space-y-8">
                {/* Basic Information Section */}
                <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm w-full">
                    <h2 className="text-lg font-semibold text-gray-900 mb-6 pb-3 border-b border-gray-200">
                        Basic Information
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* DOC: 'id' is generated server-side */}
                        <ReadOnlyIdField value={identity} />
                        <LabeledInput
                            label='Name'
                            value={name}
                            onChange={setName}
                            placeholder='Enter job name'
                        />
                        <LabeledInput 
                            label='Description' 
                            value={description} 
                            onChange={setDescription} 
                            placeholder='What this job is for' 
                        />
                        <LabeledInput
                            label='Implementation URI'
                            value={implementationUri}
                            onChange={setImplementationUri}
                            placeholder='Enter implementation URI'
                        />
                    </div>
                </section>

                {/* Roles Section */}
                <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm w-full">
                    <h2 className="text-lg font-semibold text-gray-900 mb-6 pb-3 border-b border-gray-200">
                        Roles
                    </h2>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                        {/* Inputs */}
                        <div className='border border-gray-200 rounded-lg p-4 space-y-4'>
                            <h3 className='text-base font-semibold text-gray-900 pb-2 border-b border-gray-200'>Inputs</h3>
                        {
                            <>
                                <div className='flex items-center gap-2'>
                                    <select
                                        className='flex-1 rounded border border-gray-300 px-3 py-2'
                                        value={newInputResourceTypeIdentity}
                                        onChange={(e) => setNewInputResourceTypeId(e.target.value)}
                                    >
                                        {resourceTypeResources.map((t) => (
                                            <option key={t.extractedData.identity} value={t.extractedData.identity}>
                                                {t.extractedData.name}
                                            </option>
                                        ))}
                                    </select>
                                    <button
                                        type='button'
                                        className='px-3 py-2 bg-[#7A0019] text-white rounded-md hover:bg-[#5A0013] transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium'
                                        disabled={!newInputResourceTypeIdentity}
                                        onClick={() => addRole(inputMap, setInputMap, newInputResourceTypeIdentity)}
                                    >
                                        Add input
                                    </button>
                                </div>

                                <ul className='divide-y border rounded'>
                                    {Object.entries(inputMap).length === 0 ? (
                                        <li className='p-2 text-sm text-gray-500'>No inputs yet</li>
                                    ) : (
                                        Object.entries(inputMap).map(([resourceRoleId, role]) => {
                                            const rl = role as unknown as ResourceRoleValueJson;
                                            return (
                                                <li key={resourceRoleId} className='p-2 flex flex-col gap-2'>
                                                    <div className='flex items-center gap-2'>
                                                        <span className='text-xs text-gray-500 shrink-0'>{resourceRoleId}</span>
                                                        <select
                                                            className='flex-1 rounded border border-gray-300 px-2 py-1'
                                                            value={rl?.resourceTypeRef || ''}
                                                            onChange={(e) =>
                                                                updateRoleType(
                                                                    inputMap,
                                                                    setInputMap,
                                                                    resourceRoleId as ResourceRoleIdentityJson,
                                                                    e.target.value as ResourceTypeIdentityJson
                                                                )
                                                            }
                                                        >
                                                            {resourceTypeResources.map((t) => (
                                                                <option key={t.extractedData.identity} value={t.extractedData.identity}>
                                                                    {t.extractedData.name}
                                                                </option>
                                                            ))}
                                                        </select>
                                                        <button
                                                            type='button'
                                                            className='px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 text-sm transition-colors'
                                                            onClick={() => removeRole(inputMap, setInputMap, resourceRoleId as ResourceRoleIdentityJson)}
                                                        >
                                                            Remove
                                                        </button>
                                                    </div>
                                                    <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role name'
                                                            value={rl?.name || ''}
                                                            onChange={(e) =>
                                                                updateRoleName(
                                                                    inputMap,
                                                                    setInputMap,
                                                                    resourceRoleId as ResourceRoleIdentityJson,
                                                                    e.target.value
                                                                )
                                                            }
                                                        />
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role description'
                                                            value={rl?.description || ''}
                                                            onChange={(e) =>
                                                                updateRoleDescription(
                                                                    inputMap,
                                                                    setInputMap,
                                                                    resourceRoleId as ResourceRoleIdentityJson,
                                                                    e.target.value
                                                                )
                                                            }
                                                        />
                                                    </div>
                                                </li>
                                            );
                                        })
                                    )}
                                </ul>
                            </>
                        }
                        </div>

                        {/* Outputs */}
                        <div className='border border-gray-200 rounded-lg p-4 space-y-4'>
                            <h3 className='text-base font-semibold text-gray-900 pb-2 border-b border-gray-200'>Outputs</h3>
                        {
                            <>
                                <div className='flex items-center gap-2'>
                                    <select
                                        className='flex-1 rounded border border-gray-300 px-3 py-2'
                                        value={newOutputResourceTypeIdentity}
                                        onChange={(e) => setNewOutputResourceTypeId(e.target.value)}
                                    >
                                        {resourceTypeResources.map((t) => (
                                            <option key={t.extractedData.identity} value={t.extractedData.identity}>
                                                {t.extractedData.name}
                                            </option>
                                        ))}
                                    </select>
                                    <button
                                        type='button'
                                        className='px-3 py-2 bg-[#7A0019] text-white rounded-md hover:bg-[#5A0013] transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium'
                                        disabled={!newOutputResourceTypeIdentity}
                                        onClick={() => addRole(outputMap, setOutputMap, newOutputResourceTypeIdentity)}
                                    >
                                        Add output
                                    </button>
                                </div>

                                <ul className='divide-y border rounded'>
                                    {Object.entries(outputMap).length === 0 ? (
                                        <li className='p-2 text-sm text-gray-500'>No outputs yet</li>
                                    ) : (
                                        Object.entries(outputMap).map(([resourceRoleId, role]) => {
                                            const rl = role as unknown as ResourceRoleValueJson;
                                            return (
                                                <li key={resourceRoleId} className='p-2 flex flex-col gap-2'>
                                                    <div className='flex items-center gap-2'>
                                                        <span className='text-xs text-gray-500 shrink-0'>{resourceRoleId}</span>
                                                        <select
                                                            className='flex-1 rounded border border-gray-300 px-2 py-1'
                                                            value={rl?.resourceTypeRef || ''}
                                                            onChange={(e) =>
                                                                updateRoleType(
                                                                    outputMap,
                                                                    setOutputMap,
                                                                    resourceRoleId as ResourceRoleIdentityJson,
                                                                    e.target.value as ResourceTypeIdentityJson
                                                                )
                                                            }
                                                        >
                                                            {resourceTypeResources.map((t) => (
                                                                <option key={t.extractedData.identity} value={t.extractedData.identity}>
                                                                    {t.extractedData.name}
                                                                </option>
                                                            ))}
                                                        </select>
                                                        <button
                                                            type='button'
                                                            className='px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 text-sm transition-colors'
                                                            onClick={() => removeRole(outputMap, setOutputMap, resourceRoleId as ResourceRoleIdentityJson)}
                                                        >
                                                            Remove
                                                        </button>
                                                    </div>
                                                    <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role name'
                                                            value={rl?.name || ''}
                                                            onChange={(e) =>
                                                                updateRoleName(
                                                                    outputMap,
                                                                    setOutputMap,
                                                                    resourceRoleId as ResourceRoleIdentityJson,
                                                                    e.target.value
                                                                )
                                                            }
                                                        />
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role description'
                                                            value={rl?.description || ''}
                                                            onChange={(e) =>
                                                                updateRoleDescription(
                                                                    outputMap,
                                                                    setOutputMap,
                                                                    resourceRoleId as ResourceRoleIdentityJson,
                                                                    e.target.value
                                                                )
                                                            }
                                                        />
                                                    </div>
                                                </li>
                                            );
                                        })
                                    )}
                                </ul>
                            </>
                        }
                        </div>
                    </div>
                </section>

                {/* Preview Section */}
                <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                    <div className="mb-6 pb-3 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <h2 className="text-lg font-semibold text-gray-900">
                                Preview
                            </h2>
                            {isValid && job != null && !jobParseError && (
                                <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full font-medium">
                                    ✓ Valid
                                </span>
                            )}
                            {jobParseError && (
                                <span className="text-xs px-2 py-1 bg-red-100 text-red-600 rounded-full font-medium">
                                    Invalid JSON
                                </span>
                            )}
                        </div>
                    </div>
                    <div className="bg-gray-100 rounded-lg border border-gray-300 p-3">
                        <div className="flex items-center justify-between mb-3">
                            <h3 className="text-sm font-semibold text-gray-700">Preview {identity || 'Job'}</h3>
                            {!loadingPreview && job != null && (
                                <div className="text-xs text-gray-500 font-medium">
                                    Read-only
                                </div>
                            )}
                        </div>
                        <div className="bg-gray-200 rounded p-4 overflow-auto">
                            {loadingPreview ? (
                                <div className="flex items-center justify-center py-8">
                                    <div className="flex items-center gap-2 text-gray-500">
                                        <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <span className="text-sm">Loading preview...</span>
                                    </div>
                                </div>
                            ) : job ? (
                                <pre className="text-xs font-mono text-gray-600 whitespace-pre-wrap leading-relaxed">
                                    {JSON.stringify(job, null, 2)}
                                </pre>
                            ) : (
                                <div className="text-sm text-gray-400 italic py-4 text-center">
                                    No preview available
                                </div>
                            )}
                        </div>
                        {errors && errors.length > 0 && (
                            <div className="mt-4">
                                <ValidationErrors errors={errors as ErrorObject[]} />
                            </div>
                        )}
                    </div>
                </section>
            </form>

            {/* Save Controls */}
            <SaveControls
                formId="job-form"
                buttonText="Save Job"
                disabled={!isValid || !identity}
                isValid={isValid}
                invalidMessage={isValid ? undefined : 'Fix errors above before saving.'}
                saveStatus={saveStatus}
                isLoading={isSaving}
                className="mt-8"
            />
        </div>
    );
}
