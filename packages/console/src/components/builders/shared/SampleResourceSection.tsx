'use client';

import { JsonEditor } from '@/builders/_lib/JsonEditor';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import type { ErrorObject } from 'ajv';

export interface SampleResourceSectionProps {
    legend: string;
    valueText: string;
    onChangeText: (text: string) => void;
    parseError: string | null;
    previewTitle: string;
    previewData: unknown;
    validationErrors?: ErrorObject[] | null;
    isValid?: boolean;
    heightClass?: string;
}

export function SampleResourceSection({
    legend,
    valueText,
    onChangeText,
    parseError,
    previewTitle,
    previewData,
    validationErrors,
    isValid = false,
    heightClass = 'h-64',
}: SampleResourceSectionProps) {
    return (
        <div className="space-y-6">
            {/* Sample Resource Editor */}
            <div className="bg-gray-50 rounded-lg p-3">
                <JsonEditor
                    legend={legend}
                    valueText={valueText}
                    onChangeText={onChangeText}
                    parseError={parseError}
                    heightClass={heightClass}
                />
            </div>

            {/* Preview Section */}
            <div className="bg-gray-100 rounded-lg border border-gray-300 p-3">
                <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-semibold text-gray-700">{previewTitle}</h3>
                    {previewData != null && (
                        <div className="text-xs text-gray-500 font-medium">
                            Read-only
                        </div>
                    )}
                </div>
                <div className="bg-gray-200 rounded p-4 overflow-auto">
                    {previewData ? (
                        <pre className="text-xs font-mono text-gray-600 whitespace-pre-wrap leading-relaxed">
                            {JSON.stringify(previewData, null, 2)}
                        </pre>
                    ) : (
                        <div className="text-sm text-gray-400 italic py-4 text-center">
                            No preview available
                        </div>
                    )}
                </div>
                {validationErrors && validationErrors.length > 0 ? (
                    <div className="mt-4">
                        <ValidationErrors errors={validationErrors} />
                    </div>
                ) : isValid ? (
                    <div className="mt-4 text-sm text-green-700 font-medium">
                        ✓ SampleResource is valid against current extractionSchema.
                    </div>
                ) : null}
            </div>
        </div>
    );
}

