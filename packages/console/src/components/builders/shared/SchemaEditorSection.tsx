'use client';

import { JsonEditor } from '@/builders/_lib/JsonEditor';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import type { ErrorObject } from 'ajv';

export interface SchemaEditorSectionProps {
    legend: string;
    valueText: string;
    onChangeText: (text: string) => void;
    parseError: string | null;
    previewTitle: string;
    previewData: unknown;
    validationErrors?: ErrorObject[] | null;
    heightClass?: string;
    loadingPreview?: boolean;
}

export function SchemaEditorSection({
    legend,
    valueText,
    onChangeText,
    parseError,
    previewTitle,
    previewData,
    validationErrors,
    heightClass = 'h-64',
    loadingPreview = false,
}: SchemaEditorSectionProps) {
    return (
        <div className="space-y-6">
            {/* Schema Editor */}
            <div className="bg-gray-50 rounded-lg p-3">
                <JsonEditor
                    legend={legend}
                    valueText={valueText}
                    onChangeText={onChangeText}
                    parseError={parseError}
                    heightClass={heightClass}
                />
            </div>

            {/* Preview Section */}
            <div className="bg-gray-100 rounded-lg border border-gray-300 p-3">
                <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-semibold text-gray-700">{previewTitle}</h3>
                    {!loadingPreview && previewData != null && (
                        <div className="text-xs text-gray-500 font-medium">
                            Read-only
                        </div>
                    )}
                </div>
                <div className="bg-gray-200 rounded p-4 overflow-auto">
                    {loadingPreview ? (
                        <div className="flex items-center justify-center py-8">
                            <div className="flex items-center gap-2 text-gray-500">
                                <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span className="text-sm">Loading preview...</span>
                            </div>
                        </div>
                    ) : previewData ? (
                        <pre className="text-xs font-mono text-gray-600 whitespace-pre-wrap leading-relaxed">
                            {JSON.stringify(previewData, null, 2)}
                        </pre>
                    ) : (
                        <div className="text-sm text-gray-400 italic py-4 text-center">
                            No preview available
                        </div>
                    )}
                </div>
                {validationErrors && validationErrors.length > 0 && (
                    <div className="mt-4">
                        <ValidationErrors errors={validationErrors} />
                    </div>
                )}
            </div>
        </div>
    );
}

