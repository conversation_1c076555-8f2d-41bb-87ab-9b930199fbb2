'use client';

import { LabeledInput } from '@/builders/_lib/LabeledInput';
import { LabeledCheckbox } from '@/builders/_lib/LabeledCheckbox';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import { DropDown } from '@/builders/_lib/DropDown';
import type { ResourceJson } from '@toolproof-npm/schema';

export interface BasicInfoSectionProps {
    identity: string;
    name: string;
    description: string;
    isSpecial: boolean;
    onIdentityChange?: (value: string) => void;
    onNameChange: (value: string) => void;
    onDescriptionChange: (value: string) => void;
    onIsSpecialChange: (checked: boolean) => void;
    formatItems?: ResourceJson[];
    selectedFormatId?: string;
    onFormatChange?: (id: string) => void;
    formatLabel?: string;
    nameError?: string;
    descriptionError?: string;
    showFormat?: boolean;
}

export function BasicInfoSection({
    identity,
    name,
    description,
    isSpecial,
    onIdentityChange,
    onNameChange,
    onDescriptionChange,
    onIsSpecialChange,
    formatItems = [],
    selectedFormatId = '',
    onFormatChange,
    formatLabel = 'Format',
    nameError,
    descriptionError,
    showFormat = false,
}: BasicInfoSectionProps) {
    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* ID Field */}
                {isSpecial && onIdentityChange ? (
                    <LabeledInput
                        label="ID"
                        value={identity}
                        onChange={onIdentityChange}
                        placeholder="Custom ID"
                    />
                ) : (
                    <ReadOnlyIdField value={identity} />
                )}

                {/* Format Dropdown */}
                {showFormat && formatItems.length > 0 && onFormatChange && (
                    <DropDown
                        items={formatItems}
                        value={selectedFormatId}
                        label={formatLabel}
                        onChange={onFormatChange}
                    />
                )}

                {/* Name Field */}
                <LabeledInput
                    label="Name"
                    value={name}
                    onChange={onNameChange}
                    placeholder="Enter name"
                    error={nameError}
                />

                {/* Description Field */}
                <LabeledInput
                    label="Description"
                    value={description}
                    onChange={onDescriptionChange}
                    placeholder="Enter description"
                    error={descriptionError}
                />

                {/* isSpecial Checkbox */}
                <div className="md:col-span-2">
                    <LabeledCheckbox
                        label="isSpecial"
                        checked={isSpecial}
                        onChange={onIsSpecialChange}
                    />
                </div>
            </div>
        </div>
    );
}

