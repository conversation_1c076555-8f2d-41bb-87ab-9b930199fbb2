'use client';

import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Resource_ResourceFormat<PERSON>son, Resource_ResourceTypeJson, Resource_JobJson } from '@toolproof-npm/schema';
import { extractResourcesByType } from '@toolproof-npm/shared/utils';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import FormatBuilder from '@/builders/format/FormatBuilder';
import TypeBuilder from '@/builders/type/TypeBuilder';
import JobBuilder from '@/builders/job/JobBuilder';
import ResourceBuilder from './resource/ResourceBuilder';
import StrategyBuilder from '@/builders/strategy/StrategyBuilder';
import { useState } from 'react';
import { useCosmosData } from '@/components/spaces/cosmos/CosmosDataProvider';
import { PageLayout } from '@/components/_root/PageLayout';

type BuilderKey = typeof CONSTANTS.TERMINALS.format | typeof CONSTANTS.TERMINALS.type | typeof CONSTANTS.TERMINALS.resource | typeof CONSTANTS.TERMINALS.job | 'strategy';

/**
 * Extracts resources of a specific type from the strategyState and returns them
 * as a Record mapping resource IDs to their full resource objects (including extractedData).
 * 
 * @param resourceMap - The full resource map containing all resource types
 * @param resourceTypeId - The specific resource type ID to filter by (e.g., 'TYPE-ResourceFormat')
 * @returns A record mapping resource IDs to their full resource objects
 */

const builders: { key: BuilderKey; label: string }[] = [
    { key: CONSTANTS.TERMINALS.format, label: 'ResourceFormat' },
    { key: CONSTANTS.TERMINALS.type, label: 'ResourceType' },
    { key: CONSTANTS.TERMINALS.resource, label: 'Resource' },
    { key: CONSTANTS.TERMINALS.job, label: 'Job' },
    { key: 'strategy', label: 'Strategy' },
];

export default function BuildersEntry() {
    const { cosmosData, loading, error } = useCosmosData();
    const { resourceMap } = cosmosData;
    const [active, setActive] = useState<BuilderKey>('format');

    const resourceResourceFormatMap = extractResourcesByType(resourceMap, 'TYPE-ResourceFormat') as Record<ResourceIdentityJson, Resource_ResourceFormatJson>;

    const resourceResourceTypeMap = extractResourcesByType(resourceMap, 'TYPE-ResourceType') as Record<ResourceIdentityJson, Resource_ResourceTypeJson>;

    const resourceJobMap = extractResourcesByType(resourceMap, 'TYPE-Job') as Record<ResourceIdentityJson, Resource_JobJson>;

    /* console.log(
        '[BuildersEntry] resourceJobMap:',
        JSON.stringify(resourceJobMap, null, 2)
    ); */

    if (loading) {
        return (
            <PageLayout>
                <div className="flex h-full items-center justify-center text-sm text-gray-400">
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-transparent" />
                    Loading Cosmos data…
                </div>
            </PageLayout>
        );
    }

    if (error) {
        return (
            <PageLayout>
                <div className="flex h-full items-center justify-center px-4 text-sm text-red-500">
                    Failed to load Cosmos data: {String(error?.message ?? error)}
                </div>
            </PageLayout>
        );
    }

    // Special layout for StrategyBuilder - needs adequate height but contained
    if (active === 'strategy') {
        return (
            <PageLayout>
                <div className="flex flex-col">
                    {/* Header section - fixed height */}
                    <div id="builders-entry-header" className="flex-shrink-0 tp-container py-4 sm:py-6 px-4 sm:px-6 bg-white border-b border-gray-200">
                        <div className="mx-auto max-w-[95vw] w-full">
                            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold tracking-tight text-[#7A0019] break-words mb-2 sm:mb-3" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                                Builders
                            </h1>
                            <p className="text-xs sm:text-sm text-[#7A0019]/80 break-words mb-4" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                                Create and manage ResourceFormats, ResourceTypes, Resources, Jobs, and Strategys
                            </p>

                            {/* Top controls */}
                            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
                                {/* Left: ResourceShapes and Resources groups */}
                                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 md:gap-4 flex-wrap">
                                    {/* ResourceShapes */}
                                    <div className="flex items-center gap-2 flex-wrap">
                                        <span className="text-xs font-medium tracking-wide text-gray-500 mr-1 hidden sm:inline">ResourceShapes</span>
                                        {([CONSTANTS.TERMINALS.format, CONSTANTS.TERMINALS.type] as BuilderKey[]).map((key) => {
                                            const label = builders.find((b) => b.key === key)?.label ?? String(key);
                                            return (
                                                <button
                                                    key={key}
                                                    onClick={() => setActive(key)}
                                                    className={`px-2.5 py-1.5 sm:px-3 md:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium ${active === key
                                                        ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                                        : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                                        }`}
                                                    aria-pressed={active === key}
                                                >
                                                    {label}
                                                </button>
                                            );
                                        })}
                                    </div>

                                    {/* Resources */}
                                    <div className="flex items-center gap-2 flex-wrap">
                                        <span className="text-xs font-medium tracking-wide text-gray-500 mr-1 hidden sm:inline">Resources</span>
                                        {([CONSTANTS.TERMINALS.resource, CONSTANTS.TERMINALS.job] as BuilderKey[]).map((key) => {
                                            const label = builders.find((b) => b.key === key)?.label ?? String(key);
                                            return (
                                                <button
                                                    key={key}
                                                    onClick={() => setActive(key)}
                                                    className={`px-2.5 py-1.5 sm:px-3 md:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium ${active === key
                                                        ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                                        : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                                        }`}
                                                    aria-pressed={active === key}
                                                >
                                                    {label}
                                                </button>
                                            );
                                        })}
                                    </div>
                                </div>

                                {/* Right: Strategy button */}
                                <div className="flex items-center w-full sm:w-auto">
                                    <button
                                        onClick={() => setActive('strategy')}
                                        className={`px-2.5 py-1.5 sm:px-3 md:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium w-full sm:w-auto ${active === 'strategy'
                                            ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                            : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                            }`}
                                        aria-pressed={active === 'strategy'}
                                    >
                                        Strategy
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Strategy builder - takes remaining space with padding */}
                    <div className="w-full px-2 sm:px-4 md:px-6 lg:px-8 xl:px-10" style={{ minHeight: 'calc(100vh - 300px)' }}>
                        <StrategyBuilder />
                    </div>
                </div>
            </PageLayout>
        );
    }

    // Standard layout for other builders
    return (
        <PageLayout>
            <div className="tp-container py-6 sm:py-8 md:py-12 px-4 sm:px-6">
                <div className="mx-auto max-w-6xl w-full">
                    <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold tracking-tight text-[#7A0019] break-words mb-2 sm:mb-3" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                        Builders
                    </h1>
                    <p className="text-xs sm:text-sm text-[#7A0019]/80 break-words mb-6 sm:mb-8" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                        Create and manage ResourceFormats, ResourceTypes, Jobs, Resources, and Strategies
                    </p>

                    {/* Top controls: ResourceShapes and Resources on the left; Strategy on the right */}
                    <div className="flex items-center justify-between flex-wrap gap-3 sm:gap-4 mb-8 pb-6 border-b border-gray-200">
                        {/* Left: ResourceShapes and Resources groups */}
                        <div className="flex items-center gap-3 sm:gap-4 flex-wrap">
                            {/* ResourceShapes */}
                            <div className="flex items-center gap-2 flex-wrap">
                                <span className="text-xs font-medium tracking-wide text-gray-500 mr-1">ResourceShapes</span>
                                {([CONSTANTS.TERMINALS.format, CONSTANTS.TERMINALS.type] as BuilderKey[]).map((key) => {
                                    const label = builders.find((b) => b.key === key)?.label ?? String(key);
                                    return (
                                        <button
                                            key={key}
                                            onClick={() => setActive(key)}
                                            className={`px-3 py-1.5 sm:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium ${active === key
                                                ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                                : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                                }`}
                                            aria-pressed={active === key}
                                        >
                                            {label}
                                        </button>
                                    );
                                })}
                            </div>

                            {/* Resources */}
                            <div className="flex items-center gap-2 flex-wrap">
                                <span className="text-xs font-medium tracking-wide text-gray-500 mr-1">Resources</span>
                                {([CONSTANTS.TERMINALS.resource, CONSTANTS.TERMINALS.job] as BuilderKey[]).map((key) => {
                                    const label = builders.find((b) => b.key === key)?.label ?? String(key);
                                    return (
                                        <button
                                            key={key}
                                            onClick={() => setActive(key)}
                                            className={`px-3 py-1.5 sm:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium ${active === key
                                                ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                                : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                                }`}
                                            aria-pressed={active === key}
                                        >
                                            {label}
                                        </button>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Right: Strategy button */}
                        <div className="flex items-center">
                            <button
                                onClick={() => setActive('strategy')}
                                className="px-3 py-1.5 sm:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5"
                                aria-pressed={false}
                            >
                                Strategy
                            </button>
                        </div>
                    </div>

                    <div className="mt-6">
                        {active === CONSTANTS.TERMINALS.format && (
                            <FormatBuilder />
                        )}
                        {active === CONSTANTS.TERMINALS.type && (
                            <TypeBuilder resourceResourceFormatMap={resourceResourceFormatMap} />
                        )}
                        {active === CONSTANTS.TERMINALS.job && (
                            <JobBuilder resourceResourceTypeMap={resourceResourceTypeMap} />
                        )}
                        {active === CONSTANTS.TERMINALS.resource && (
                            <ResourceBuilder resourceResourceTypeMap={resourceResourceTypeMap} />
                        )}
                    </div>
                </div>
            </div>
        </PageLayout>
    );
}
