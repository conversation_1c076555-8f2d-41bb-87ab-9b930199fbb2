'use client';

import type { ResourceFormat<PERSON><PERSON>, ResourceFormatIdentityJson, ExecutionIdentityJson, ResourceIdentityJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateSpecial } from '@toolproof-npm/validation';
import { getUiContext } from '@/builders/_lib/utils';
import { BasicInfoSection } from '@/builders/shared/BasicInfoSection';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import { usePrefetchedIdentities } from '@/builders/_lib/usePrefetchedIdentities';
import { uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import SaveControls from '@/builders/_lib/SaveControls';
import type { ErrorObject } from 'ajv';
import { useState, useMemo } from 'react';


export default function FormatBuilder() {
    const { values: ids, setValues: setIds } = usePrefetchedIdentities({
        identity: { terminal: CONSTANTS.TERMINALS.format },
        resourceIdentity: { terminal: CONSTANTS.TERMINALS.resource },
        executionIdentity: { terminal: CONSTANTS.TERMINALS.execution },
    });
    const identity = ids.identity;
    const resourceIdentity = ids.resourceIdentity;
    const executionIdentity = ids.executionIdentity;
    const setIdentity = (next: string) => setIds((prev) => ({ ...prev, identity: next }));
    const [name, setName] = useState<string>('');
    const [description, setDescription] = useState<string>('dummy-description');
    const [isSpecial, setIsSpecial] = useState<boolean>(false);
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [isSaving, setIsSaving] = useState<boolean>(false);

    // DOC: Let an instance of the ResourceFormat be defined by state variables
    const format: ResourceFormatJson = useMemo(
        () => {
            return {
                identity: identity as ResourceFormatIdentityJson,
                name,
                description,
                recognizerUri: 'https://dummy-recognizer-uri',
            };
        },
        [identity, name, description]
    );

    // DOC: Fetch new identities (handled by usePrefetchedIdentities)

    // DOC: Validate the resourceshape locally
    const isValidLocal = useMemo(() => validateLocally(format), [format]);

    const uiContext = getUiContext();

    // DOC: Validate the resourceshape formally against its schema
    const { isValid: isValidFormal, errors } = validateSpecial('ResourceFormat', format, uiContext); // ATTENTION: hardcoded defsPointer

    // console.log('-----------');
    // console.log('FormatBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidLocal.valid && isValidFormal;

    // DOC: Upload the resourceshape upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        setIsSaving(true);
        setSaveStatus(null);
        try {
            const res = (await uploadResource(
                {
                    identity: resourceIdentity as ResourceIdentityJson,
                    resourceTypeRef: CONSTANTS.SPECIALS.TYPE_ResourceFormat,
                    creationContext: {
                        resourceRoleRef: CONSTANTS.SPECIALS.ROLE_Manual,
                        executionRef: executionIdentity as ExecutionIdentityJson,
                    },
                },
                format
            ));
            if (res.success) {
                setSaveStatus(`✓ Successfully saved at: ${res.path}`);
            } else {
                setSaveStatus(`✗ Save failed: ${res.error ?? 'Unknown error'}`);
            }
        } catch (err) {
            setSaveStatus(`✗ Save failed: ${(err as Error).message ?? 'Unknown error'}`);
        } finally {
            setIsSaving(false);
        }
    };

    // Collect all validation errors for summary
    const allErrors = [
        ...(isValidLocal.errors.name ? [`Name: ${isValidLocal.errors.name}`] : []),
        ...(isValidLocal.errors.description ? [`Description: ${isValidLocal.errors.description}`] : []),
        ...(errors && errors.length > 0 ? errors.map(e => `Schema validation: ${e.instancePath || 'root'} ${e.message}`) : []),
    ];

    return (
        <div className="space-y-8">
            {/* Validation Summary Banner */}
            {!isValid && allErrors.length > 0 && (
                <div className="bg-red-50 border-l-4 border-red-400 rounded-r-lg p-4 shadow-sm">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3 flex-1">
                            <h3 className="text-sm font-semibold text-red-600 mb-2">
                                Please fix the following errors before saving:
                            </h3>
                            <ul className="list-disc list-inside space-y-1 text-sm text-red-600">
                                {allErrors.slice(0, 5).map((error, idx) => (
                                    <li key={idx}>{error}</li>
                                ))}
                                {allErrors.length > 5 && (
                                    <li className="text-red-600 italic">...and {allErrors.length - 5} more error(s)</li>
                                )}
                            </ul>
                        </div>
                    </div>
                </div>
            )}

            <form id="format-form" onSubmit={handleSubmit} className="space-y-8">
                {/* Basic Information Section */}
                <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm w-full">
                    <h2 className="text-lg font-semibold text-gray-900 mb-6 pb-3 border-b border-gray-200">
                        Basic Information
                    </h2>
                    <BasicInfoSection
                        identity={identity}
                        name={name}
                        description={description}
                        isSpecial={isSpecial}
                        onIdentityChange={isSpecial ? setIdentity : undefined}
                        onNameChange={setName}
                        onDescriptionChange={setDescription}
                        onIsSpecialChange={setIsSpecial}
                        nameError={isValidLocal.errors.name}
                        descriptionError={isValidLocal.errors.description}
                        showFormat={false}
                    />
                </section>

                {/* Preview Section */}
                <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                    <div className="mb-6 pb-3 border-b border-gray-200">
                        <h2 className="text-lg font-semibold text-gray-900">
                            Preview
                        </h2>
                    </div>
                    <div className="bg-gray-100 rounded-lg border border-gray-300 p-3">
                        <div className="flex items-center justify-between mb-3">
                            <h3 className="text-sm font-semibold text-gray-700">Preview {name || 'Format'}</h3>
                            {!loadingPreview && format != null && (
                                <div className="text-xs text-gray-500 font-medium">
                                    Read-only
                                </div>
                            )}
                        </div>
                        <div className="bg-gray-200 rounded p-4 overflow-auto">
                            {loadingPreview ? (
                                <div className="flex items-center justify-center py-8">
                                    <div className="flex items-center gap-2 text-gray-500">
                                        <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <span className="text-sm">Loading preview...</span>
                                    </div>
                                </div>
                            ) : format ? (
                                <pre className="text-xs font-mono text-gray-600 whitespace-pre-wrap leading-relaxed">
                                    {JSON.stringify(format, null, 2)}
                                </pre>
                            ) : (
                                <div className="text-sm text-gray-400 italic py-4 text-center">
                                    No preview available
                                </div>
                            )}
                        </div>
                        {errors && errors.length > 0 && (
                            <div className="mt-4">
                                <ValidationErrors errors={errors as ErrorObject[]} />
                            </div>
                        )}
                    </div>
                </section>
            </form>

            {/* Save Controls */}
            <SaveControls
                formId="format-form"
                buttonText="Save Format"
                disabled={!isValid || !identity}
                isValid={isValid}
                invalidMessage={isValid ? undefined : 'Fix errors above before saving.'}
                saveStatus={saveStatus}
                isLoading={isSaving}
                className="mt-8"
            />
        </div>
    );
}

function validateLocally(
    format: ResourceFormatJson
): { valid: boolean; errors: Record<string, string | undefined> } {
    const errors: Record<string, string | undefined> = {};

    if (!format.name.trim()) errors.name = 'name is required';
    // Description is optional, so no validation needed

    return { valid: Object.keys(errors).length === 0, errors };
}
