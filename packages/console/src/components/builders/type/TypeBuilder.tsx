'use client';

import type { ResourceTypeIdentityJson, ResourceFormatIdentityJson, ResourceIdentityJson, ExecutionIdentityJson, Resource_ResourceFormatJson, ResourceTypeJson, ExtractionSchemaJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateSpecial, validateResource } from '@toolproof-npm/validation';
import { useMemo, useState, useEffect } from 'react';
import type { ErrorObject } from 'ajv';
import { getUiContext } from '@/builders/_lib/utils';
import { usePrefetchedIdentities } from '@/builders/_lib/usePrefetchedIdentities';
import { BasicInfoSection } from '@/builders/shared/BasicInfoSection';
import { SchemaEditorSection } from '@/builders/shared/SchemaEditorSection';
import { SampleResourceSection } from '@/builders/shared/SampleResourceSection';
import SaveControls from '@/builders/_lib/SaveControls';
import { uploadResource } from '@/_lib/server/firebaseAdminHelpers';

const DUMMY_EXTRACTOR_URI = 'https://extractors.toolproof.com/v0/DummyExtractor.js';

type ResourceTypeWithExtractorUri = ResourceTypeJson & { extractorUri?: string };


const defaultExtractionSchema: ExtractionSchemaJson = {
    $anchor: 'Natural',
    $schema: 'https://json-schema.org/draft/2020-12/schema',
    type: 'object',
    required: ['identity'],
    properties: {
        identity: { $ref: '#/$defs/NaturalIdentity' },
    },
    $defs: {
        NaturalIdentity: {
            type: 'integer',
        }
    },
    additionalProperties: false,
};

const defaultType: ResourceTypeJson = {
    identity: '' as ResourceTypeIdentityJson,
    name: 'Natural',
    description: 'dummy-description',
    resourceFormatRef: 'FORMAT-ApplicationJson',
    extractionSchema: defaultExtractionSchema,
};

const defaultSampleResource = { identity: 0 };

interface TypeBuilderProps {
    resourceResourceFormatMap: Record<string, Resource_ResourceFormatJson>;
}

export default function TypeBuilder({ resourceResourceFormatMap }: TypeBuilderProps) {
    const { values: ids, setValues: setIds } = usePrefetchedIdentities({
        identity: { terminal: CONSTANTS.TERMINALS.type, initial: defaultType.identity },
        resourceIdentity: { terminal: CONSTANTS.TERMINALS.resource },
        executionIdentity: { terminal: CONSTANTS.TERMINALS.execution },
    });
    const identity = ids.identity;
    const resourceIdentity = ids.resourceIdentity;
    const executionIdentity = ids.executionIdentity;
    const setIdentity = (next: string) => setIds((prev) => ({ ...prev, identity: next }));
    const [name, setName] = useState(defaultType.name);
    const [description, setDescription] = useState(defaultType.description);
    const [isSpecial, setIsSpecial] = useState<boolean>(false);
    const [selectedResourceFormatId, setSelectedResourceFormatId] = useState<string>('');
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [isSaving, setIsSaving] = useState<boolean>(false);

    const [extractionSchema, setExtractionSchema] = useState<ExtractionSchemaJson>(defaultType.extractionSchema as ExtractionSchemaJson);
    const [extractionSchemaText, setExtractionSchemaText] = useState<string>(
        JSON.stringify(defaultType.extractionSchema, null, 2)
    );
    const [extractionSchemaParseError, setExtractionSchemaParseError] = useState<string | null>(null);

    const [sampleResource, setSampleResource] = useState<unknown>(defaultSampleResource);
    const [sampleResourceText, setSampleResourceText] = useState<string>(JSON.stringify(defaultSampleResource, null, 2));
    const [sampleResourceParseError, setSampleResourceParseError] = useState<string | null>(null);

    const resourceFormatResources = useMemo(() => {
        return Object.values(resourceResourceFormatMap).map((res) => ({
            ...res,
        }));
    }, [resourceResourceFormatMap]);

    const requiresExtractorUri =
        !!selectedResourceFormatId && selectedResourceFormatId !== CONSTANTS.SPECIALS.FORMAT_ApplicationJson;

    // DOC: Let an instance of the ResourceType be defined by state variables
    const type: ResourceTypeJson = useMemo(() => {
        const next: ResourceTypeWithExtractorUri = {
            identity: identity as ResourceTypeIdentityJson,
            name,
            description,
            resourceFormatRef: selectedResourceFormatId as ResourceFormatIdentityJson,
            extractionSchema,
        };

        if (requiresExtractorUri) {
            next.extractorUri = DUMMY_EXTRACTOR_URI;
        }

        return next;
    }, [identity, name, description, selectedResourceFormatId, extractionSchema, requiresExtractorUri]);

    // DOC: Fetch new identities (handled by usePrefetchedIdentities)

    // DOC: When dependents arrive, auto-select the first
    useEffect(() => {
        if (!selectedResourceFormatId && resourceFormatResources.length) {
            setSelectedResourceFormatId(resourceFormatResources[0].extractedData.identity);
        }
    }, [resourceFormatResources, selectedResourceFormatId]);

    // DOC: Validate the resourceshape locally
    const isValidLocal = useMemo(() => validateLocally(type), [type]);

    const uiContext = getUiContext();

    // DOC: Validate the resourceshape formally against its schema
    const { isValid: isValidFormal, errors } = validateSpecial('ResourceType', type, uiContext);

    // console.log('-----------');
    // console.log('TypeBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidLocal.valid && !extractionSchemaParseError && isValidFormal;

    // DOC: Validate sampleResource against extractionSchema
    const { isValid: isValidSample, errors: errorsSample } = validateResource(extractionSchema, sampleResource);

    // DOC: Update extractionSchema state on text change, with parse error handling
    const handleExtractionSchemaChange = (text: string) => {
        setExtractionSchemaText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setExtractionSchemaParseError('extractionSchema must be a JSON Schema object.');
                return;
            }
            setExtractionSchema(parsed as ExtractionSchemaJson);
            setExtractionSchemaParseError(null);
        } catch (e) {
            setExtractionSchemaParseError((e as Error).message);
        }
    };

    // DOC: Update sampleResource state on text change, with parse error handling
    const handleSampleResourceChange = (text: string) => {
        setSampleResourceText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setSampleResourceParseError('sampleResource must be a JSON object.');
                return;
            }
            setSampleResource(parsed);
            setSampleResourceParseError(null);
        } catch (e) {
            setSampleResourceParseError((e as Error).message);
        }
    };

    // DOC: Upload the resourceshape upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        setIsSaving(true);
        setSaveStatus(null);
        try {
            const res = (await uploadResource(
                {
                    identity: resourceIdentity as ResourceIdentityJson,
                    resourceTypeRef: CONSTANTS.SPECIALS.TYPE_ResourceType,
                    creationContext: {
                        resourceRoleRef: CONSTANTS.SPECIALS.ROLE_Manual,
                        executionRef: executionIdentity as ExecutionIdentityJson,
                    },
                },
                type
            ));
            if (res.success) {
                setSaveStatus(`✓ Successfully saved at: ${res.path}`);
            } else {
                setSaveStatus(`✗ Save failed: ${res.error ?? 'Unknown error'}`);
            }
        } catch (err) {
            setSaveStatus(`✗ Save failed: ${(err as Error).message ?? 'Unknown error'}`);
        } finally {
            setIsSaving(false);
        }
    };

    // Collect all validation errors for summary
    const allErrors = [
        ...(isValidLocal.errors.name ? [`Name: ${isValidLocal.errors.name}`] : []),
        ...(isValidLocal.errors.description ? [`Description: ${isValidLocal.errors.description}`] : []),
        ...(isValidLocal.errors.resourceFormatId ? [`Format: ${isValidLocal.errors.resourceFormatId}`] : []),
        ...(isValidLocal.errors.extractorUri ? [`Extractor URI: ${isValidLocal.errors.extractorUri}`] : []),
        ...(isValidLocal.errors.extractionSchema ? [`Extraction Schema: ${isValidLocal.errors.extractionSchema}`] : []),
        ...(extractionSchemaParseError ? [`Extraction Schema JSON: ${extractionSchemaParseError}`] : []),
        ...(sampleResourceParseError ? [`Sample Resource JSON: ${sampleResourceParseError}`] : []),
        ...(errors && errors.length > 0 ? errors.map(e => `Schema validation: ${e.instancePath || 'root'} ${e.message}`) : []),
    ];

    return (
        <div className="space-y-8">
            {/* Validation Summary Banner */}
            {!isValid && allErrors.length > 0 && (
                <div className="bg-red-50 border-l-4 border-red-400 rounded-r-lg p-4 shadow-sm">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3 flex-1">
                            <h3 className="text-sm font-semibold text-red-600 mb-2">
                                Please fix the following errors before saving:
                            </h3>
                            <ul className="list-disc list-inside space-y-1 text-sm text-red-600">
                                {allErrors.slice(0, 5).map((error, idx) => (
                                    <li key={idx}>{error}</li>
                                ))}
                                {allErrors.length > 5 && (
                                    <li className="text-red-600 italic">...and {allErrors.length - 5} more error(s)</li>
                                )}
                            </ul>
                        </div>
                    </div>
                </div>
            )}

            <form id="type-form" onSubmit={handleSubmit} className="space-y-8">
                {/* Basic Information Section */}
                <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm w-full">
                    <h2 className="text-lg font-semibold text-gray-900 mb-6 pb-3 border-b border-gray-200">
                        Basic Information
                    </h2>
                    <BasicInfoSection
                        identity={identity}
                        name={name}
                        description={description}
                        isSpecial={isSpecial}
                        onIdentityChange={isSpecial ? setIdentity : undefined}
                        onNameChange={setName}
                        onDescriptionChange={setDescription}
                        onIsSpecialChange={setIsSpecial}
                        formatItems={resourceFormatResources}
                        selectedFormatId={selectedResourceFormatId}
                        onFormatChange={setSelectedResourceFormatId}
                        formatLabel="Format"
                        nameError={isValidLocal.errors.name}
                        descriptionError={isValidLocal.errors.description}
                        showFormat={true}
                    />
                </section>

                {/* Schema & Sample Resource Section */}
                <div className="grid grid-cols-1 lg:grid-cols-[1.2fr_1fr] xl:grid-cols-[1.4fr_1fr] gap-6 w-full">
                    {/* Extraction Schema Section */}
                    <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                        <div className="mb-6 pb-3 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <h2 className="text-lg font-semibold text-gray-900">
                                    Extraction Schema
                                </h2>
                                {extractionSchemaParseError && (
                                    <span className="text-xs px-2 py-1 bg-red-100 text-red-600 rounded-full font-medium">
                                        Invalid JSON
                                    </span>
                                )}
                            </div>
                        </div>
                        <SchemaEditorSection
                            legend="extractionSchema (JSON Schema draft 2020-12)"
                            valueText={extractionSchemaText}
                            onChangeText={handleExtractionSchemaChange}
                            parseError={extractionSchemaParseError}
                            previewTitle={`Preview ${name || 'Type'}`}
                            previewData={type}
                            validationErrors={errors as ErrorObject[] | null | undefined}
                            heightClass="h-64"
                            loadingPreview={loadingPreview}
                        />
                    </section>

                    {/* Sample Resource Section */}
                    <section className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                        <div className="mb-6 pb-3 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <h2 className="text-lg font-semibold text-gray-900">
                                    Sample Resource
                                </h2>
                                {isValidSample && sampleResource != null && !sampleResourceParseError && (
                                    <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full font-medium">
                                        ✓ Valid
                                    </span>
                                )}
                                {sampleResourceParseError && (
                                    <span className="text-xs px-2 py-1 bg-red-100 text-red-600 rounded-full font-medium">
                                        Invalid JSON
                                    </span>
                                )}
                            </div>
                        </div>
                        <SampleResourceSection
                            legend="sampleResource (validated against extractionSchema)"
                            valueText={sampleResourceText}
                            onChangeText={handleSampleResourceChange}
                            parseError={sampleResourceParseError}
                            previewTitle="Preview SampleResource"
                            previewData={sampleResource}
                            validationErrors={errorsSample}
                            isValid={isValidSample}
                            heightClass="h-64"
                        />
                    </section>
                </div>
            </form>

            {/* Save Controls */}
            <SaveControls
                formId="type-form"
                buttonText="Save Type"
                disabled={!isValid || !identity}
                isValid={isValid}
                invalidMessage={isValid ? undefined : 'Fix errors above before saving.'}
                saveStatus={saveStatus}
                isLoading={isSaving}
                className="mt-8"
            />
        </div>
    );
}

function validateLocally(
    type: ResourceTypeJson
): { valid: boolean; errors: Record<string, string | undefined> } {
    const errors: Record<string, string | undefined> = {};

    if (!type.name.trim()) errors.name = 'name is required';
    // if (!rt.description?.trim()) errors.description = 'Description is required';
    if (!type.resourceFormatRef) errors.resourceFormatId = 'resourceFormatId is required';
    if (type.resourceFormatRef && type.resourceFormatRef !== CONSTANTS.SPECIALS.FORMAT_ApplicationJson) {
        const extractorUri = (type as ResourceTypeWithExtractorUri).extractorUri;
        if (typeof extractorUri !== 'string' || extractorUri.trim() === '') {
            errors.extractorUri = 'extractorUri is required for non-ApplicationJson formats';
        }
    }
    if (!type.extractionSchema || typeof type.extractionSchema !== 'object' || Array.isArray(type.extractionSchema)) {
        errors.extractionSchema = 'extractionSchema must be a JSON Schema object';
    }

    return { valid: Object.keys(errors).length === 0, errors };
}
