'use client';

import { mockGoals, MockGoal, MockStrategy } from './mockData';
import { useState } from 'react';

export function GoalsTable() {
  const [contributeDialogGoal, setContributeDialogGoal] = useState<MockGoal | null>(null);
  const [isProcessingDonation, setIsProcessingDonation] = useState(false);

  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-8">
      {/* Contribute Dialog */}
      {contributeDialogGoal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden">
            {/* Dialog Header */}
            <div className="bg-gradient-to-r from-[#7A0019] to-[#5A0013] px-6 py-4">
              <h3 className="text-xl font-bold text-[#FFCC33]">Contribute to Goal</h3>
              <p className="text-white/90 text-sm mt-1">{contributeDialogGoal.name}</p>
            </div>

            {/* Dialog Content */}
            <div className="p-6 space-y-3">
              <button
                onClick={async () => {
                  if (!contributeDialogGoal) return;
                  
                  setIsProcessingDonation(true);
                  try {
                    const response = await fetch('/api/create-checkout-session', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify({
                        goalId: contributeDialogGoal.id,
                        goalName: contributeDialogGoal.name,
                      }),
                    });

                    const data = await response.json();

                    if (!response.ok) {
                      throw new Error(data.error || 'Failed to create checkout session');
                    }

                    // Redirect to Stripe Checkout
                    if (data.url) {
                      window.location.href = data.url;
                    } else {
                      throw new Error('No checkout URL received');
                    }
                  } catch (error) {
                    console.error('Donation error:', error);
                    alert(`Failed to process donation: ${(error as Error).message}`);
                    setIsProcessingDonation(false);
                  }
                }}
                disabled={isProcessingDonation}
                className="w-full flex items-center gap-3 px-4 py-3 border-2 border-[#FFCC33] text-[#7A0019] rounded-md hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="text-left">
                  <div className="font-semibold">
                    {isProcessingDonation ? 'Processing...' : 'Donate'}
                  </div>
                  <div className="text-xs opacity-75">Support this goal financially</div>
                </div>
              </button>

              <button
                onClick={() => {
                  alert(`Develop for: ${contributeDialogGoal.name}`);
                  setContributeDialogGoal(null);
                }}
                className="w-full flex items-center gap-3 px-4 py-3 border-2 border-[#FFCC33] text-[#7A0019] rounded-md hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors font-semibold"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
                <div className="text-left">
                  <div className="font-semibold">Develop</div>
                  <div className="text-xs opacity-75">Contribute code or strategies</div>
                </div>
              </button>

              <button
                onClick={() => {
                  alert(`Learn more about: ${contributeDialogGoal.name}`);
                  setContributeDialogGoal(null);
                }}
                className="w-full flex items-center gap-3 px-4 py-3 border-2 border-[#FFCC33] text-[#7A0019] rounded-md hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors font-semibold"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="text-left">
                  <div className="font-semibold">Learn more</div>
                  <div className="text-xs opacity-75">Read about this goal and progress</div>
                </div>
              </button>
            </div>

            {/* Dialog Footer */}
            <div className="px-6 pb-6">
              <button
                onClick={() => setContributeDialogGoal(null)}
                className="w-full px-4 py-2 text-sm text-gray-600 hover:text-[#7A0019] transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mb-8">
        <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight text-[#7A0019] mb-3">
          Goals & Strategies
        </h1>
        <p className="text-lg text-gray-600">
          Mock demonstration of computational research goals with associated job execution strategies
        </p>
      </div>

      {/* Compact Overview Table */}
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden mb-8">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                Goal
              </th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                Strategies
              </th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                Total Jobs
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {mockGoals.map((goal) => {
              const totalJobs = goal.strategies.reduce((sum, s) => sum + s.steps.length, 0);
              return (
                <tr key={goal.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4">
                    <div className="font-semibold text-[#7A0019]">{goal.name}</div>
                    <div className="text-sm text-gray-600 mt-1">{goal.description}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="space-y-1">
                      {goal.strategies.map((strategy) => (
                        <div key={strategy.identity} className="text-sm">
                          <span className="font-medium text-gray-900">{strategy.name}</span>
                          <span className="text-gray-500 ml-2">({strategy.steps.length} jobs)</span>
                        </div>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#7A0019] text-[#FFCC33]">
                      {totalJobs}
                    </span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Detailed Expandable Cards */}
      <div className="space-y-4">
        {mockGoals.map((goal) => (
          <GoalCard key={goal.id} goal={goal} onContribute={setContributeDialogGoal} />
        ))}
      </div>
    </div>
  );
}

function GoalCard({ goal, onContribute }: { goal: MockGoal; onContribute: (goal: MockGoal) => void }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const totalJobs = goal.strategies.reduce((sum, s) => sum + s.steps.length, 0);

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
      {/* Collapsible Goal Header */}
      <div className="bg-gradient-to-r from-[#7A0019] to-[#5A0013]">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full px-6 py-4 text-left hover:opacity-95 transition-opacity"
        >
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-3">
                <span className="text-[#FFCC33] text-xl font-bold transition-transform duration-200" style={{ transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)' }}>
                  ▸
                </span>
                <h2 className="text-xl font-bold text-[#FFCC33]">
                  {goal.name}
                </h2>
              </div>
              <p className="text-white/90 mt-2 ml-8">
                {goal.description}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#FFCC33] text-[#7A0019]">
                {goal.strategies.length} {goal.strategies.length === 1 ? 'strategy' : 'strategies'}
              </span>
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-white/20 text-white">
                {totalJobs} total jobs
              </span>
            </div>
          </div>
        </button>
        
        {/* Contribute Button */}
        <div className="px-6 pb-4 flex justify-end">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onContribute(goal);
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-[#FFCC33] text-[#7A0019] rounded-md font-semibold text-sm hover:bg-[#FFD700] transition-colors shadow-md hover:shadow-lg"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Contribute
          </button>
        </div>
      </div>

      {/* Strategies Section - Collapsible */}
      {isExpanded && (
        <div className="p-6 bg-gray-50">
          <div className="space-y-4">
            {goal.strategies.map((strategy) => (
              <StrategyCard key={strategy.identity} strategy={strategy} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

function StrategyCard({ strategy }: { strategy: MockStrategy }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="border border-gray-300 rounded-md overflow-hidden bg-white shadow-sm">
      {/* Strategy Header - Collapsible */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full bg-white hover:bg-gray-50 px-4 py-3 border-b border-gray-200 text-left transition-colors"
      >
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-start gap-2 flex-1">
            <span className="text-[#7A0019] text-sm font-bold mt-0.5 transition-transform duration-200" style={{ transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)' }}>
              ▸
            </span>
            <div className="flex-1">
              <h4 className="font-semibold text-[#7A0019]">
                {strategy.name}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {strategy.description}
              </p>
              <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                <span className="font-mono">{strategy.identity}</span>
              </div>
            </div>
          </div>
          <div className="flex-shrink-0">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#7A0019] text-[#FFCC33]">
              {strategy.steps.length} jobs
            </span>
          </div>
        </div>
      </button>

      {/* Job Steps - Collapsible */}
      {isExpanded && (
        <div className="bg-white">
          <div className="divide-y divide-gray-100">
            {strategy.steps.map((step, index) => (
              <div key={step.identity} className="px-4 py-3 hover:bg-gray-50 transition-colors">
                <div className="flex items-start gap-3">
                  {/* Step Number */}
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-[#7A0019] text-[#FFCC33] flex items-center justify-center text-xs font-bold">
                    {index + 1}
                  </div>
                  
                  {/* Job Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-baseline gap-2 flex-wrap">
                      <span className="font-medium text-gray-900">
                        {step.jobName}
                      </span>
                      <span className="text-xs text-gray-500 font-mono">
                        {step.jobId}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {step.description}
                    </p>
                  </div>

                  {/* Kind Badge */}
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                      {step.kind}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
