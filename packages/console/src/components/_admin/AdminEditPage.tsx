'use client';

import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useState, useEffect, useMemo, useRef, memo, useCallback } from 'react';
import { useCosmosData } from '@/components/spaces/cosmos/CosmosDataProvider';
import { PageLayout } from '@/components/_root/PageLayout';
import { uploadResource, deleteResource, getNewIdentity } from '@/_lib/server/firebaseAdminHelpers';
import type { ResourceJson, ResourceTypeIdJson, ResourceIdJson, ResourceMetaJson, ResourceRoleIdJson, ExecutionIdJson, ResourcePotentialOutputJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import SaveControls from '@/builders/_lib/SaveControls';

interface NestedFieldProps {
    keyName: string;
    value: unknown;
    path: string[];
    onValueChange: (path: string[], value: unknown) => void;
    onKeyChange?: (oldKey: string, newKey: string) => void;
    isOriginalKey: boolean;
    onRemove?: () => void;
    indentLevel?: number;
    isEditable?: boolean;
    onRegisterSync?: (syncFn: () => void) => void;
}

function NestedField({ keyName, value, path, onValueChange, onKeyChange, isOriginalKey, onRemove, indentLevel = 0, isEditable = true, onRegisterSync }: NestedFieldProps) {
    const isObject = typeof value === 'object' && value !== null && !Array.isArray(value);
    const indent = indentLevel * 24;
    const canEditKey = isEditable && !isOriginalKey && indentLevel === 0 && onKeyChange;
    
    // Controlled component with focus tracking to prevent updates while typing
    const getStringValue = (val: unknown) => typeof val === 'string' ? val : JSON.stringify(val, null, 2);
    const [localValue, setLocalValue] = useState<string>(() => getStringValue(value));
    const isFocusedRef = useRef(false);
    const lastSyncedValueRef = useRef(value);
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const cursorPositionRef = useRef<number | null>(null);
    
    // Function to force sync the current value
    const forceSync = useCallback(() => {
        if (isFocusedRef.current && textareaRef.current) {
            const currentValue = textareaRef.current.value;
            let parsedValue: unknown = currentValue;
            // Try to parse as JSON if it looks like JSON
            if (currentValue.trim().startsWith('{') || currentValue.trim().startsWith('[')) {
                try {
                    parsedValue = JSON.parse(currentValue);
                } catch {
                    parsedValue = currentValue;
                }
            }
            lastSyncedValueRef.current = parsedValue;
            onValueChange(path, parsedValue);
            isFocusedRef.current = false;
        }
    }, [path, onValueChange]);
    
    // Register sync function if provided
    useEffect(() => {
        if (onRegisterSync && isEditable) {
            onRegisterSync(forceSync);
            return () => {
                // Cleanup would require storing the function reference
            };
        }
    }, [onRegisterSync, forceSync, isEditable]);
    
    // Local state for key name input to prevent focus loss
    const [localKeyName, setLocalKeyName] = useState<string>(keyName);
    const isKeyFocusedRef = useRef(false);
    const keyInputRef = useRef<HTMLInputElement>(null);
    const keyCursorPositionRef = useRef<number | null>(null);
    
    // Sync local key name with prop when not focused
    useEffect(() => {
        if (!isKeyFocusedRef.current && localKeyName !== keyName) {
            setLocalKeyName(keyName);
        }
    }, [keyName, localKeyName]);
    
    // Restore key input focus and cursor position after render if it was focused
    useEffect(() => {
        if (isKeyFocusedRef.current && keyInputRef.current && keyCursorPositionRef.current !== null) {
            keyInputRef.current.focus();
            keyInputRef.current.setSelectionRange(keyCursorPositionRef.current, keyCursorPositionRef.current);
        }
    });
    
    // Sync local value with prop value ONLY when not focused
    useEffect(() => {
        if (!isFocusedRef.current && lastSyncedValueRef.current !== value) {
            const newStringValue = getStringValue(value);
            setLocalValue(newStringValue);
            lastSyncedValueRef.current = value;
        }
    }, [value]);
    
    // Restore cursor position after render if textarea was focused
    useEffect(() => {
        if (isFocusedRef.current && textareaRef.current && cursorPositionRef.current !== null) {
            textareaRef.current.focus();
            textareaRef.current.setSelectionRange(cursorPositionRef.current, cursorPositionRef.current);
        }
    });
    
    if (isObject) {
        const entries = Object.entries(value as Record<string, unknown>);
        return (
            <div className="mb-3">
                <div className="flex items-start gap-2 justify-between" style={{ marginLeft: `${indent}px` }}>
                    <div className="flex items-start gap-2 flex-1 min-w-0">
                        {canEditKey ? (
                            <input
                                ref={keyInputRef}
                                type="text"
                                value={localKeyName}
                                onFocus={(e) => {
                                    isKeyFocusedRef.current = true;
                                    keyCursorPositionRef.current = e.target.selectionStart;
                                }}
                                onChange={(e) => {
                                    // Save cursor position before update
                                    keyCursorPositionRef.current = e.target.selectionStart;
                                    // Update local state immediately - don't update parent yet
                                    setLocalKeyName(e.target.value);
                                }}
                                onBlur={(e) => {
                                    isKeyFocusedRef.current = false;
                                    keyCursorPositionRef.current = null;
                                    const newKey = e.target.value.trim();
                                    if (!newKey) {
                                        // Revert to original key if empty
                                        setLocalKeyName(keyName);
                                    } else if (newKey !== keyName && onKeyChange) {
                                        // Update parent state only on blur
                                        onKeyChange(keyName, newKey);
                                    }
                                }}
                                className="text-sm font-semibold text-gray-900 font-mono bg-white border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[#7A0019] focus:border-transparent"
                            />
                        ) : (
                            <span className="text-sm font-semibold text-gray-900 font-mono">{keyName}</span>
                        )}
                        <span className="text-sm text-gray-600 font-mono">:</span>
                        <span className="text-sm text-gray-600 font-mono">{'{'}</span>
                    </div>
                    {onRemove && indentLevel === 0 && isEditable && (
                        <button
                            onClick={onRemove}
                            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors flex-shrink-0"
                            title="Remove field"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    )}
                </div>
                <div className="space-y-1 mt-1">
                    {entries.map(([nestedKey, nestedValue]) => (
                        <NestedField
                            key={nestedKey}
                            keyName={nestedKey}
                            value={nestedValue}
                            path={[...path, nestedKey]}
                            onValueChange={onValueChange}
                            isOriginalKey={isOriginalKey}
                            indentLevel={indentLevel + 1}
                            isEditable={isEditable}
                        />
                    ))}
                </div>
                <div className="flex items-start" style={{ marginLeft: `${indent}px` }}>
                    <span className="text-sm text-gray-600 font-mono">{'}'}</span>
                </div>
            </div>
        );
    }
    
    return (
        <div className="flex items-start gap-3 mb-2" style={{ marginLeft: `${indent}px` }}>
            <div className="flex items-center gap-1">
                {canEditKey ? (
                    <input
                        ref={keyInputRef}
                        type="text"
                        value={localKeyName}
                        onFocus={(e) => {
                            isKeyFocusedRef.current = true;
                            keyCursorPositionRef.current = e.target.selectionStart;
                        }}
                        onChange={(e) => {
                            // Save cursor position before update
                            keyCursorPositionRef.current = e.target.selectionStart;
                            // Update local state immediately - don't update parent yet
                            setLocalKeyName(e.target.value);
                        }}
                        onBlur={(e) => {
                            isKeyFocusedRef.current = false;
                            keyCursorPositionRef.current = null;
                            const newKey = e.target.value.trim();
                            if (!newKey) {
                                // Revert to original key if empty
                                setLocalKeyName(keyName);
                            } else if (newKey !== keyName && onKeyChange) {
                                // Update parent state only on blur
                                onKeyChange(keyName, newKey);
                            }
                        }}
                        className="text-sm font-semibold text-gray-900 font-mono bg-white border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[#7A0019] focus:border-transparent whitespace-nowrap"
                    />
                ) : (
                    <span className="text-sm font-semibold text-gray-900 font-mono whitespace-nowrap">{keyName}</span>
                )}
                <span className="text-sm text-gray-600 font-mono">:</span>
            </div>
            <div className="flex-1 min-w-0 flex items-start gap-2">
                {isEditable ? (
                    <textarea
                        ref={textareaRef}
                        value={localValue}
                        onFocus={(e) => {
                            isFocusedRef.current = true;
                            cursorPositionRef.current = e.target.selectionStart;
                        }}
                        onChange={(e) => {
                            // Save cursor position before update
                            cursorPositionRef.current = e.target.selectionStart;
                            // Update local state immediately - React won't reset this while focused
                            setLocalValue(e.target.value);
                        }}
                        onBlur={(e) => {
                            isFocusedRef.current = false;
                            cursorPositionRef.current = null;
                            // Parse and update parent state only on blur
                            let parsedValue: unknown = e.target.value;
                            // Try to parse as JSON if it looks like JSON
                            if (e.target.value.trim().startsWith('{') || e.target.value.trim().startsWith('[')) {
                                try {
                                    parsedValue = JSON.parse(e.target.value);
                                } catch {
                                    parsedValue = e.target.value;
                                }
                            }
                            lastSyncedValueRef.current = parsedValue;
                            onValueChange(path, parsedValue);
                        }}
                        rows={localValue.length < 50 ? 1 : 2}
                        className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-[#7A0019] focus:border-transparent font-mono"
                    />
                ) : (
                    <div className="w-full rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 bg-gray-50 font-mono min-h-[2.5rem] flex items-center">
                        <span className="break-words whitespace-pre-wrap">{localValue}</span>
                    </div>
                )}
                {onRemove && indentLevel === 0 && isEditable && (
                    <button
                        onClick={onRemove}
                        className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors flex-shrink-0 mt-1"
                        title="Remove field"
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                )}
            </div>
        </div>
    );
}

export default function AdminEditPage() {
    const searchParams = useSearchParams();
    const router = useRouter();
    const { cosmosData, loading } = useCosmosData();
    
    const resourceTypeRef = searchParams.get('type') as ResourceTypeIdJson | null;
    const resourceIdentity = searchParams.get('id') as ResourceIdJson | null;
    
    const [editedData, setEditedData] = useState<Record<string, unknown>>({});
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [isSaving, setIsSaving] = useState(false);
    const [originalKeys, setOriginalKeys] = useState<Set<string>>(new Set());
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [dataInitialized, setDataInitialized] = useState(false);
    const [validationErrors, setValidationErrors] = useState<string[]>([]);
    const [originalData, setOriginalData] = useState<Record<string, unknown>>({});
    const fieldRefsRef = useRef<Set<() => void>>(new Set());

    // Find the resource
    const resource = useMemo(() => {
        if (!resourceTypeRef || !resourceIdentity || !cosmosData?.resourceMap) {
            return null;
        }
        const resources = cosmosData.resourceMap[resourceTypeRef] || [];
        return resources.find(r => r.identity === resourceIdentity) || null;
    }, [resourceTypeRef, resourceIdentity, cosmosData?.resourceMap]);

    // Initialize edited data when resource is found
    useEffect(() => {
        if (loading) return; // Don't initialize while still loading
        
        if (!resourceTypeRef || !resourceIdentity) return; // Need valid params
        
        // Check if resourceMap has actually loaded data for this specific resource type
        // resourceMap[resourceTypeRef] will be undefined if not loaded yet, or [] if loaded but empty
        const resourceTypeData = cosmosData?.resourceMap?.[resourceTypeRef];
        const hasResourceTypeData = resourceTypeData !== undefined;
        
        if (resource && !dataInitialized) {
            // Resource found - initialize data
            const initialData = resource.extractedData || {};
            setEditedData(initialData);
            setOriginalData(initialData);
            setOriginalKeys(new Set(Object.keys(initialData)));
            setDataInitialized(true);
        } else if (hasResourceTypeData && !resource && !dataInitialized) {
            // Data has loaded for this resource type but resource not found - mark as initialized so we can show error
            setDataInitialized(true);
        }
    }, [resource, dataInitialized, loading, resourceTypeRef, resourceIdentity, cosmosData?.resourceMap]);

    // Re-validate when editedData changes to clear errors when fields are filled
    useEffect(() => {
        // Only validate if data has been initialized
        if (!dataInitialized) return;
        
        // Re-validate when data changes
        const validation = validateData();
        if (validation.isValid) {
            // Clear errors if validation passes
            if (validationErrors.length > 0) {
                setValidationErrors([]);
            }
        } else {
            // Update errors if they changed
            const currentErrorsStr = JSON.stringify(validationErrors);
            const newErrorsStr = JSON.stringify(validation.errors);
            if (currentErrorsStr !== newErrorsStr) {
                setValidationErrors(validation.errors);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [editedData, dataInitialized]);

    // Show loading while data is being fetched
    if (loading) {
        return (
            <PageLayout>
                <div className="tp-container py-6 sm:py-8 md:py-12 px-4 sm:px-6">
                    <div className="flex items-center justify-center py-12">
                        <div className="flex items-center gap-2 text-gray-500">
                            <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Loading...</span>
                        </div>
                    </div>
                </div>
            </PageLayout>
        );
    }

    if (!resourceTypeRef || !resourceIdentity || (!resource && dataInitialized)) {
        return (
            <PageLayout>
                <div className="tp-container py-6 sm:py-8 md:py-12 px-4 sm:px-6">
                    <div className="bg-red-50 border-l-4 border-red-500 rounded-r-lg p-4">
                        <div className="text-red-800">
                            Resource not found. Please check the URL parameters.
                        </div>
                        <button
                            onClick={() => router.push('/admin')}
                            className="mt-4 text-sm text-red-600 hover:text-red-800 underline"
                        >
                            Back to Admin Panel
                        </button>
                    </div>
                </div>
            </PageLayout>
        );
    }

    const updateNestedValue = (path: string[], value: unknown) => {
        setEditedData(prev => {
            const newData = { ...prev };
            let current: Record<string, unknown> = newData;
            
            // Navigate to the parent object
            for (let i = 0; i < path.length - 1; i++) {
                const key = path[i];
                if (typeof current[key] === 'object' && current[key] !== null && !Array.isArray(current[key])) {
                    current[key] = { ...current[key] as Record<string, unknown> };
                    current = current[key] as Record<string, unknown>;
                } else {
                    return prev; // Invalid path
                }
            }
            
            // Set the final value
            current[path[path.length - 1]] = value;
            return newData;
        });
    };

    const handleValueChange = (path: string[], value: unknown) => {
        updateNestedValue(path, value);
    };

    const handleAddField = () => {
        const newKey = `newField_${Date.now()}`;
        setEditedData(prev => ({ ...prev, [newKey]: '' }));
    };

    const handleRemoveField = (key: string) => {
        setEditedData(prev => {
            const newData = { ...prev };
            delete newData[key];
            return newData;
        });
    };

    const handleKeyChange = (oldKey: string, newKey: string) => {
        setEditedData(prev => {
            if (prev[newKey] !== undefined) {
                // Key already exists, don't change
                return prev;
            }
            const newData = { ...prev };
            newData[newKey] = newData[oldKey];
            delete newData[oldKey];
            return newData;
        });
    };

    // Helper function to check if a value is empty (only for simple values, not objects)
    const isEmptyValue = (value: unknown): boolean => {
        if (value === null || value === undefined) return true;
        if (typeof value === 'string') {
            const trimmed = value.trim();
            // Empty string is empty
            if (trimmed === '') return true;
            // But a string with content is not empty
            return false;
        }
        // For numbers, booleans, objects, arrays - if they exist, they're not empty
        // Only null, undefined, and empty strings are considered empty
        return false;
    };

    // Helper function to get a user-friendly field name
    const getFieldDisplayName = (key: string): string => {
        // For known fields, use their friendly name
        if (key === 'name') return 'Name';
        if (key === 'description') return 'Description';
        if (key === 'identity') return 'Identity';
        // For new fields, show "New Field" with the key name
        if (key.startsWith('newField_')) {
            return `New Field`;
        }
        // Otherwise capitalize the first letter of the key
        return key.charAt(0).toUpperCase() + key.slice(1);
    };

    // Helper function to deep compare objects (only for editable fields)
    const deepEqual = (obj1: unknown, obj2: unknown): boolean => {
        if (obj1 === obj2) return true;
        if (obj1 == null || obj2 == null) return false;
        if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return obj1 === obj2;
        
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);
        
        if (keys1.length !== keys2.length) return false;
        
        for (const key of keys1) {
            if (!keys2.includes(key)) return false;
            if (!deepEqual((obj1 as Record<string, unknown>)[key], (obj2 as Record<string, unknown>)[key])) {
                return false;
            }
        }
        
        return true;
    };

    const validateData = (): { isValid: boolean; errors: string[] } => {
        const errors: string[] = [];

        // Filter to only editable fields for comparison
        const editableFieldsInOriginal: Record<string, unknown> = {};
        const editableFieldsInEdited: Record<string, unknown> = {};
        
        for (const key of Object.keys(originalData)) {
            const isEditable = key === 'name' || key === 'description' || !originalKeys.has(key);
            if (isEditable) {
                editableFieldsInOriginal[key] = originalData[key];
            }
        }
        
        for (const key of Object.keys(editedData)) {
            const isEditable = key === 'name' || key === 'description' || !originalKeys.has(key);
            if (isEditable) {
                editableFieldsInEdited[key] = editedData[key];
            }
        }

        // 1. Check if there are any changes in editable fields
        const hasChanges = !deepEqual(editableFieldsInOriginal, editableFieldsInEdited);
        if (!hasChanges) {
            errors.push('No changes detected. Please make changes to editable fields before saving.');
            // Return early if no changes - no need to check empty fields
            return {
                isValid: false,
                errors
            };
        }

        // 2. Make sure editable fields are not empty (only check name, description, and new fields)
        const emptyFields: string[] = [];
        for (const [key, value] of Object.entries(editedData)) {
            const isEditable = key === 'name' || key === 'description' || !originalKeys.has(key);
            if (isEditable) {
                // Check if value is empty
                const isEmpty = isEmptyValue(value);
                if (isEmpty) {
                    const displayName = getFieldDisplayName(key);
                    emptyFields.push(displayName);
                }
            }
        }
        
        if (emptyFields.length > 0) {
            if (emptyFields.length === 1) {
                errors.push(`The field "${emptyFields[0]}" is empty. Please provide a value before saving.`);
            } else {
                errors.push(`The following fields are empty: ${emptyFields.join(', ')}. Please provide values for all fields before saving.`);
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    };

    const handleSaveClick = () => {
        // Force sync all field values before validation
        // Blur all active inputs/textareas to trigger their sync
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
            (activeElement as HTMLElement).blur();
        }
        
        // Wait a tick for blur handlers to complete, then validate
        setTimeout(() => {
            const validation = validateData();
            if (!validation.isValid) {
                setValidationErrors(validation.errors);
                return;
            }
            setValidationErrors([]);
            setShowConfirmDialog(true);
        }, 100); // Increased timeout to ensure blur handlers complete
    };

    const handleConfirmSave = async () => {
        if (!resource) return;
        
        setShowConfirmDialog(false);
        setIsSaving(true);
        setSaveStatus(null);
        
        try {
            // Get the meta information from the resource
            // Resource may have resourceTypeRef or resourceTypeId property
            // creationContext may have resourceRoleRef/resourceRoleId and executionRef/executionId
            type ResourceWithExtendedProps = ResourceJson & { 
                resourceTypeRef?: string; 
                resourceTypeId?: string; 
                creationContext?: {
                    resourceRoleRef?: string;
                    resourceRoleId?: string;
                    executionRef?: string;
                    executionId?: string;
                };
                path?: string;
            };
            const resourceWithExtended = resource as ResourceWithExtendedProps;
            const resourceTypeRefForMeta = resourceWithExtended.resourceTypeRef || resourceWithExtended.resourceTypeId || resourceTypeRef;
            
            // Get or create creationContext
            // Handle both naming conventions: resourceRoleRef/executionRef OR resourceRoleId/executionId
            const existingCreationContext = resourceWithExtended.creationContext;
            let resourceRoleRef: string;
            let executionRef: string;
            
            if (existingCreationContext && 
                (existingCreationContext.resourceRoleRef || existingCreationContext.resourceRoleId) &&
                (existingCreationContext.executionRef || existingCreationContext.executionId)) {
                // Use existing creationContext, mapping to the expected format
                resourceRoleRef = existingCreationContext.resourceRoleRef || existingCreationContext.resourceRoleId || '';
                executionRef = existingCreationContext.executionRef || existingCreationContext.executionId || '';
            } else {
                // Generate new creationContext if missing
                const executionIdentity = await getNewIdentity('execution' as const);
                resourceRoleRef = 'ROLE-Manual';
                executionRef = executionIdentity;
            }
            
            const meta = {
                identity: resource.identity,
                resourceTypeRef: resourceTypeRefForMeta,
                creationContext: {
                    resourceRoleRef: resourceRoleRef,
                    executionRef: executionRef,
                },
            } as unknown as Omit<ResourcePotentialOutputJson, 'kind'>;

            // Upload only the extractedData (editedData), not the full resource object
            // Pass editedData as an object, not a string - uploadResource will handle stringification
            const content = editedData;
            
            console.log('content:', content);
            console.log('meta:', meta);
            // First, upload the new resource
            const uploadRes = await uploadResource(meta, content);
            
            if (!uploadRes.success) {
                setSaveStatus(`✗ Upload failed: ${uploadRes.error ?? 'Unknown error'}`);
                setIsSaving(false);
                return;
            }

            // Get the old resource path from the original resource
            // The path is typically stored in the resource metadata or we need to construct it
            // If the resource has a path property, use it; otherwise construct from resourceTypeRef and identity
            const resourceTypeRefForPath = resourceWithExtended.resourceTypeRef || resourceWithExtended.resourceTypeId || resourceTypeRef;
            const oldPath = resourceWithExtended.path || `${resourceTypeRefForPath}/${resource.identity}`;
            const resourceTypeRefs = [resourceTypeRefForPath] as ResourceTypeIdJson[];

            // Delete the old resource
            try {
                const deleteRes = await deleteResource(oldPath, resourceTypeRefs);
                if (!deleteRes.success) {
                    console.warn('Failed to delete old resource:', deleteRes.error);
                    // Continue anyway since new resource is uploaded
                }
            } catch (deleteErr) {
                console.warn('Error deleting old resource:', deleteErr);
                // Continue anyway since new resource is uploaded
            }
            
            setSaveStatus(`✓ Successfully saved at: ${uploadRes.path}`);
            setTimeout(() => {
                router.push('/admin');
            }, 1500);
        } catch (err) {
            setSaveStatus(`✗ Save failed: ${(err as Error).message ?? 'Unknown error'}`);
        } finally {
            setIsSaving(false);
        }
    };

    const handleCancelSave = () => {
        setShowConfirmDialog(false);
    };

    const entries = Object.entries(editedData);
    console.log('entries:', entries);

    return (
        <PageLayout>
            <div className="tp-container py-6 sm:py-8 md:py-12 px-4 sm:px-6">
                <div className="mx-auto max-w-4xl w-full">
                    {/* Header */}
                    <div className="mb-6">
                        <button
                            onClick={() => router.push('/admin')}
                            className="text-sm text-[#7A0019] hover:text-[#5A0013] mb-4 inline-flex items-center gap-1"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                            Back to Admin Panel
                        </button>
                        <h1 className="text-2xl sm:text-3xl md:text-4xl font-extrabold tracking-tight text-[#7A0019] break-words mb-2">
                            Edit Resource
                        </h1>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                            <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded font-medium">
                                {resourceTypeRef}
                            </span>
                            <span className="font-mono">{resourceIdentity}</span>
                        </div>
                    </div>

                    {/* Resource Editor */}
                    <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                        <div className="flex items-center justify-between mb-6 pb-3 border-b border-gray-200">
                            <div>
                                <h2 className="text-lg font-semibold text-gray-900 mb-2">
                                    Resource Data
                                </h2>
                                <p className="text-sm text-gray-600">
                                    <span className="font-semibold text-[#7A0019]">name</span> and <span className="font-semibold text-[#7A0019]">description</span> fields are editable. Newly added fields are also editable. Other existing fields are read-only.
                                </p>
                            </div>
                            <button
                                onClick={handleAddField}
                                className="px-3 py-1.5 bg-[#7A0019] text-white rounded-md hover:bg-[#5A0013] transition-colors text-sm font-medium flex-shrink-0"
                            >
                                + Add Field
                            </button>
                        </div>

                        <div className="space-y-4">
                            {!resource || !dataInitialized ? (
                                <div className="flex items-center justify-center py-8">
                                    <div className="flex items-center gap-2 text-gray-500">
                                        <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <span className="text-sm">Loading resource data...</span>
                                    </div>
                                </div>
                            ) : entries.length === 0 ? (
                                <div className="text-center py-8 text-gray-500">
                                    No fields available.
                                </div>
                            ) : (
                                entries.map(([key, value]) => {
                                    const isOriginalKey = originalKeys.has(key);
                                    // Editable if: name/description OR newly added field (not in originalKeys)
                                    const isEditableField = key === 'name' || key === 'description' || !isOriginalKey;
                                    return (
                                        <div key={key} className={isEditableField ? 'p-4 bg-blue-50 rounded-lg border-2 border-blue-200' : 'p-4 bg-gray-50 rounded-lg border border-gray-200'}>
                                            {isEditableField && (
                                                <div className="flex items-center gap-2 mb-2">
                                                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                    </svg>
                                                    <span className="text-xs font-semibold text-blue-700 uppercase tracking-wide">Editable</span>
                                                </div>
                                            )}
                                            {!isEditableField && (
                                                <div className="flex items-center gap-2 mb-2">
                                                    <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                                    </svg>
                                                    <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">Read-only</span>
                                                </div>
                                            )}
                                            <NestedField
                                                keyName={key}
                                                value={value}
                                                path={[key]}
                                                onValueChange={handleValueChange}
                                                onKeyChange={isEditableField && !isOriginalKey ? handleKeyChange : undefined}
                                                isOriginalKey={isOriginalKey}
                                                onRemove={isEditableField && !isOriginalKey ? () => handleRemoveField(key) : undefined}
                                                indentLevel={0}
                                                isEditable={isEditableField}
                                            />
                                        </div>
                                    );
                                })
                            )}
                        </div>
                    </div>

                    {/* Validation Errors */}
                    {validationErrors.length > 0 && (
                        <div className="mt-6 bg-red-50 border-l-4 border-red-400 rounded-r-lg p-4 shadow-sm">
                            <div className="flex items-start">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-red-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="ml-3 flex-1">
                                    <h3 className="text-sm font-semibold text-red-800 mb-3">
                                        Please fix the following issues before saving:
                                    </h3>
                                    <ul className="space-y-2 text-sm text-red-700">
                                        {validationErrors.map((error, idx) => (
                                            <li key={idx} className="flex items-start">
                                                <span className="mr-2">•</span>
                                                <span>{error}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Save Controls */}
                    <div className="mt-8">
                        <SaveControls
                            formId="edit-resource-form"
                            buttonText="Save Changes"
                            disabled={isSaving || validationErrors.length > 0}
                            isValid={validationErrors.length === 0}
                            invalidMessage={validationErrors.length > 0 ? validationErrors[0] : undefined}
                            saveStatus={saveStatus}
                            isLoading={isSaving}
                        />
                        <form id="edit-resource-form" onSubmit={(e) => { e.preventDefault(); handleSaveClick(); }} />
                    </div>
                </div>
            </div>

            {/* Confirmation Dialog */}
            {showConfirmDialog && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                            Confirm Save
                        </h3>
                        <p className="text-gray-700 mb-6">
                            Are you sure you want to save these changes? This will replace the existing resource on the cloud.
                        </p>
                        <div className="flex gap-3 justify-end">
                            <button
                                onClick={handleCancelSave}
                                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors font-medium"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleConfirmSave}
                                className="px-4 py-2 bg-[#7A0019] text-white rounded-md hover:bg-[#5A0013] transition-colors font-medium"
                            >
                                Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </PageLayout>
    );
}

