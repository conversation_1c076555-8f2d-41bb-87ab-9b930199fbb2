'use client';

import { PageLayout } from '@/components/_root/PageLayout';
import { useRouter } from 'next/navigation';
import { useState, useMemo, useEffect } from 'react';
import type { ResourceJson, ResourceTypeIdJson } from '@toolproof-npm/schema';
import type { ResourceMap } from '@toolproof-npm/shared/types';
import { useCosmosData } from '@/components/spaces/cosmos/CosmosDataProvider' // Kanika: use this to list existing resources per resourceType
import { uploadResource, deleteResource } from '@/_lib/server/firebaseAdminHelpers'; // Kanika: use these to upload the new resource and delete the old one

export default function AdminPanel() {
    const { cosmosData, loading: cosmosLoading, error: cosmosError } = useCosmosData();
    const router = useRouter();

    // State management
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedTypes, setSelectedTypes] = useState<Set<ResourceTypeIdJson>>(new Set());
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 20;

    // Flatten all resources with their type
    const allResources: Array<{ resource: ResourceJson; resourceTypeRef: ResourceTypeIdJson }> = useMemo(() => {
        const resourceMap = cosmosData?.resourceMap;
        if (!resourceMap) return [];
        const result: Array<{ resource: ResourceJson; resourceTypeRef: ResourceTypeIdJson }> = [];
        Object.entries(resourceMap as ResourceMap).forEach(([resourceTypeRef, resources]) => {
            resources.forEach((resource) => {
                result.push({ resource, resourceTypeRef: resourceTypeRef as ResourceTypeIdJson });
            });
        });
        return result;
    }, [cosmosData?.resourceMap]);

    // Extract unique resource types for filter
    const availableResourceTypes = useMemo(() => {
        const resourceMap = cosmosData?.resourceMap;
        if (!resourceMap) return [];
        return Object.keys(resourceMap as ResourceMap) as ResourceTypeIdJson[];
    }, [cosmosData?.resourceMap]);

    // Filtering logic
    const filteredResources = useMemo(() => {
        let filtered = allResources;

        // Apply search filter
        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase().trim();
            filtered = filtered.filter(({ resource, resourceTypeRef }) => {
                const identityMatch = resource.identity.toLowerCase().includes(query);
                const typeMatch = resourceTypeRef.toLowerCase().includes(query);
                return identityMatch || typeMatch;
            });
        }

        // Apply type filter
        if (selectedTypes.size > 0) {
            filtered = filtered.filter(({ resourceTypeRef }) => selectedTypes.has(resourceTypeRef));
        }

        return filtered;
    }, [allResources, searchQuery, selectedTypes]);

    // Pagination logic
    const totalPages = Math.ceil(filteredResources.length / itemsPerPage);
    const paginatedResources = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filteredResources.slice(startIndex, endIndex);
    }, [filteredResources, currentPage, itemsPerPage]);

    // Reset to page 1 when filters change
    useEffect(() => {
        setCurrentPage(1);
    }, [searchQuery, selectedTypes]);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const dropdown = document.getElementById('type-filter-dropdown');
            if (dropdown && !dropdown.contains(event.target as Node)) {
                const button = event.target as HTMLElement;
                if (!button.closest('button') || !button.closest('button')?.parentElement?.querySelector('#type-filter-dropdown')) {
                    dropdown.classList.add('hidden');
                }
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    if (cosmosLoading) {
        return (
            <PageLayout>
                <div className="tp-container py-6 sm:py-8 md:py-12 px-4 sm:px-6">
                    <div className="flex items-center justify-center py-12">
                        <div className="flex items-center gap-2 text-gray-500">
                            <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Loading resources...</span>
                        </div>
                    </div>
                </div>
            </PageLayout>
        );
    }

    if (cosmosError) {
        return (
            <PageLayout>
                <div className="tp-container py-6 sm:py-8 md:py-12 px-4 sm:px-6">
                    <div className="flex items-center justify-center py-12">
                        <div className="text-red-600">
                            Failed to load resources: {String(cosmosError?.message ?? cosmosError)}
                        </div>
                    </div>
                </div>
            </PageLayout>
        );
    }

    // Toggle resource type in filter
    const toggleResourceType = (type: ResourceTypeIdJson) => {
        setSelectedTypes(prev => {
            const newSet = new Set(prev);
            if (newSet.has(type)) {
                newSet.delete(type);
            } else {
                newSet.add(type);
            }
            return newSet;
        });
    };

    // Clear all filters
    const clearFilters = () => {
        setSearchQuery('');
        setSelectedTypes(new Set());
    };

    const handleEdit = (resourceTypeRef: string, resourceIdentity: string) => {
        router.push(`/admin/edit?type=${encodeURIComponent(resourceTypeRef)}&id=${encodeURIComponent(resourceIdentity)}`);
    };

    return (
        <PageLayout>
            <div className="tp-container py-6 sm:py-8 md:py-12 px-4 sm:px-6">
                <div className="mx-auto max-w-6xl w-full">
                    <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold tracking-tight text-[#7A0019] break-words mb-2 sm:mb-3" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                        Admin Panel
                    </h1>
                    <p className="text-xs sm:text-sm text-[#7A0019]/80 break-words mb-6 sm:mb-8" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                        Manage and edit all resources in the system
                    </p>

                    {/* Search and Filter Controls */}
                    <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-4 sm:p-6 mb-6">
                        <div className="flex flex-col sm:flex-row gap-4">
                            {/* Search Bar */}
                            <div className="flex-1">
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </div>
                                    <input
                                        type="text"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        placeholder="Search by identity or type..."
                                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-[#7A0019] focus:border-[#7A0019] text-sm"
                                    />
                                </div>
                            </div>

                            {/* Resource Type Filter */}
                            <div className="relative">
                                <div className="relative">
                                    <button
                                        type="button"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            const dropdown = document.getElementById('type-filter-dropdown');
                                            if (dropdown) {
                                                dropdown.classList.toggle('hidden');
                                            }
                                        }}
                                        className="inline-flex items-center justify-between w-full sm:w-64 px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-[#7A0019] focus:border-[#7A0019]"
                                    >
                                        <span>
                                            {selectedTypes.size === 0
                                                ? 'All Types'
                                                : `${selectedTypes.size} type${selectedTypes.size > 1 ? 's' : ''} selected`}
                                        </span>
                                        <svg className="ml-2 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <div
                                        id="type-filter-dropdown"
                                        className="hidden absolute right-0 mt-2 w-64 sm:w-80 bg-white rounded-md shadow-lg z-10 border border-gray-200 max-h-96 overflow-y-auto"
                                        onClick={(e) => e.stopPropagation()}
                                    >
                                        <div className="p-2">
                                            <div className="px-3 py-2 border-b border-gray-200 flex items-center justify-between">
                                                <span className="text-xs font-semibold text-gray-700 uppercase">Filter by Type</span>
                                                {selectedTypes.size > 0 && (
                                                    <button
                                                        onClick={clearFilters}
                                                        className="text-xs text-[#7A0019] hover:text-[#5A0013]"
                                                    >
                                                        Clear all
                                                    </button>
                                                )}
                                            </div>
                                            <div className="py-2 space-y-1">
                                                {availableResourceTypes.map((type) => (
                                                    <label
                                                        key={type}
                                                        className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer rounded"
                                                    >
                                                        <input
                                                            type="checkbox"
                                                            checked={selectedTypes.has(type)}
                                                            onChange={() => toggleResourceType(type)}
                                                            className="h-4 w-4 text-[#7A0019] focus:ring-[#7A0019] border-gray-300 rounded"
                                                        />
                                                        <span className="ml-3 text-sm text-gray-700">{type}</span>
                                                    </label>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Selected Type Chips */}
                        {selectedTypes.size > 0 && (
                            <div className="mt-4 flex flex-wrap gap-2">
                                {Array.from(selectedTypes).map((type) => (
                                    <span
                                        key={type}
                                        className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700"
                                    >
                                        {type}
                                        <button
                                            onClick={() => toggleResourceType(type)}
                                            className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200 focus:outline-none"
                                        >
                                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                            </svg>
                                        </button>
                                    </span>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Resources List */}
                    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                        <div className="p-6 border-b border-gray-200">
                            <h2 className="text-lg font-semibold text-gray-900">
                                Resources ({filteredResources.length} of {allResources.length})
                            </h2>
                        </div>
                        <div className="divide-y divide-gray-200">
                            {paginatedResources.length === 0 ? (
                                <div className="p-8 text-center text-gray-500">
                                    {filteredResources.length === 0 && allResources.length > 0
                                        ? 'No resources match your filters'
                                        : 'No resources found'}
                                </div>
                            ) : (
                                paginatedResources.map(({ resource, resourceTypeRef }) => (
                                    <div
                                        key={`${resourceTypeRef}-${resource.identity}`}
                                        className="p-4 hover:bg-gray-50 transition-colors"
                                    >
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-3">
                                                    <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded font-medium">
                                                        {resourceTypeRef}
                                                    </span>
                                                    <span className="text-sm font-medium text-gray-900 truncate">
                                                        {resource.identity}
                                                    </span>
                                                </div>
                                                {resource.extractedData && Object.keys(resource.extractedData).length > 0 && (
                                                    <div className="mt-2 text-xs text-gray-500">
                                                        {Object.keys(resource.extractedData).length} field(s)
                                                    </div>
                                                )}
                                            </div>
                                            <button
                                                onClick={() => handleEdit(resourceTypeRef, resource.identity)}
                                                className="ml-4 p-2 text-gray-600 hover:text-[#7A0019] hover:bg-gray-100 rounded-md transition-colors"
                                                title="Edit resource"
                                            >
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>

                        {/* Pagination Controls */}
                        {totalPages > 1 && (
                            <div className="px-6 py-4 border-t border-gray-200 flex flex-col sm:flex-row items-center justify-between gap-4">
                                <div className="text-sm text-gray-700">
                                    Showing <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
                                    <span className="font-medium">
                                        {Math.min(currentPage * itemsPerPage, filteredResources.length)}
                                    </span>{' '}
                                    of <span className="font-medium">{filteredResources.length}</span> results
                                </div>
                                <div className="flex items-center gap-2">
                                    <button
                                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                        disabled={currentPage === 1}
                                        className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-1 focus:ring-[#7A0019] focus:border-[#7A0019]"
                                    >
                                        Previous
                                    </button>
                                    <div className="flex items-center gap-1">
                                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                            let pageNum: number;
                                            if (totalPages <= 5) {
                                                pageNum = i + 1;
                                            } else if (currentPage <= 3) {
                                                pageNum = i + 1;
                                            } else if (currentPage >= totalPages - 2) {
                                                pageNum = totalPages - 4 + i;
                                            } else {
                                                pageNum = currentPage - 2 + i;
                                            }
                                            return (
                                                <button
                                                    key={pageNum}
                                                    onClick={() => setCurrentPage(pageNum)}
                                                    className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-1 focus:ring-[#7A0019] ${
                                                        currentPage === pageNum
                                                            ? 'bg-[#7A0019] text-white'
                                                            : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                                                    }`}
                                                >
                                                    {pageNum}
                                                </button>
                                            );
                                        })}
                                    </div>
                                    <span className="text-sm text-gray-700 px-2">
                                        Page {currentPage} of {totalPages}
                                    </span>
                                    <button
                                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                        disabled={currentPage === totalPages}
                                        className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-1 focus:ring-[#7A0019] focus:border-[#7A0019]"
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </PageLayout>
    );
}
