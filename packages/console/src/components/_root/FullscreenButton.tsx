'use client';

import { ArrowsPointingOutIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';

interface FullscreenButtonProps {
  href?: string;
  onClick?: () => void;
}

export function FullscreenButton({ href = '/', onClick }: FullscreenButtonProps) {
  const router = useRouter();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      router.push(href);
    }
  };

  return (
    <button
      onClick={handleClick}
      className="fixed bottom-6 right-6 z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-all hover:scale-110 flex items-center gap-2"
      aria-label="View demo in fullscreen"
    >
      <ArrowsPointingOutIcon className="w-5 h-5" />
      <span className="hidden sm:inline font-medium">View Fullscreen</span>
    </button>
  );
}

