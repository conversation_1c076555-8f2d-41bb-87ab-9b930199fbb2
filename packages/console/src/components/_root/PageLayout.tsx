'use client';

import { HomeTopHeader } from '@/components/builders/ui/home-top-header';
import { usePathname } from 'next/navigation';
import { ReactNode } from 'react';

interface PageLayoutProps {
  children: ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  contentBg?: string;
}

export function PageLayout({ children, showHeader = true, showFooter = true, contentBg = 'bg-white' }: PageLayoutProps) {
  const pathname = usePathname();
  const footerLinkClass = (href: string) => 
    `tp-footer-link ${pathname?.startsWith(href) ? 'tp-footer-link-active' : ''}`;

  return (
    <div className="h-full w-full flex flex-col overflow-hidden" style={{ maxWidth: '100vw', minWidth: 0 }}>
      {showHeader && <HomeTopHeader />}
      <main className={`flex-1 min-h-0 ${contentBg} w-full overflow-y-auto overflow-x-hidden`} style={{ maxWidth: '100vw', minWidth: 0 }}>
        {children}
      </main>
      {showFooter && (
        <footer id="home-footer" className="w-full py-4 sm:py-6 border-t-2 border-[#FFCC33] bg-white mt-auto flex-shrink-0">
          <div className="tp-container flex items-center justify-center gap-4 md:gap-6">
            <a href="/terms" className={footerLinkClass('/terms')}>
              Terms & Conditions
            </a>
            <a href="/privacy-policy" className={footerLinkClass('/privacy-policy')}>
              Privacy Policy
            </a>
          </div>
        </footer>
      )}
    </div>
  );
}

