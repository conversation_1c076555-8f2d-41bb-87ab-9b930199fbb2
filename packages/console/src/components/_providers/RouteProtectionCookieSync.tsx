'use client';

import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/_lib/client/redux/hooks';
import { setEnforceTeamRoutes } from '@/_lib/client/redux/features/configSlice';

const COOKIE_NAME = 'tp_enforce_team_routes';

function readCookie(name: string): string | undefined {
  if (typeof document === 'undefined') return undefined;
  const match = document.cookie
    .split(';')
    .map((c) => c.trim())
    .find((c) => c.startsWith(`${name}=`));
  if (!match) return undefined;
  return decodeURIComponent(match.substring(name.length + 1));
}

function writeCookie(name: string, value: string) {
  if (typeof document === 'undefined') return;
  // 1 year
  const maxAge = 60 * 60 * 24 * 365;
  document.cookie = `${name}=${encodeURIComponent(value)};path=/;max-age=${maxAge};samesite=lax`;
}

export default function RouteProtectionCookieSync() {
  const dispatch = useAppDispatch();
  const enforceTeamRoutes = useAppSelector((state) => state.config.enforceTeamRoutes);

  // Hydrate Redux from cookie (client-only). In production, always force enforcement on.
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      if (!enforceTeamRoutes) dispatch(setEnforceTeamRoutes(true));
      writeCookie(COOKIE_NAME, '1');
      return;
    }

    const raw = readCookie(COOKIE_NAME);
    if (raw === undefined) return;

    const next = !(raw === '0' || raw.toLowerCase() === 'false');
    if (next !== enforceTeamRoutes) {
      dispatch(setEnforceTeamRoutes(next));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Persist Redux -> cookie
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      // Never allow disabling enforcement in prod.
      if (!enforceTeamRoutes) dispatch(setEnforceTeamRoutes(true));
      writeCookie(COOKIE_NAME, '1');
      return;
    }

    writeCookie(COOKIE_NAME, enforceTeamRoutes ? '1' : '0');
  }, [dispatch, enforceTeamRoutes]);

  return null;
}
