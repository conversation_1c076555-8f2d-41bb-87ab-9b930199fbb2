'use client'
import { Toaster } from 'react-hot-toast';
import CosmosDataProvider from '@/explorer/spaces/cosmos/CosmosDataProvider';
import GenesisDataProvider from '@/explorer/spaces/genesis/GenesisDataProvider';

export default function ClientProvider({ children }: { children: React.ReactNode }) {
    return (
        <CosmosDataProvider>
            <GenesisDataProvider>
                <Toaster position='top-right' />
                {children}
            </GenesisDataProvider>
        </CosmosDataProvider>
    );
}