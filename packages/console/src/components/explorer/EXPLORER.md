
# Explorer (Console): Runtime + Spaces + Drivers

This document describes how the Explorer 3D experience works in the console package.

Explorer is composed from:

- **A reusable 3D runtime library** (`@toolproof-npm/visualization`) that provides a Canvas/XR shell plus a generic controller for space switching and portal transitions.
- **Console-owned “spaces”** (domain visuals + interactions) like Genesis and Cosmos.
- **Console-owned “drivers”** (domain orchestration) that choose what to render and when.

## Core concepts

### Runtime (library)

From `@toolproof-npm/visualization`:

- `Runtime` (Canvas/XR shell)
	- Owns the R3F `<Canvas>`, XR integration, camera wiring, and `OrbitControls`.
	- Applies `RuntimeCameraConfig` to camera position/target/FOV.
	- Disables orbit controls during an active XR session.
- `RuntimeController` (orchestrator)
	- Owns the current `activeSpace`.
	- Accepts a `renderSpace(...)` callback to render the active space’s content.
	- Accepts portal descriptors (via `setPortal`) and can run portal transitions.

### Space (console)

A space is a self-contained 3D scene fragment for a domain concept.

Examples:

- Genesis: `src/components/explorer/spaces/genesis/GenesisSpace.tsx`
- Cosmos: `src/components/explorer/spaces/cosmos/*`

Spaces do not handle app-wide navigation; they expose state and events that a driver can interpret.

### Driver (console)

A driver is a client component that owns the “script” and wiring:

- Chooses the initial space and camera config.
- Decides which space to render.
- Translates space-specific events into generic runtime actions (space switching, portal descriptors, etc).

Currently, the only driver is Demo:

- `src/components/explorer/drivers/Demo.tsx`

### Cosmos data + views (console)

Explorer spaces commonly need access to Cosmos resources (resourceMap + derived maps like `jobMap`).
To avoid threading “visibility” and stage-specific filtering props through every component, the console uses a two-layer provider approach:

- **Canonical data**: `CosmosDataProvider`
	- File: `src/components/explorer/spaces/cosmos/CosmosDataProvider.tsx`
	- Owns acquisition/merging of resources into a canonical `resourceMap`.
	- In mock mode, it merges static mock naturals (JSON) with any dynamically created mock naturals.
	- Exposes canonicalization helpers (by `path`) for provenance-aware consumers.

- **View layer (driver-owned)**: `CosmosDataViewProvider`
	- File: `src/components/explorer/spaces/cosmos/CosmosDataViewProvider.tsx`
	- Applies *rendering-only* transforms to the canonical resource map:
		- `prune(resources)`: remove items that should be hidden for the current stage.
		- `sort(resources)`: reorder items for presentation.
	- Transforms can be configured per resource type (e.g. only prune Naturals) or via a `default` transform.
	- Always operates immutably (clones arrays before pruning/sorting) and recomputes derived maps (e.g. `jobMap`, `loopableJobs`) from the transformed resource map.

#### How drivers use it

Drivers decide *what to show* at a given stage by providing transforms.

Example (Demo driver):

- `src/components/explorer/drivers/Demo.tsx` wraps the Cosmos subtree in:
	- `<CosmosDataViewProvider transforms={cosmosDataTransforms}> ... </CosmosDataViewProvider>`

This lets the driver implement stage-specific “visibility” without changing CosmosSpace internals.

#### How spaces consume it

Spaces and builder UIs should typically use:

- `useCosmosDataView()` (from `CosmosDataViewProvider`)

…so they automatically respect driver-owned view transforms.
Only use `useCosmosData()` directly when you explicitly need the raw canonical resource map and will *not* apply visibility filtering.

## How rendering works

At a high level, a page mounts a driver component. The driver renders a `RuntimeController` and provides:

- `initialSpace`
- `cameraConfig: RuntimeCameraConfig`
- `renderSpace({ activeSpace, orbitControlsRef, setPortal })`

The `renderSpace` callback is the key boundary:

- `activeSpace` tells the driver which space is currently active.
- `orbitControlsRef` lets spaces interact with orbit controls (e.g., update target).
- `setPortal(...)` lets the driver declare a portal overlay for the current space.

## Space switching and transitions

`RuntimeController` exposes an imperative handle (`RuntimeControllerHandle`) that drivers can use to navigate without changing routes:

- `setActiveSpace(space)` switches immediately.
- `transitionTo(space, { via: 'portal', portalId? })` runs a portal transition when a matching portal is currently visible; otherwise it falls back to `setActiveSpace`.
- `getStatus()` / `subscribeStatus(listener)` allows the driver to mirror runtime state (active space, portal, transition state).

### Portal descriptors

The portal overlay is driven by a `PortalDescriptor`:

- `id`: stable identifier for the transition
- `from` / `to`: the source/destination spaces
- `position` / `rotation` / `scale`: placement in the active space
- `visible`: whether the portal should be shown

`RuntimeController` renders the portal only when `portal.visible && portal.from === activeSpace`.

### Commit point

When transitioning via a portal, the destination space is committed only after the portal animation completes:

- `onTransitionComplete(true)` commits to `portal.to`.
- `onTransitionComplete(false)` cancels and returns to the non-transitioning state.

## Portal ownership (`portalOwner`)

Some spaces can either:

- **Render their own portal**, or
- **Declare a portal descriptor for an external host** (the runtime controller) to render.

Genesis supports both modes via `portalOwner`:

- `portalOwner="space"`
	- Genesis renders the portal internally.
- `portalOwner="external"`
	- Genesis does not render a portal; it emits a `GenesisPortalDescriptor` via `onPortalDescriptorChange(...)`.
	- The driver maps that descriptor into a generic `PortalDescriptor` and passes it to `RuntimeController` via `setPortal(...)`.

This keeps the runtime/controller generic and keeps space-specific geometry/layout logic inside the space.

## XR

The runtime exports an XR store (`runtimeXrStore`) from `@toolproof-npm/visualization`.

- Use it for UI such as “Enter VR”.
- Orbit controls are automatically disabled while an XR session is active.

## Where to look

- Driver: `src/components/explorer/drivers/Demo.tsx`
- Genesis space (portalOwner + descriptor): `src/components/explorer/spaces/genesis/GenesisSpace.tsx`
- Runtime controller + portal transitions (library): `core/packages/visualization/src/runtime/RuntimeController.tsx`
- Runtime view (Canvas/XR shell, library): `core/packages/visualization/src/runtime/RuntimeView.tsx`
- Portal primitives (library): `core/packages/visualization/src/primitives/*`

