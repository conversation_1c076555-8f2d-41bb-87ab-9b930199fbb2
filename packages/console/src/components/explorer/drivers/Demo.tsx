'use client';

import StrategyBuilderController from '@/components/builders/strategy/StrategyBuilderController';
import StrategyBuilderControllerR3F from '@/components/explorer/spaces/cosmos/StrategyBuilderControllerR3F';
import {
  RuntimeController,
  type RuntimeCameraConfig,
  type RuntimeController<PERSON>andle,
  type RuntimeControllerStatus,
  type PortalDescriptor,
} from '@toolproof-npm/visualization';
import GenesisSpace, { type GenesisPortalDescriptor, type GenesisStage } from '@/explorer/spaces/genesis/GenesisSpace';
import strategyExecutionData from '@/components/explorer/spaces/cosmos/strategyExecution.json';
import StrategyRunnerController from '@/components/explorer/spaces/cosmos/StrategyRunnerController';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAppSelector } from '@/_lib/client/redux/hooks';
import CosmosDataViewProvider, { type CosmosResourceMapTransforms } from '@/explorer/spaces/cosmos/CosmosDataViewProvider';

type SpaceId = 'genesis' | 'cosmos';

type GenesisView = 'data' | 'schema' | 'instances' | 'portal';

type CosmosMode = 'runner' | 'builder';

type DemoStageLabel = 'data' | 'schema' | 'instances' | 'portal' | 'run' | 'build';

function getGenesisViewForInitialStage(stage: number): GenesisView {
  switch (stage) {
    case 1:
      return 'schema';
    case 2:
      return 'instances';
    case 3:
      return 'portal';
    default:
      return 'data';
  }
}

function mapGenesisViewToStage(view: GenesisView): GenesisStage {
  switch (view) {
    case 'data':
      return 0;
    case 'schema':
      return 1;
    case 'instances':
      return 2;
    case 'portal':
      return 3;
  }
}

function getCameraConfig(params: { activeSpace: SpaceId; cosmosMode: CosmosMode }): RuntimeCameraConfig {
  if (params.activeSpace === 'genesis') {
    return {
      position: [0, 1.5, 10],
      target: [0, 1, 0],
      fov: 50,
    };
  }

  // cosmos (runner or builder)
  return {
    position: [0, 8, 16],
    target: [0, 0, 0],
    fov: 55,
  };
}

export default function Demo() {
  const initialDemoStageIndex = useAppSelector((state) => state.config.initialDemoStageIndex);
  const replayStartedRef = useRef(false);
  const runtimeRef = useRef<RuntimeControllerHandle | null>(null);
  const [runtimeStatus, setRuntimeStatus] = useState<RuntimeControllerStatus | null>(null);

  const initialCosmosMode: CosmosMode = initialDemoStageIndex === 5 ? 'builder' : 'runner';
  const [cosmosMode, setCosmosMode] = useState<CosmosMode>(initialCosmosMode);

  const [genesisView, setGenesisView] = useState<GenesisView>(() => getGenesisViewForInitialStage(initialDemoStageIndex));

  const initialSpace: SpaceId = initialDemoStageIndex <= 3 ? 'genesis' : 'cosmos';
  const activeSpace: SpaceId = useMemo(() => {
    return (runtimeStatus?.activeSpace as SpaceId | undefined) ?? initialSpace;
  }, [runtimeStatus?.activeSpace, initialSpace]);

  const demoStageLabel: DemoStageLabel = useMemo(() => {
    if (activeSpace === 'genesis') {
      return genesisView;
    }

    return cosmosMode === 'builder' ? 'build' : 'run';
  }, [activeSpace, cosmosMode, genesisView]);

  const demoStageNumber = useMemo(() => {
    switch (demoStageLabel) {
      case 'data':
        return 0;
      case 'schema':
        return 1;
      case 'instances':
        return 2;
      case 'portal':
        return 3;
      case 'run':
        return 4;
      case 'build':
        return 5;
    }
  }, [demoStageLabel]);

  const [showDetailedTooltip, setShowDetailedTooltip] = useState(false);
  const [isRunnerAnimating, setIsRunnerAnimating] = useState(false);

  const cosmosDataTransforms: CosmosResourceMapTransforms | undefined = useMemo(() => {
    // Stage 4: show Natural 1 + all Naturals produced during the run.
    if (demoStageLabel !== 'run') return undefined;

    return {
      'TYPE-Natural': {
        prune: (resources) => {
          return resources.filter((resource) => {
            const extractedIdentity = (resource as { extractedData?: { identity?: unknown } }).extractedData?.identity;
            const isNatural1FromGenesis =
              extractedIdentity === 1 &&
              (resource as { creationContext?: { executionRef?: unknown } }).creationContext?.executionRef === 'EXECUTION-Genesis';

            const isProducedNatural =
              (resource as { creationContext?: { executionRef?: unknown } }).creationContext?.executionRef !== 'EXECUTION-Genesis';

            return isNatural1FromGenesis || isProducedNatural;
          });
        },
      },
    };
  }, [demoStageLabel]);

  const goToGenesisView = useCallback((view: GenesisView) => {
    const handle = runtimeRef.current;
    if (!handle) return;
    handle.setActiveSpace('genesis');
    setGenesisView(view);
  }, []);

  const goToCosmosMode = useCallback(
    (mode: CosmosMode, opts?: { viaPortal?: boolean }) => {
      const handle = runtimeRef.current;
      if (!handle) return;

      setCosmosMode(mode);

      // If we are in the genesis portal view and we're entering runner, prefer the portal transition.
      if (mode === 'runner' && opts?.viaPortal && activeSpace === 'genesis' && genesisView === 'portal') {
        handle.transitionTo('cosmos', { via: 'portal', portalId: 'genesis-to-cosmos' });
        return;
      }

      handle.setActiveSpace('cosmos');
    },
    [activeSpace, genesisView]
  );

  const next = useCallback(() => {
    if (activeSpace === 'genesis') {
      switch (genesisView) {
        case 'data':
          goToGenesisView('schema');
          return;
        case 'schema':
          goToGenesisView('instances');
          return;
        case 'instances':
          goToGenesisView('portal');
          return;
        case 'portal':
          goToCosmosMode('runner', { viaPortal: true });
          return;
      }
    }

    // cosmos
    if (cosmosMode === 'runner') {
      goToCosmosMode('builder');
    }
  }, [activeSpace, cosmosMode, genesisView, goToCosmosMode, goToGenesisView]);

  const prev = useCallback(() => {
    if (activeSpace === 'genesis') {
      switch (genesisView) {
        case 'data':
          return;
        case 'schema':
          goToGenesisView('data');
          return;
        case 'instances':
          goToGenesisView('schema');
          return;
        case 'portal':
          goToGenesisView('instances');
          return;
      }
    }

    // cosmos
    if (cosmosMode === 'builder') {
      goToCosmosMode('runner');
      return;
    }

    // runner -> back to genesis portal view
    goToGenesisView('portal');
  }, [activeSpace, cosmosMode, genesisView, goToCosmosMode, goToGenesisView]);

  const showLoading = false;

  const handleNext = useCallback(() => {
    if (showLoading) return;
    next();
  }, [showLoading, next]);

  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      const wantsNext = e.key === 'ArrowRight' || e.key === 'PageDown' || e.key === ' ';
      const wantsPrev = e.key === 'ArrowLeft' || e.key === 'PageUp';

      if (wantsNext && showLoading) return;
      if (wantsNext) next();
      if (wantsPrev) prev();
    };

    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [next, prev, showLoading]);

  useEffect(() => {
    const handle = runtimeRef.current;
    if (!handle) return;

    // If user enters the portal (double click), reflect the transition in the demo stage.
    return handle.subscribeStatus((status) => {
      setRuntimeStatus(status);
      // If we entered cosmos via portal double-click, prefer runner mode unless the user
      // explicitly selected builder.
      if (status.activeSpace === 'cosmos' && cosmosMode !== 'builder') {
        setCosmosMode('runner');
      }
    });
  }, [cosmosMode]);

  const cameraConfig = useMemo(() => {
    return getCameraConfig({ activeSpace, cosmosMode });
  }, [activeSpace, cosmosMode]);

  const replayRecordedRun = useCallback(() => {
    if (replayStartedRef.current) return;
    replayStartedRef.current = true;

    const events = strategyExecutionData as unknown[];
    // Replay through the same live pipeline as streaming execution.
    // StrategyRunnerController(live) listens for 'toolproof:graphEvent'.
    events.forEach((detail, i) => {
      window.setTimeout(() => {
        window.dispatchEvent(new CustomEvent('toolproof:graphEvent', { detail }));
      }, i * 10);
    });
  }, []);

  return (
    <div className="relative h-screen w-full">
      <RuntimeController
        ref={runtimeRef}
        initialSpace={initialSpace}
        cameraConfig={cameraConfig}
        renderSpace={({ activeSpace, orbitControlsRef, setPortal }) => {
          if (activeSpace === 'genesis') {
            return (
              <GenesisSpace
                stage={mapGenesisViewToStage(genesisView)}
                showDetailedTooltip={showDetailedTooltip}
                orbitControlsRef={orbitControlsRef}
                portalOwner="external"
                onPortalDescriptorChange={(desc: GenesisPortalDescriptor | null) => {
                  if (!desc) {
                    setPortal(undefined);
                    return;
                  }

                  const portal: PortalDescriptor = {
                    id: 'genesis-to-cosmos',
                    from: 'genesis',
                    to: 'cosmos',
                    ...desc,
                  };

                  setPortal(portal);
                }}
              />
            );
          }

          // cosmos
          return (
            <CosmosDataViewProvider transforms={cosmosDataTransforms}>
              {cosmosMode === 'builder' ? (
                <StrategyBuilderController>
                  <StrategyBuilderControllerR3F />
                </StrategyBuilderController>
              ) : (
                <StrategyRunnerController live={true} />
              )}
            </CosmosDataViewProvider>
          );
        }}
      />

      <div className="absolute left-4 top-4 z-10 flex items-center gap-2">
        <button className="rounded bg-white/80 px-3 py-1 text-sm" onClick={prev}>
          Prev
        </button>
        <button
          className={`rounded bg-white/80 px-3 py-1 text-sm flex items-center gap-2 ${
            showLoading ? 'opacity-70 cursor-not-allowed' : ''
          }`}
          onClick={() => {
            if (showLoading) return;
            handleNext();
          }}
          disabled={showLoading}
        >
          Next
          {showLoading && (
            <svg
              className="animate-spin h-3 w-3 text-gray-700"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          )}
        </button>
        <div className="rounded bg-white/80 px-3 py-1 text-sm">
          Stage {demoStageNumber}: {demoStageLabel}
        </div>
      </div>

      {activeSpace === 'genesis' ? (
        <button
          type="button"
          onClick={() => setShowDetailedTooltip((prevVal) => !prevVal)}
          className="absolute right-4 top-4 z-10 rounded border border-gray-300 bg-white/90 px-3 py-2 text-xs"
        >
          {showDetailedTooltip ? 'Show Key Only' : 'Show Key + Data'}
        </button>
      ) : null}

      {demoStageLabel === 'run' ? (
        <button
          type="button"
          onClick={replayRecordedRun}
          className="absolute right-4 top-4 z-10 rounded bg-white/80 px-3 py-1 text-sm"
        >
          {isRunnerAnimating ? 'Running…' : 'Replay Recording'}
        </button>
      ) : null}
    </div>
  );
}
