'use client';

import type { ReactNode } from 'react';
import type { ResourceJson, ResourceTypeIdentityJson } from '@toolproof-npm/schema';
import type { ResourceMap } from '@toolproof-npm/shared/types';
import { createContext, useContext, useMemo } from 'react';
import { extractJobMap } from '@toolproof-npm/shared/utils';

import { getLoopableJobIdentities } from '@/builders/strategy/_lib/utils/loopableJobs';
import { useCosmosData, type CosmosDataContextValue } from '@/explorer/spaces/cosmos/CosmosDataProvider';

export type ResourceListTransforms = {
  prune?: (resources: ResourceJson[]) => ResourceJson[];
  sort?: (resources: ResourceJson[]) => ResourceJson[];
};

export type CosmosResourceMapTransforms = {
  default?: ResourceListTransforms;
} & Partial<Record<ResourceTypeIdentityJson, ResourceListTransforms>>;

export type CosmosDataViewContextValue = CosmosDataContextValue;

const CosmosDataViewContext = createContext<CosmosDataViewContextValue | null>(null);

export function useCosmosDataView(): CosmosDataViewContextValue {
  const context = useContext(CosmosDataViewContext);
  if (!context) {
    throw new Error('useCosmosDataView must be used within a CosmosDataViewProvider');
  }
  return context;
}

function hasAnyTransforms(transforms: CosmosResourceMapTransforms | undefined): boolean {
  if (!transforms) return false;
  if (transforms.default?.prune || transforms.default?.sort) return true;
  for (const [key, val] of Object.entries(transforms)) {
    if (key === 'default') continue;
    if (val?.prune || val?.sort) return true;
  }
  return false;
}

function applyResourceListTransforms(
  resources: ResourceJson[] | undefined,
  transforms: ResourceListTransforms
): ResourceJson[] {
  const base = resources ?? [];

  // Always operate on a cloned array so user-provided transforms (especially sort)
  // cannot mutate the raw resource list.
  let working = [...base];

  if (transforms.prune) {
    working = transforms.prune(working);
  }

  if (transforms.sort) {
    working = transforms.sort([...working]);
  }

  return working;
}

function applyResourceMapTransforms(
  resourceMap: ResourceMap,
  transforms: CosmosResourceMapTransforms
): ResourceMap {
  const next = {} as ResourceMap;

  const entries = Object.entries(resourceMap) as Array<[ResourceTypeIdentityJson, ResourceJson[]]>;
  for (const [resourceTypeRef, resources] of entries) {
    const perType = transforms[resourceTypeRef as ResourceTypeIdentityJson];
    const merged: ResourceListTransforms | null = perType || transforms.default || null;

    if (!merged || (!merged.prune && !merged.sort)) {
      next[resourceTypeRef] = resources;
      continue;
    }

    next[resourceTypeRef] = applyResourceListTransforms(resources, merged);
  }

  return next;
}

interface CosmosDataViewProviderProps {
  children: ReactNode;
  transforms?: CosmosResourceMapTransforms;
}

export default function CosmosDataViewProvider({ children, transforms }: CosmosDataViewProviderProps) {
  const base = useCosmosData();

  const value: CosmosDataViewContextValue = useMemo(() => {
    if (!hasAnyTransforms(transforms)) {
      // Mirror the raw provider exactly (including helper function identities).
      return base;
    }

    const nextResourceMap = applyResourceMapTransforms(base.cosmosData.resourceMap, transforms!);
    const jobMap = extractJobMap(nextResourceMap);
    const loopableJobs = getLoopableJobIdentities(jobMap);

    return {
      ...base,
      cosmosData: {
        ...base.cosmosData,
        resourceMap: nextResourceMap,
        jobMap,
        loopableJobs,
      },
      // Forward raw canonicalization helpers for now.
      resolveCanonicalIdentity: base.resolveCanonicalIdentity,
      getCanonicalResourceByIdentity: base.getCanonicalResourceByIdentity,
      getCanonicalResourceByPath: base.getCanonicalResourceByPath,
      getProvenanceByPath: base.getProvenanceByPath,
    };
  }, [base, transforms]);

  return <CosmosDataViewContext.Provider value={value}>{children}</CosmosDataViewContext.Provider>;
}
