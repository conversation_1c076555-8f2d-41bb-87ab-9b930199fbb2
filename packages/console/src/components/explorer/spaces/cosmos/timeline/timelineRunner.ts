import type { StrategyTimeline, TimelineSegment } from './timelineTypes';
import { findSegmentAt, phaseProgress } from './timelineBuilder';

/**
 * Configuration options for TimelineRunner
 */
export interface TimelineRunnerOptions {
  /** Enable continuous looping */
  loop?: boolean;
  /** Idle duration before loop restart (ms) */
  loopBreakMs?: number;
}

/**
 * Animation tick data provided to onTick handlers
 */
export interface TimelineTick {
  /** Absolute time from performance.now() */
  nowMs: number;
  /** Time within current loop (excludes pauses) */
  playheadMs: number;
  /** Current active segment */
  segment: TimelineSegment;
  /** 0-1 progress within segment */
  t: number;
}

export type TickHandler = (tick: TimelineTick) => void;
export type PhaseChangeHandler = (
  prev: TimelineSegment | null,
  next: TimelineSegment
) => void;

/**
 * RAF-based animation clock that drives timeline playback
 */
export class TimelineRunner {
  private startEpoch = 0;
  private pausedAt = 0;
  private pausedAccum = 0;
  private rafId: number | null = null;
  private running = false;
  private lastSegment: TimelineSegment | null = null;
  private onTickHandlers: TickHandler[] = [];
  private onPhaseChangeHandlers: PhaseChangeHandler[] = [];

  constructor(
    private timeline: StrategyTimeline,
    private opts: TimelineRunnerOptions = {}
  ) {}

  /**
   * Subscribe to animation ticks (called every frame)
   * @returns Unsubscribe function
   */
  onTick(cb: TickHandler): () => void {
    this.onTickHandlers.push(cb);
    return () => this.off(this.onTickHandlers, cb);
  }

  /**
   * Subscribe to phase change events (called when segment changes)
   * @returns Unsubscribe function
   */
  onPhaseChange(cb: PhaseChangeHandler): () => void {
    this.onPhaseChangeHandlers.push(cb);
    return () => this.off(this.onPhaseChangeHandlers, cb);
  }

  private off<T>(arr: T[], cb: T): void {
    const i = arr.indexOf(cb);
    if (i >= 0) arr.splice(i, 1);
  }

  /**
   * Start animation from beginning
   */
  start(): void {
    if (this.running) return;
    this.running = true;
    this.startEpoch = performance.now();
    this.pausedAt = 0;
    this.pausedAccum = 0;
    this.lastSegment = null;
    this.loop();
  }

  /**
   * Stop animation and cleanup
   */
  stop(): void {
    this.running = false;
    if (this.rafId !== null) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
  }

  /**
   * Pause animation (maintains state)
   */
  pause(): void {
    if (!this.running || this.pausedAt !== 0) return;
    this.pausedAt = performance.now();
  }

  /**
   * Resume paused animation
   */
  resume(): void {
    if (!this.running || this.pausedAt === 0) return;
    const pauseDuration = performance.now() - this.pausedAt;
    this.pausedAccum += pauseDuration;
    this.pausedAt = 0;
  }

  /**
   * Check if runner is active
   */
  isRunning(): boolean {
    return this.running;
  }

  /**
   * Check if runner is paused
   */
  isPaused(): boolean {
    return this.pausedAt !== 0;
  }

  /**
   * Update timeline during playback (seamless transition)
   */
  updateTimeline(newTimeline: StrategyTimeline): void {
    this.timeline = newTimeline;
  }

  /**
   * Main animation loop (RAF-based)
   */
  private loop(): void {
    this.rafId = requestAnimationFrame(() => this.loop());

    // Skip if paused
    if (this.pausedAt !== 0) return;

    const now = performance.now();
    const base = now - this.startEpoch - this.pausedAccum;
    const totalDurationMs = this.timeline.totalDurationMs;
    const span = totalDurationMs + (this.opts.loop ? this.opts.loopBreakMs ?? 0 : 0);
    const playhead = this.opts.loop ? base % span : Math.min(base, totalDurationMs);

    // Handle loop break (idle period after timeline end)
    if (this.opts.loop && playhead > totalDurationMs) {
      return; // Skip ticks during idle period
    }

    // Auto-stop non-looping timelines
    if (!this.opts.loop && base > totalDurationMs + 200) {
      this.stop();
      return;
    }

    // Find current segment
    const segment = findSegmentAt(this.timeline, playhead);
    if (!segment) return;

    // Detect phase change
    const phaseChanged =
      !this.lastSegment ||
      this.lastSegment.index !== segment.index ||
      (this.opts.loop && this.lastSegment.index > segment.index); // Loop restart

    if (phaseChanged) {
      // Fire phase change handlers
      for (const handler of this.onPhaseChangeHandlers) {
        try {
          handler(this.lastSegment, segment);
        } catch (err) {
          console.error('TimelineRunner: phase change handler error', err);
        }
      }
      this.lastSegment = segment;
    }

    // Calculate progress within segment
    const t = phaseProgress(segment, playhead);

    // Fire tick handlers
    const tick: TimelineTick = {
      nowMs: now,
      playheadMs: playhead,
      segment,
      t,
    };

    for (const handler of this.onTickHandlers) {
      try {
        handler(tick);
      } catch (err) {
        console.error('TimelineRunner: tick handler error', err);
      }
    }
  }
}
