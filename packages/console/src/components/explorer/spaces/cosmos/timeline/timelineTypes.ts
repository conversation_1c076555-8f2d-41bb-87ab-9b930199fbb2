import type { StatefulStrategyJson } from '@toolproof-npm/schema';

/**
 * Animation phase states for job processing
 */
export type TimelinePhase = 'PAUSING' | 'PULLING_IN' | 'INSIDE' | 'PULLING_OUT';

/**
 * Configuration for timeline generation
 */
export interface TimelineConfig {
  /** Duration of job approaching engine (ms) */
  pullInDuration: number;
  /** Duration of job leaving engine (ms) */
  pullOutDuration: number;
  /** Pause before each job starts (ms) */
  pauseBetweenJobs: number;
  /** Duration job stays at engine - processing phase (ms) */
  pauseInside: number;
}

/**
 * Individual segment of the timeline (one phase of one job)
 */
export interface TimelineSegment {
  /** Global segment index across all jobs */
  index: number;
  /** Index of job in job sequence */
  jobIndex: number;
  /** Job identifier (jobRef from execution) */
  jobId: string;
  /** Current animation phase */
  phase: TimelinePhase;
  /** Segment start time in milliseconds */
  startMs: number;
  /** Segment end time in milliseconds */
  endMs: number;
  /** Segment duration in milliseconds */
  durationMs: number;
}

/**
 * Complete timeline structure
 */
export interface StrategyTimeline {
  /** Ordered array of all segments */
  segments: TimelineSegment[];
  /** Total timeline duration in milliseconds */
  totalDurationMs: number;
  /** Array of job IDs in execution order */
  jobSequence: string[];
}

/**
 * Extract job sequence from strategy JSON
 * @param statefulStrategy - Strategy spec containing steps and executions
 * @returns Array of job IDs (jobRef values) in execution order
 */
export function sequenceFromStatefulStrategy(
  statefulStrategy: StatefulStrategyJson | null | undefined
): string[] {
  if (!statefulStrategy?.statelessStrategy?.steps) return [];
  const seq: string[] = [];
  for (const step of statefulStrategy.statelessStrategy.steps) {
    const exec = (step as unknown as { execution?: { jobRef?: string } }).execution;
    if (exec?.jobRef) seq.push(String(exec.jobRef));
  }
  return seq;
}
