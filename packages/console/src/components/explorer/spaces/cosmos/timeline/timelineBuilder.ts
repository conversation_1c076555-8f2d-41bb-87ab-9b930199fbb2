import type { TimelineConfig, TimelineSegment, StrategyTimeline } from './timelineTypes';

/**
 * Build a complete timeline from a job sequence and timing configuration
 * @param jobSequence - Array of job IDs in execution order
 * @param cfg - Timeline configuration (durations for each phase)
 * @returns Complete timeline with segments and total duration
 */
export function buildTimelineFromSequence(
  jobSequence: string[],
  cfg: TimelineConfig
): StrategyTimeline {
  const segments: TimelineSegment[] = [];
  let t = 0;
  let segIndex = 0;

  for (let j = 0; j < jobSequence.length; j++) {
    const jobId = jobSequence[j];

    // Inter-job pause before each job (including the first) for visual consistency
    const pauseDur = cfg.pauseBetweenJobs;
    segments.push({
      index: segIndex++,
      jobIndex: j,
      jobId,
      phase: 'PAUSING',
      startMs: t,
      endMs: t + pauseDur,
      durationMs: pauseDur,
    });
    t += pauseDur;

    // Pulling in
    const pullInDur = cfg.pullInDuration;
    segments.push({
      index: segIndex++,
      jobIndex: j,
      jobId,
      phase: 'PULLING_IN',
      startMs: t,
      endMs: t + pullInDur,
      durationMs: pullInDur,
    });
    t += pullInDur;

    // Inside (processing)
    const insideDur = cfg.pauseInside;
    segments.push({
      index: segIndex++,
      jobIndex: j,
      jobId,
      phase: 'INSIDE',
      startMs: t,
      endMs: t + insideDur,
      durationMs: insideDur,
    });
    t += insideDur;

    // Pulling out
    const pullOutDur = cfg.pullOutDuration;
    segments.push({
      index: segIndex++,
      jobIndex: j,
      jobId,
      phase: 'PULLING_OUT',
      startMs: t,
      endMs: t + pullOutDur,
      durationMs: pullOutDur,
    });
    t += pullOutDur;
  }

  return {
    segments,
    totalDurationMs: t,
    jobSequence: [...jobSequence],
  };
}

/**
 * Find the active segment at a given time
 * @param timeline - Timeline to search
 * @param timeMs - Time in milliseconds
 * @returns Active segment or null if none found
 */
export function findSegmentAt(
  timeline: StrategyTimeline,
  timeMs: number
): TimelineSegment | null {
  if (timeline.segments.length === 0) return null;
  // Linear scan is fine for short sequences; could use binary search for optimization
  for (const seg of timeline.segments) {
    if (timeMs >= seg.startMs && timeMs < seg.endMs) return seg;
  }
  // If exactly at the end, return last segment
  return timeline.segments[timeline.segments.length - 1] || null;
}

/**
 * Calculate 0-1 progress within a segment
 * @param seg - Segment to calculate progress for
 * @param timeMs - Current time in milliseconds
 * @returns Progress value clamped between 0 and 1
 */
export function phaseProgress(seg: TimelineSegment, timeMs: number): number {
  if (seg.durationMs <= 0) return 1;
  return Math.max(0, Math.min(1, (timeMs - seg.startMs) / seg.durationMs));
}
