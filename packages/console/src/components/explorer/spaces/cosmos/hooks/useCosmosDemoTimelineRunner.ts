import type {
  BranchStepIdentityJson,
  ForStepI<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ity<PERSON><PERSON>,
  Step<PERSON><PERSON>,
  WhileStepIdentityJson,
  WorkStepIdentityJson,
} from '@toolproof-npm/schema';
import { PANEL_ANIMATION } from './animationConstants';
import type React from 'react';
import { useCallback, useEffect, useRef } from 'react';
import type * as THREE from 'three';
import { TimelineRunner } from '../timeline/timelineRunner';

type TimelinePhase = 'PAUSING' | 'PULLING_IN' | 'INSIDE' | 'PULLING_OUT';

type StepIdentityJson =
  | WorkStepIdentityJson
  | BranchStepIdentityJson
  | WhileStepIdentityJson
  | ForStepIdentityJson;

export type UseCosmosDemoTimelineRunnerArgs = {
  strategySteps: StepJson[];

  workStepRefs: React.MutableRefObject<Record<string, React.RefObject<THREE.Group | null>>>;
  applyTimelineAnimationRef: React.MutableRefObject<
    ((
      stepGroup: THREE.Group,
      stepIdentity: StepIdentityJson,
      phase: TimelinePhase,
      t: number
    ) => void) | null
  >;

  timelineRunnerRef: React.MutableRefObject<TimelineRunner | null>;

  cancelJobActivation: () => void;
  setActivatedJobId: (id: JobIdentityJson | null) => void;
  setSlidingJobId: (id: JobIdentityJson | null) => void;
  setHoveredJobId: (id: JobIdentityJson | null) => void;

  setIsAnimating: (isAnimating: boolean) => void;

  activeAnimatingJobIdRef: React.MutableRefObject<JobIdentityJson | null>;
  setActiveAnimatingJobId: (id: JobIdentityJson | null) => void;
};

/**
 * Encapsulates the "demo" timeline runner used by CosmosSpace runner mode.
 *
 * It builds timeline segments directly from strategy steps and drives
 * CosmosSpace animations through a ref-based callback.
 */
export function useCosmosDemoTimelineRunner({
  strategySteps,
  workStepRefs,
  applyTimelineAnimationRef,
  timelineRunnerRef,
  cancelJobActivation,
  setActivatedJobId,
  setSlidingJobId,
  setHoveredJobId,
  setIsAnimating,
  activeAnimatingJobIdRef,
  setActiveAnimatingJobId,
}: UseCosmosDemoTimelineRunnerArgs): {
  runDemoTimeline: () => void;
} {
  const completionTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const stopRunnerOnly = useCallback(() => {
    if (completionTimeoutRef.current) {
      clearTimeout(completionTimeoutRef.current);
      completionTimeoutRef.current = null;
    }

    if (timelineRunnerRef.current) {
      timelineRunnerRef.current.stop();
      timelineRunnerRef.current = null;
    }
  }, [timelineRunnerRef]);

  const stopAndReset = useCallback(() => {
    stopRunnerOnly();

    // Cancel any click-driven step/job animation + clear selection state.
    cancelJobActivation();
    setActivatedJobId(null);
    setSlidingJobId(null);
    setHoveredJobId(null);

    // Reset animation state
    setIsAnimating(false);
    activeAnimatingJobIdRef.current = null;
    setActiveAnimatingJobId(null);
  }, [
    activeAnimatingJobIdRef,
    cancelJobActivation,
    setActivatedJobId,
    setActiveAnimatingJobId,
    setHoveredJobId,
    setIsAnimating,
    setSlidingJobId,
    stopRunnerOnly,
  ]);

  const runDemoTimeline = useCallback(() => {
    try {
      // Stage 4 (runner) playback: slightly slower pacing for readability.
      // (>1 means slower; applied to segment durations only.)
      const runnerDurationScale = 1.35;

      stopAndReset();

      // Build timeline from strategy steps directly
      if (!strategySteps || strategySteps.length === 0) {
        console.warn('No strategy steps available for timeline animation');
        return;
      }

      // Build timeline segments for each step
      const segments: Array<{
        index: number;
        stepIndex: number;
        stepId: string;
        phase: TimelinePhase;
        startMs: number;
        endMs: number;
        durationMs: number;
      }> = [];

      let currentTime = 0;
      let segmentIndex = 0;

      for (let i = 0; i < strategySteps.length; i++) {
        const step = strategySteps[i];
        const stepId = String(step.identity);

        // PAUSING phase
        const pauseDurationMs = PANEL_ANIMATION.PHASE_1_DURATION * 1000 * runnerDurationScale;
        segments.push({
          index: segmentIndex++,
          stepIndex: i,
          stepId,
          phase: 'PAUSING',
          startMs: currentTime,
          endMs: currentTime + pauseDurationMs,
          durationMs: pauseDurationMs,
        });
        currentTime += pauseDurationMs;

        // PULLING_IN phase
        const pullInDurationMs = PANEL_ANIMATION.PHASE_2_DURATION * 1000 * runnerDurationScale;
        segments.push({
          index: segmentIndex++,
          stepIndex: i,
          stepId,
          phase: 'PULLING_IN',
          startMs: currentTime,
          endMs: currentTime + pullInDurationMs,
          durationMs: pullInDurationMs,
        });
        currentTime += pullInDurationMs;

        // INSIDE phase
        const insideDurationMs = PANEL_ANIMATION.PHASE_1_DURATION * 1000 * runnerDurationScale;
        segments.push({
          index: segmentIndex++,
          stepIndex: i,
          stepId,
          phase: 'INSIDE',
          startMs: currentTime,
          endMs: currentTime + insideDurationMs,
          durationMs: insideDurationMs,
        });
        currentTime += insideDurationMs;

        // PULLING_OUT phase
        const pullOutDurationMs = PANEL_ANIMATION.PHASE_2_DURATION * 1000 * runnerDurationScale;
        segments.push({
          index: segmentIndex++,
          stepIndex: i,
          stepId,
          phase: 'PULLING_OUT',
          startMs: currentTime,
          endMs: currentTime + pullOutDurationMs,
          durationMs: pullOutDurationMs,
        });
        currentTime += pullOutDurationMs;
      }

      const timeline = {
        segments,
        totalDurationMs: currentTime,
      };

      const runner = new TimelineRunner(timeline as never, {
        loop: false,
        loopBreakMs: 3000,
      });

      runner.onTick((tick) => {
        const seg = tick.segment as unknown as (typeof segments)[number];
        const stepIdentity = seg.stepId as StepIdentityJson;

        // Update active step once per segment (CosmosSpace historically stores this in the job id ref)
        if (activeAnimatingJobIdRef.current !== (stepIdentity as unknown as JobIdentityJson)) {
          activeAnimatingJobIdRef.current = stepIdentity as unknown as JobIdentityJson;
          setActiveAnimatingJobId(stepIdentity as unknown as JobIdentityJson);
        }

        const stepRef = workStepRefs.current[stepIdentity];
        if (!stepRef?.current) {
          console.warn(
            'No mesh ref for step identity:',
            stepIdentity,
            'Available refs:',
            Object.keys(workStepRefs.current)
          );
          return;
        }

        applyTimelineAnimationRef.current?.(stepRef.current, stepIdentity, seg.phase, tick.t);
      });

      runner.start();
      timelineRunnerRef.current = runner;
      setIsAnimating(true);

      completionTimeoutRef.current = setTimeout(() => {
        stopAndReset();
      }, timeline.totalDurationMs);
    } catch (error) {
      console.error('Error loading hardcoded strategy:', error);
    }
  }, [
    activeAnimatingJobIdRef,
    applyTimelineAnimationRef,
    setActiveAnimatingJobId,
    setIsAnimating,
    stopAndReset,
    strategySteps,
    timelineRunnerRef,
    workStepRefs,
  ]);

  // Cleanup timeline runner + timeout on unmount
  useEffect(() => {
    return () => {
      stopRunnerOnly();
    };
  }, [stopRunnerOnly]);

  return { runDemoTimeline };
}
