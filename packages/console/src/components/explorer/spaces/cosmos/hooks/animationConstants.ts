/**
 * Shared animation constants for panel animations (jobs and steps)
 */

export const PANEL_ANIMATION = {
  /** Y offset when panel goes "down" during animation */
  DOWN_Y: -1.4,
  
  /** Duration fractions for 3-phase motion (relative to speed multiplier) */
  PHASE_1_DURATION: 0.18, // down
  PHASE_2_DURATION: 0.45, // translate
  PHASE_3_DURATION: 0.18, // up
  
  /** Easing curves for each phase */
  PHASE_1_EASE: 'power2.in' as const,
  PHASE_2_EASE: 'power2.inOut' as const,
  PHASE_3_EASE: 'power2.out' as const,
} as const;

/**
 * Calculate phase progress fractions for timeline animation
 */
export function getPhaseProgressFractions() {
  const total = PANEL_ANIMATION.PHASE_1_DURATION + 
                PANEL_ANIMATION.PHASE_2_DURATION + 
                PANEL_ANIMATION.PHASE_3_DURATION;
  
  return {
    f1: PANEL_ANIMATION.PHASE_1_DURATION / total,
    f2: PANEL_ANIMATION.PHASE_2_DURATION / total,
    f3: PANEL_ANIMATION.PHASE_3_DURATION / total,
    downY: PANEL_ANIMATION.DOWN_Y,
  };
}
