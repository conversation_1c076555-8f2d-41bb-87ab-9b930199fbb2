import type {
  BranchStepIdentity<PERSON><PERSON>,
  ForStep<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  WhileStepIdentityJ<PERSON>,
  WorkStepIdentityJson,
} from '@toolproof-npm/schema';
import { useEffect, useRef } from 'react';
import type { TimelineConfig } from '../timeline/timelineTypes';
import { StrategyEventNormalizer } from '../eventNormalizer';
import { TimelineRunner } from '../timeline/timelineRunner';
import type React from 'react';
import type * as THREE from 'three';

type StepIdentityJson =
  | WorkStepIdentityJson
  | BranchStepIdentityJson
  | WhileStepIdentityJson
  | ForStepIdentityJson;

export type UseCosmosLiveTimelineArgs = {
  mode: 'builder' | 'runner';
  strategySteps: StepJson[];

  enableLiveTimelineEvents: boolean;
  enableRecordedTimeline: boolean;
  autoLoadRecordedTimeline: boolean;
  recordedFilePath: string;

  timelineConfig: TimelineConfig;

  eventNormalizerRef: React.MutableRefObject<StrategyEventNormalizer | null>;
  timelineRunnerRef: React.MutableRefObject<TimelineRunner | null>;

  // Live events produce a "stateful strategy" used elsewhere for resource creation.
  statefulStrategyRef: React.MutableRefObject<unknown>;

  // Legacy job-mesh animation path
  jobRefs: React.MutableRefObject<Record<string, React.RefObject<THREE.Group | null>>>;
  applyTimelineAnimationRef: React.MutableRefObject<
    ((
      group: THREE.Group,
      identity: StepIdentityJson,
      phase: 'PAUSING' | 'PULLING_IN' | 'INSIDE' | 'PULLING_OUT',
      t: number
    ) => void) | null
  >;

  activeAnimatingJobIdRef: React.MutableRefObject<JobIdentityJson | null>;
  setActiveAnimatingJobId: (id: JobIdentityJson | null) => void;
  setIsAnimating: (isAnimating: boolean) => void;

  // Reset selection state on completion
  setActivatedJobId: (id: JobIdentityJson | null) => void;
  setSlidingJobId: (id: JobIdentityJson | null) => void;
  setHoveredJobId: (id: JobIdentityJson | null) => void;
};

/**
 * Encapsulates CosmosSpace's live StrategyRun timeline pipeline:
 * - StrategyEventNormalizer
 * - window 'toolproof:graphEvent' listener
 * - optional recorded timeline loader (feature-flagged)
 * - TimelineRunner lifecycle for the live (job-based) animation path
 */
export function useCosmosLiveTimeline({
  mode,
  strategySteps,
  enableLiveTimelineEvents,
  enableRecordedTimeline,
  autoLoadRecordedTimeline,
  recordedFilePath,
  timelineConfig,
  eventNormalizerRef,
  timelineRunnerRef,
  statefulStrategyRef,
  jobRefs,
  applyTimelineAnimationRef,
  activeAnimatingJobIdRef,
  setActiveAnimatingJobId,
  setIsAnimating,
  setActivatedJobId,
  setSlidingJobId,
  setHoveredJobId,
}: UseCosmosLiveTimelineArgs) {
  const mountedRef = useRef(true);
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Initialize the event normalizer + hook it up to a TimelineRunner (live/job path)
  useEffect(() => {
    // Skip event normalizer in runner mode with steps (demo runner handles this path)
    if (mode === 'runner' && strategySteps && strategySteps.length > 0) {
      return;
    }

    if (!eventNormalizerRef.current) {
      const normalizer = new StrategyEventNormalizer(timelineConfig);

      normalizer.onTimelineUpdate((timeline, statefulStrategy) => {
        // Store statefulStrategy reference for resource creation
        statefulStrategyRef.current = statefulStrategy;

        if (!timelineRunnerRef.current) {
          const runner = new TimelineRunner(timeline, {
            loop: false,
            loopBreakMs: 3000,
          });

          runner.onTick(({ segment, t }) => {
            const jobIdentity = segment.jobId as JobIdentityJson;

            if (activeAnimatingJobIdRef.current !== jobIdentity) {
              activeAnimatingJobIdRef.current = jobIdentity;
              if (mountedRef.current) setActiveAnimatingJobId(jobIdentity);
            }

            const jobRef = jobRefs.current[jobIdentity];
            if (!jobRef?.current) {
              console.warn(
                'No mesh ref for job identity:',
                jobIdentity,
                'Available refs:',
                Object.keys(jobRefs.current)
              );
              return;
            }

            // Legacy job animation path
            // @ts-expect-error - Legacy job animation path uses job identities.
            applyTimelineAnimationRef.current?.(jobRef.current, jobIdentity, segment.phase, t);
          });

          runner.start();
          timelineRunnerRef.current = runner;
          if (mountedRef.current) setIsAnimating(true);
        } else {
          timelineRunnerRef.current.updateTimeline(timeline);
        }
      });

      normalizer.onComplete(() => {
        if (mountedRef.current) {
          setIsAnimating(false);
          setActiveAnimatingJobId(null);
          setActivatedJobId(null);
          setSlidingJobId(null);
          setHoveredJobId(null);
        }
        activeAnimatingJobIdRef.current = null;
      });

      eventNormalizerRef.current = normalizer;
    }
  }, [
    activeAnimatingJobIdRef,
    applyTimelineAnimationRef,
    eventNormalizerRef,
    jobRefs,
    mode,
    setActivatedJobId,
    setActiveAnimatingJobId,
    setHoveredJobId,
    setIsAnimating,
    setSlidingJobId,
    statefulStrategyRef,
    strategySteps,
    timelineConfig,
    timelineRunnerRef,
  ]);

  // Listen for live events from StrategyBuilder
  useEffect(() => {
    if (!enableLiveTimelineEvents) return;

    const handleGraphEvent = (event: Event) => {
      const customEvent = event as CustomEvent;
      const rawEvent = customEvent.detail;

      if (!eventNormalizerRef.current) return;
      eventNormalizerRef.current.processLiveEvent(rawEvent);
    };

    window.addEventListener('toolproof:graphEvent', handleGraphEvent);
    return () => {
      window.removeEventListener('toolproof:graphEvent', handleGraphEvent);
    };
  }, [enableLiveTimelineEvents, eventNormalizerRef]);

  // Load recorded strategy file (feature-flagged)
  useEffect(() => {
    if (!enableRecordedTimeline || !autoLoadRecordedTimeline) return;

    const loadRecordedStrategy = async () => {
      try {
        const response = await fetch(recordedFilePath);
        if (!response.ok) {
          console.warn('Could not load recorded strategy file:', recordedFilePath);
          return;
        }

        const events = await response.json();
        if (!Array.isArray(events) || events.length === 0) {
          console.warn('Recorded strategy file is empty or invalid');
          return;
        }

        if (eventNormalizerRef.current) {
          for (const event of events) {
            eventNormalizerRef.current.processLiveEvent(event);
          }
        }
      } catch (error) {
        console.error('Error loading recorded strategy:', error);
      }
    };

    loadRecordedStrategy();
  }, [autoLoadRecordedTimeline, enableRecordedTimeline, recordedFilePath, eventNormalizerRef]);

  // Best-effort cleanup of the TimelineRunner created by this path.
  useEffect(() => {
    return () => {
      if (timelineRunnerRef.current) {
        timelineRunnerRef.current.stop();
        timelineRunnerRef.current = null;
      }
    };
  }, [timelineRunnerRef]);
}
