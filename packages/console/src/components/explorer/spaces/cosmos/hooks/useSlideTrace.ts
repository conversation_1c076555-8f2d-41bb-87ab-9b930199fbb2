import { useCallback, useEffect, useRef, useState } from 'react';
import type * as THREE from 'three';

type Vec3Tuple = [number, number, number];

/**
 * Reusable hook for managing slide trace animation trails
 * Used by both job panels and step panels
 */
export function useSlideTrace<TId extends string>() {
  const [slideTrace, setSlideTrace] = useState<Array<Vec3Tuple>>([]);
  const slideTraceRef = useRef<{ 
    id: TId | null; 
    samples: Vec3Tuple[]; 
    lastSampleMs: number 
  }>({
    id: null,
    samples: [],
    lastSampleMs: 0,
  });
  const [activeAnimatingId, setActiveAnimatingId] = useState<TId | null>(null);

  const nowMs = () => (typeof performance !== 'undefined' ? performance.now() : Date.now());

  const resetSlideTrace = useCallback((id: TId) => {
    slideTraceRef.current = { id, samples: [[0, 0, 0]], lastSampleMs: 0 };
    setSlideTrace([[0, 0, 0]]);
  }, []);

  const pushSlideTrace = useCallback((id: TId, p: THREE.Vector3) => {
    const ref = slideTraceRef.current;
    if (ref.id !== id) return;

    const t = nowMs();
    // Throttle updates to avoid re-rendering at full frame rate.
    if (ref.lastSampleMs !== 0 && t - ref.lastSampleMs < 28) return;
    ref.lastSampleMs = t;

    ref.samples.push([p.x, p.y, p.z]);
    const max = 14;
    if (ref.samples.length > max) ref.samples.splice(0, ref.samples.length - max);
    setSlideTrace([...ref.samples]);
  }, []);

  useEffect(() => {
    // Clear the trace when animation ends.
    if (!activeAnimatingId) {
      slideTraceRef.current = { id: null, samples: [], lastSampleMs: 0 };
      setSlideTrace([]);
    }
  }, [activeAnimatingId]);

  return {
    slideTrace,
    activeAnimatingId,
    setActiveAnimatingId,
    resetSlideTrace,
    pushSlideTrace,
  };
}
