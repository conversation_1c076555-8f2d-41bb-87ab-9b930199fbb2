import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import type * as THREE from 'three';
import type { JobIdentityJson } from '@toolproof-npm/schema';
import { useSlideTrace } from './useSlideTrace';
import { PANEL_ANIMATION } from './animationConstants';

type Vec3Tuple = [number, number, number];

export function useJobPanelAnimation<TStepId = never>(opts: {
  activatedJobId: JobIdentityJson | null;
  setActivatedJobId: (id: JobIdentityJson | null) => void;
  slidingJobId: JobIdentityJson | null;
  setSlidingJobId: (id: JobIdentityJson | null) => void;
  setHoveredJobId: (id: JobIdentityJson | null) => void;
  setCenterSelection: (
    selection:
      | { kind: 'job'; id: JobIdentityJson }
      | { kind: 'step'; id: TStepId }
      | null
  ) => void;
  jobRefs: React.MutableRefObject<Record<JobIdentityJson, React.RefObject<THREE.Group | null>>>;
  jobPanelPosByJobId: Map<JobIdentityJson, Vec3Tuple>;
  toLocalOffsetForCenter: (homeWorld: Vec3Tuple) => Vec3Tuple;
}) {
  const {
    activatedJobId,
    setActivatedJobId,
    slidingJobId,
    setSlidingJobId,
    setHoveredJobId,
    setCenterSelection,
    jobRefs,
    jobPanelPosByJobId,
    toLocalOffsetForCenter,
  } = opts;

  const jobActivationTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const jobActivationInFlightRef = useRef(false);

  // Use shared slide trace hook
  const {
    slideTrace: jobSlideTrace,
    activeAnimatingId: activeJobTraceId,
    setActiveAnimatingId: setActiveJobTraceId,
    resetSlideTrace: resetJobSlideTrace,
    pushSlideTrace: pushJobSlideTrace,
  } = useSlideTrace<JobIdentityJson>();

  const slidingJobIdRef = useRef<JobIdentityJson | null>(null);

  useEffect(() => {
    slidingJobIdRef.current = slidingJobId;
    // Sync trace animation state with sliding state
    setActiveJobTraceId(slidingJobId);
  }, [slidingJobId, setActiveJobTraceId]);

  const getHomePosition = (id: JobIdentityJson): Vec3Tuple | null => {
    return jobPanelPosByJobId.get(id) ?? null;
  };

  const animateJobTo = (
    id: JobIdentityJson,
    targetLocal: Vec3Tuple,
    opts?: { speedMultiplier?: number }
  ): Promise<void> => {
    const group = jobRefs.current[id]?.current;
    if (!group) return Promise.resolve();

    // Cancel any prior activation animation.
    jobActivationTimelineRef.current?.kill();
    jobActivationTimelineRef.current = null;

    const speedMultiplier = Math.max(0.1, opts?.speedMultiplier ?? 1);
    const d1 = PANEL_ANIMATION.PHASE_1_DURATION / speedMultiplier;
    const d2 = PANEL_ANIMATION.PHASE_2_DURATION / speedMultiplier;
    const d3 = PANEL_ANIMATION.PHASE_3_DURATION / speedMultiplier;
    const downY = PANEL_ANIMATION.DOWN_Y;

    return new Promise((resolve) => {
      const tl = gsap.timeline({
        onUpdate: () => {
          if (slidingJobIdRef.current !== id) return;
          pushJobSlideTrace(id, group.position);
        },
        onComplete: () => resolve(),
      });

      jobActivationTimelineRef.current = tl;

      tl.to(group.position, { y: downY, duration: d1, ease: PANEL_ANIMATION.PHASE_1_EASE })
        .to(group.position, {
          x: targetLocal[0],
          z: targetLocal[2],
          duration: d2,
          ease: PANEL_ANIMATION.PHASE_2_EASE,
        })
        .to(group.position, { y: targetLocal[1], duration: d3, ease: PANEL_ANIMATION.PHASE_3_EASE });
    });
  };

  const animateJobHome = async (id: JobIdentityJson, opts?: { speedMultiplier?: number }) => {
    const home = getHomePosition(id);
    if (!home) return;
    await animateJobTo(id, [0, 0, 0], opts);
  };

  const animateJobToCenter = async (id: JobIdentityJson, opts?: { speedMultiplier?: number }) => {
    const home = getHomePosition(id);
    if (!home) return;

    resetJobSlideTrace(id);
    setSlidingJobId(id);
    await animateJobTo(id, toLocalOffsetForCenter(home), opts);
    setHoveredJobId(null);
    setSlidingJobId(null);
  };

  const handleJobSelect = (jobId: JobIdentityJson | null) => {
    if (jobActivationInFlightRef.current) return;

    const run = async () => {
      jobActivationInFlightRef.current = true;
      try {
        // Clicking background or the active job deactivates it.
        if (!jobId || activatedJobId === jobId) {
          if (activatedJobId) {
            // Immediately hide roles/connectors while returning home.
            setActivatedJobId(null);
            setCenterSelection(null);
            const home = getHomePosition(activatedJobId);
            if (home) await animateJobTo(activatedJobId, [0, 0, 0], { speedMultiplier: 2 });
          }
          setSlidingJobId(null);
          return;
        }

        // If another job is active, animate it back home first.
        if (activatedJobId) {
          // Immediately hide roles/connectors while returning home.
          const returningId = activatedJobId;
          setActivatedJobId(null);
          setCenterSelection(null);
          const home = getHomePosition(activatedJobId);
          if (home) await animateJobTo(returningId, [0, 0, 0], { speedMultiplier: 2 });
        }

        // Slide clicked job to center (down-in-up), then activate (show roles/connectors).
        const home = getHomePosition(jobId);
        if (!home) {
          setActivatedJobId(null);
          setSlidingJobId(null);
          return;
        }

        resetJobSlideTrace(jobId);
        setSlidingJobId(jobId);
        await animateJobTo(jobId, toLocalOffsetForCenter(home));
        setHoveredJobId(null);
        setActivatedJobId(jobId);
        setCenterSelection({ kind: 'job', id: jobId });
        setSlidingJobId(null);
      } finally {
        jobActivationInFlightRef.current = false;
      }
    };

    void run();
  };

  const cancelJobActivation = () => {
    jobActivationTimelineRef.current?.kill();
    jobActivationTimelineRef.current = null;
    jobActivationInFlightRef.current = false;
  };

  return {
    handleJobSelect,
    cancelJobActivation,
    slidingJobIdRef,
    jobSlideTrace,
    resetJobSlideTrace,
    pushJobSlideTrace,
    animateJobHome,
    animateJobToCenter,
  };
}
