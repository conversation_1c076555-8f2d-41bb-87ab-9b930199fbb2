import { Plane } from '@react-three/drei';
import React from 'react';

interface SlideTraceProps {
  trace: Array<[number, number, number]>;
  name: string;
  homePosition?: [number, number, number];
  yawRotation?: number;
}

/**
 * Renders a yellow trace trail for sliding animations.
 * Used by both job and step animations.
 */
export const SlideTrace: React.FC<SlideTraceProps> = ({ 
  trace, 
  name, 
  homePosition,
  yawRotation 
}) => {
  if (trace.length <= 1) return null;

  return (
    <group name={name}>
      {trace.map((p, idx) => {
        const len = trace.length;
        const alpha = ((idx + 1) / len) * 0.28;

        let posX = p[0];
        const posY = p[1] + 0.02;
        let posZ = p[2];

        // If homePosition and yawRotation provided, apply rotation transformation
        if (homePosition && yawRotation !== undefined) {
          const yaw = -yawRotation; // Negate to match parent rotation
          const cosYaw = Math.cos(yaw);
          const sinYaw = Math.sin(yaw);
          
          // Rotate local position by yaw angle
          const rotatedX = p[0] * cosYaw - p[2] * sinYaw;
          const rotatedZ = p[0] * sinYaw + p[2] * cosYaw;
          
          // Add rotated local position to world home position
          posX = homePosition[0] + rotatedX;
          posZ = homePosition[2] + rotatedZ;
        }

        return (
          <Plane
            key={`trace-${idx}`}
            args={[1.4, 0.9]}
            position={[posX, posY, posZ]}
            rotation={[-Math.PI / 2, 0, 0]}
          >
            <meshBasicMaterial
              color='#FFFF00'
              transparent
              opacity={alpha}
              depthWrite={false}
              toneMapped={false}
            />
          </Plane>
        );
      })}
    </group>
  );
};
