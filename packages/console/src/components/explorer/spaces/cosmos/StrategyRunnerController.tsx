'use client';

import React, { useCallback, useEffect, useRef } from 'react';
import type { Job<PERSON><PERSON>ity<PERSON><PERSON>, Step<PERSON><PERSON> } from '@toolproof-npm/schema';
import { PANEL_ANIMATION } from './hooks/animationConstants';
import { TimelineRunner } from './timeline/timelineRunner';
import CosmosSpace, {
  type CosmosSpaceBridge,
  type CosmosSpaceHandle,
  type TimelinePhase,
} from './CosmosSpace';

export type StrategyRunnerControllerHandle = {
  runDemoTimeline: () => void;
};

type TimelineSegment = {
  index: number;
  stepIndex: number;
  stepId: string;
  phase: TimelinePhase;
  startMs: number;
  endMs: number;
  durationMs: number;
};

export default React.forwardRef<StrategyRunnerControllerHandle, {
  // Demo mode: provide a bridge and call runDemoTimeline().
  // Live mode: omit bridge and set live=true to consume toolproof:graphEvent updates.
  bridge?: CosmosSpaceBridge;
  live?: boolean;
  onTimelineStateChange?: (isAnimating: boolean) => void;
}>(function StrategyRunnerController(props, ref) {
  const { bridge, live = false, onTimelineStateChange } = props;
  const cosmosRef = useRef<CosmosSpaceHandle | null>(null);

  const runnerRef = useRef<TimelineRunner | null>(null);
  const completionTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const stopRunnerOnly = useCallback(() => {
    if (completionTimeoutRef.current) {
      clearTimeout(completionTimeoutRef.current);
      completionTimeoutRef.current = null;
    }

    if (runnerRef.current) {
      runnerRef.current.stop();
      runnerRef.current = null;
    }
  }, []);

  const stopAndReset = useCallback(() => {
    stopRunnerOnly();
    cosmosRef.current?.resetRunnerTimeline();
  }, [stopRunnerOnly]);

  const buildDemoSegments = useCallback((steps: StepJson[]): { segments: TimelineSegment[]; totalDurationMs: number } => {
    const runnerDurationScale = 1.35;

    const segments: TimelineSegment[] = [];
    let currentTime = 0;
    let segmentIndex = 0;

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      const stepId = String(step.identity);

      const pauseDurationMs = PANEL_ANIMATION.PHASE_1_DURATION * 1000 * runnerDurationScale;
      segments.push({
        index: segmentIndex++,
        stepIndex: i,
        stepId,
        phase: 'PAUSING',
        startMs: currentTime,
        endMs: currentTime + pauseDurationMs,
        durationMs: pauseDurationMs,
      });
      currentTime += pauseDurationMs;

      const pullInDurationMs = PANEL_ANIMATION.PHASE_2_DURATION * 1000 * runnerDurationScale;
      segments.push({
        index: segmentIndex++,
        stepIndex: i,
        stepId,
        phase: 'PULLING_IN',
        startMs: currentTime,
        endMs: currentTime + pullInDurationMs,
        durationMs: pullInDurationMs,
      });
      currentTime += pullInDurationMs;

      const insideDurationMs = PANEL_ANIMATION.PHASE_1_DURATION * 1000 * runnerDurationScale;
      segments.push({
        index: segmentIndex++,
        stepIndex: i,
        stepId,
        phase: 'INSIDE',
        startMs: currentTime,
        endMs: currentTime + insideDurationMs,
        durationMs: insideDurationMs,
      });
      currentTime += insideDurationMs;

      const pullOutDurationMs = PANEL_ANIMATION.PHASE_2_DURATION * 1000 * runnerDurationScale;
      segments.push({
        index: segmentIndex++,
        stepIndex: i,
        stepId,
        phase: 'PULLING_OUT',
        startMs: currentTime,
        endMs: currentTime + pullOutDurationMs,
        durationMs: pullOutDurationMs,
      });
      currentTime += pullOutDurationMs;
    }

    return { segments, totalDurationMs: currentTime };
  }, []);

  const runDemoTimeline = useCallback(() => {
    if (live) {
      throw new Error('StrategyRunnerController: runDemoTimeline() called in live mode');
    }

    if (!bridge) {
      throw new Error('StrategyRunnerController: demo mode requires bridge');
    }

    const strategySteps = bridge.strategySteps ?? [];

    stopAndReset();

    if (!strategySteps || strategySteps.length === 0) {
      console.warn('No strategy steps available for demo timeline animation');
      return;
    }

    const { segments, totalDurationMs } = buildDemoSegments(strategySteps);

    cosmosRef.current?.setTimelineAnimating(true);

    const timeline = { segments, totalDurationMs };
    const runner = new TimelineRunner(timeline as never, { loop: false, loopBreakMs: 3000 });

    const lastStepIdRef = { current: null as string | null };

    runner.onTick((tick) => {
      const seg = tick.segment as unknown as TimelineSegment;

      if (lastStepIdRef.current !== seg.stepId) {
        lastStepIdRef.current = seg.stepId;
        cosmosRef.current?.setActiveAnimatingJobId(seg.stepId as unknown as JobIdentityJson);
      }

      cosmosRef.current?.applyDemoTimelineTick(seg.stepId, seg.phase, tick.t);
    });

    runner.start();
    runnerRef.current = runner;

    completionTimeoutRef.current = setTimeout(() => {
      cosmosRef.current?.setTimelineAnimating(false);
      cosmosRef.current?.setActiveAnimatingJobId(null);
      cosmosRef.current?.resetRunnerTimeline();
      stopRunnerOnly();
    }, totalDurationMs);
  }, [bridge, buildDemoSegments, live, stopAndReset, stopRunnerOnly]);

  React.useImperativeHandle(ref, () => ({ runDemoTimeline }), [runDemoTimeline]);

  useEffect(() => {
    return () => {
      stopAndReset();
    };
  }, [stopAndReset]);

  if (!live && !bridge) {
    throw new Error('StrategyRunnerController: either set live=true or provide bridge');
  }

  return (
    <CosmosSpace
      ref={cosmosRef}
      mode="runner"
      bridge={bridge}
      onTimelineStateChange={onTimelineStateChange}
      timelineMode={live ? 'live' : 'demo'}
    />
  );
});
