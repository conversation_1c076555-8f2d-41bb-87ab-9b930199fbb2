'use client';

import { useStrategyContext } from '@/builders/strategy/contexts/StrategyContext';
import { useStrategyActionsContext } from '@/builders/strategy/contexts/StrategyActionsContext';
import { useSelectionContext } from '@/builders/strategy/contexts/SelectionContext';
import type { CenterSelection } from '@/builders/strategy/contexts/CenterSelectionContext';
import CosmosSpace, { type CosmosSpaceBuilderBridge } from './CosmosSpace';
import { useMemo, useState } from 'react';

export default function StrategyBuilderControllerR3F() {
  const strategyContext = useStrategyContext();
  const strategyActions = useStrategyActionsContext();
  const selectionContext = useSelectionContext();
  const [centerSelection, setCenterSelection] = useState<CenterSelection>(null);

  const builder: CosmosSpaceBuilderBridge = useMemo(
    () => ({
      // Strategy content & state
      strategySteps: strategyContext.statefulStrategy?.statelessStrategy?.steps ?? [],
      strategyState: strategyContext.strategyState,

      // Build step actions
      onBuildWorkStep: strategyActions.onBuildWorkStep,
      onBuildBranchStep: strategyActions.onBuildBranchStep,
      onBuildWhileStep: strategyActions.onBuildWhileStep,
      onBuildForStep: strategyActions.onBuildForStep,

      // Selection actions
      onSelectStep: strategyActions.onSelectStep,
      onClearSelection: strategyActions.onClearSelection,

      // Selection state
      activeStep: selectionContext.activeStep,

      // Center selection state
      centerSelection: centerSelection,
      setCenterSelection: setCenterSelection,
    }),
    [strategyContext, strategyActions, selectionContext, centerSelection]
  );

  return <CosmosSpace mode="builder" builder={builder} />;
}
