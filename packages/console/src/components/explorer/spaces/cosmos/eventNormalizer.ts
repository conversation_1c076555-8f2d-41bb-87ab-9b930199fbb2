import type {
  StatefulStrategy<PERSON><PERSON>,
  Strategy<PERSON><PERSON><PERSON><PERSON>,
  StrategyRunUpdate<PERSON>son,
  StrategyStateJson,
} from '@toolproof-npm/schema';
import { buildTimelineFromSequence } from './timeline/timelineBuilder';
import { sequenceFromStatefulStrategy } from './timeline/timelineTypes';
import type { StrategyTimeline, TimelineConfig } from './timeline/timelineTypes';
import { getRunGraphEventKind } from '@/_lib/types/runGraphEvents';

/**
 * Converts both live strategy events and recorded strategy data into normalized timeline format.
 * This provides a single abstraction layer so the rest of the system doesn't need to distinguish
 * between live execution and playback.
 */
export class StrategyEventNormalizer {
  private statefulStrategy: StatefulStrategyJson | null = null;
  private onTimelineUpdateCallback?: (
    timeline: StrategyTimeline,
    spec: StatefulStrategyJson
  ) => void;
  private onCompleteCallback?: (
    timeline: StrategyTimeline,
    spec: StatefulStrategyJson
  ) => void;

  constructor(private timelineConfig: TimelineConfig) {}

  /**
   * Subscribe to incremental timeline updates (triggered on each event)
   * Used for real-time visualization of live strategies
   */
  public onTimelineUpdate(
    callback: (timeline: StrategyTimeline, spec: StatefulStrategyJson) => void
  ): void {
    this.onTimelineUpdateCallback = callback;
  }

  /**
   * Subscribe to strategy completion (triggered on graph_end event)
   * Used for saving final timeline for replay
   */
  public onComplete(
    callback: (timeline: StrategyTimeline, spec: StatefulStrategyJson) => void
  ): void {
    this.onCompleteCallback = callback;
  }

  /**
   * Process a live graph event from StrategyBuilder
   * Normalizes the event and updates the strategy spec incrementally
   */
  public processLiveEvent(raw: unknown): void {
    const kind = getRunGraphEventKind(raw);
    if (!kind) return;

    const label = kind;
    const eventObj = (raw && typeof raw === 'object') ? (raw as Record<string, unknown>) : {};

    // strategy_run: server emits { kind: 'strategy_run', strategyRun }
    if (kind === 'strategy_run' && eventObj['strategyRun']) {
      const strategyRun = eventObj['strategyRun'] as StrategyRunJson;
      
      // Build a StatefulStrategyJson from the strategyRun data
      // The strategyRun contains strategyThreadMap which has the execution order
      const strategyThreadMap = (strategyRun as unknown as { strategyThreadMap?: Record<string, unknown[]> }).strategyThreadMap;
      
      if (strategyThreadMap) {
        // Extract first thread's work items to build steps
        const firstThreadKey = Object.keys(strategyThreadMap)[0];
        const workItems = strategyThreadMap[firstThreadKey];
        
        if (workItems && Array.isArray(workItems)) {
          // Create a minimal StatefulStrategyJson structure
          const steps = workItems.map((work: unknown) => ({
            execution: (work as { execution?: unknown }).execution,
          }));
          
          this.statefulStrategy = {
            identity: (strategyRun as unknown as { identity?: string }).identity as string,
            statelessStrategy: { steps },
          } as unknown as StatefulStrategyJson;
          
          // Also store strategyState if present
          if ((strategyRun as unknown as { strategyState?: StrategyStateJson }).strategyState) {
            (this.statefulStrategy as unknown as { strategyState?: StrategyStateJson }).strategyState =
              (strategyRun as unknown as { strategyState?: StrategyStateJson }).strategyState;
          }
          
          console.log('Built statefulStrategy from strategy_run event:', this.statefulStrategy);
          this.generateTimeline(false);
          return; // Early return after processing
        }
      }
    }

    // Complete strategy
    if (kind === 'graph_end' && this.statefulStrategy) {
      this.generateTimeline(true);
      return;
    }

    // Update strategyState incrementally from step_complete events
    if (label === 'step_complete' && this.statefulStrategy) {
      const update = eventObj['strategyRunUpdate'] as StrategyRunUpdateJson | undefined;
      const delta = update?.strategyStateUpdate;
      if (!delta) return;

      // strategyState is the evolving executionId -> roleMap object
      const ss = this.statefulStrategy as unknown as {
        strategyState?: StrategyStateJson;
      };
      if (!ss.strategyState) ss.strategyState = {} as StrategyStateJson;
      const base = ss.strategyState;

      for (const [executionId, roleMap] of Object.entries(delta)) {
        const existingExec = (base as Record<string, Record<string, unknown>>)[executionId] || {};
        for (const [resourceRoleId, resourceRecord] of Object.entries(roleMap as Record<string, unknown>)) {
          existingExec[resourceRoleId] = resourceRecord;
        }
        (base as Record<string, Record<string, unknown>>)[executionId] = existingExec;
      }

      this.generateTimeline(false);
    }
  }

  /**
   * Load a recorded strategy spec (creates initial timeline)
   */
  public loadRecordedStrategy(spec: StatefulStrategyJson): void {
    this.updateStatefulStrategy(spec, false);
  }

  /**
   * Update the strategy spec and generate a new timeline
   */
  private updateStatefulStrategy(
    spec: StatefulStrategyJson,
    isComplete: boolean = false
  ): void {
    this.statefulStrategy = spec;
    this.generateTimeline(isComplete);
  }

  /**
   * Generate a timeline from the current strategy spec
   * @param isComplete - Whether this is the final timeline (graph_end) or incremental update
   */
  private generateTimeline(isComplete: boolean = false): void {
    console.log('generateTimeline called', { 
      hasStrategy: !!this.statefulStrategy,
      isComplete 
    });
    
    if (!this.statefulStrategy) {
      console.warn('No statefulStrategy available for timeline generation');
      return;
    }

    const sequence = sequenceFromStatefulStrategy(this.statefulStrategy);
    console.log('Job sequence extracted:', sequence);
    
    if (sequence.length === 0) {
      console.warn('Job sequence is empty, cannot generate timeline');
      return;
    }

    const timeline = buildTimelineFromSequence(sequence, this.timelineConfig);
    console.log('Timeline generated:', {
      segments: timeline.segments.length,
      duration: timeline.totalDurationMs,
      jobCount: timeline.jobSequence.length
    });

    // Notify subscribers of timeline update (for real-time visualization)
    if (this.onTimelineUpdateCallback) {
      console.log('Firing onTimelineUpdate callback');
      this.onTimelineUpdateCallback(timeline, this.statefulStrategy);
    }

    // Notify completion subscribers when strategy finishes
    if (isComplete && this.onCompleteCallback) {
      console.log('Firing onComplete callback');
      this.onCompleteCallback(timeline, this.statefulStrategy);
    }
  }

  /**
   * Get the current strategy spec
   */
  public getStatefulStrategy(): StatefulStrategyJson | null {
    return this.statefulStrategy;
  }

}
