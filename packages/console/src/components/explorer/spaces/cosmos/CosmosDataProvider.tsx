'use client';

import type { ReactNode } from 'react';
import type { ResourceTypeIdentityJson, ResourceJson } from '@toolproof-npm/schema';
import { createContext, useContext, useEffect, useMemo } from 'react';
import { useResources } from '@/_lib/client/firebaseWebHelpers';
import type { CosmosData } from '@/explorer/spaces/cosmos/_lib/types';
import { useAppSelector } from '@/_lib/client/redux/hooks';
import mockNaturalsData from '@/_lib/mockNaturals/mockNaturals.json';
import { extractJobMap } from '@toolproof-npm/shared/utils';
import { getLoopableJobIdentities } from '@/builders/strategy/_lib/utils/loopableJobs';

export type CosmosDataContextValue = {
  cosmosData: CosmosData;
  loading: boolean;
  error: Error | null;
  // Canonicalization helpers (by path) for consumers like visualization.
  // These do not change the underlying resourceMap; they provide a stable way to
  // treat `path` as the semantic identity while keeping all duplicates for provenance.
  resolveCanonicalIdentity: (identity: string) => string;
  getCanonicalResourceByIdentity: (resourceTypeRef: ResourceTypeIdentityJson, identity: string) => ResourceJson | undefined;
  getCanonicalResourceByPath: (resourceTypeRef: ResourceTypeIdentityJson, path: string) => ResourceJson | undefined;
  getProvenanceByPath: (resourceTypeRef: ResourceTypeIdentityJson, path: string) => ResourceJson[];
};

const CosmosDataContext = createContext<CosmosDataContextValue | null>(null);

export function useCosmosData(): CosmosDataContextValue {
  const context = useContext(CosmosDataContext);
  if (!context) {
    throw new Error('useCosmosData must be used within a CosmosDataProvider');
  }
  return context;
}

interface CosmosDataProviderProps {
  children: ReactNode;
}

export default function CosmosDataProvider({ children }: CosmosDataProviderProps) {

  const mockModeEnabled = useAppSelector((state) => state.config.mockModeEnabled);
  const initialMockNaturalIndices = useAppSelector((state) => state.config.initialMockNaturalIndices);
  const mockNaturalsFromRedux = useAppSelector((state) => state.mockMode.mockNaturals);

  const resourceTypeIdentities: ResourceTypeIdentityJson[] = useMemo(() => {
    // When mock mode is enabled, exclude TYPE-Natural from Firebase fetch

    const base = [
      'TYPE-ResourceFormat',
      'TYPE-ResourceType',
      'TYPE-Job',
      'TYPE-Boolean',
      'TYPE-StatefulStrategy',
    ] as ResourceTypeIdentityJson[];

    if (mockModeEnabled) {
      return base;
    }
    return [
      ...base,
      'TYPE-Natural',
    ];
  }, [mockModeEnabled]);

  const { items: resourceMap, loading: resourceMapLoading, error: resourceMapError } = useResources(resourceTypeIdentities);

  const loading = resourceMapLoading;
  const error = resourceMapError ?? null;

  useEffect(() => {
    if (error) console.error('[CosmosDataProvider] Cosmos data error', error);
  }, [error]);

  const cosmosData: CosmosData = useMemo(() => {
    let finalResourceMap = resourceMap;
    
    // When mock mode is enabled, replace TYPE-Natural with mock data
    if (mockModeEnabled) {
      // Start with initial mock naturals from JSON file (cast to ResourceJson[])
      const mockNaturalsAll = mockNaturalsData as ResourceJson[];
      const allowed = new Set((initialMockNaturalIndices ?? []).filter((n) => Number.isInteger(n) && n >= 0));
      const mockNaturals = allowed.size === 0
        ? []
        : mockNaturalsAll.filter((_, idx) => allowed.has(idx));

      // Add any dynamically created mock naturals from Redux
      const dynamicMockNaturals = Object.values(mockNaturalsFromRedux);

      finalResourceMap = {
        ...resourceMap,
        'TYPE-Natural': [...mockNaturals, ...dynamicMockNaturals],
      };
    }

    // Derive jobMap from resourceMap for convenience
    const jobMap = extractJobMap(finalResourceMap);

    // Derived list of jobs that can be used in a for-loop
    const loopableJobs = getLoopableJobIdentities(jobMap);

    return {
      resourceMap: finalResourceMap,
      jobMap,
      loopableJobs,
    };
  }, [resourceMap, mockModeEnabled, mockNaturalsFromRedux, initialMockNaturalIndices]);

  const canonicalIndex = useMemo(() => {
    const resourceByIdentity: Record<string, ResourceJson> = {};
    const provenanceByTypeAndPath: Record<string, Record<string, ResourceJson[]>> = {};
    const canonicalByTypeAndPath: Record<string, Record<string, ResourceJson>> = {};
    const canonicalIdentityByIdentity: Record<string, string> = {};

    const getTimestampMs = (resource: ResourceJson): number => {
      if (typeof resource.timestamp !== 'string') return Number.POSITIVE_INFINITY;
      const ms = Date.parse(resource.timestamp);
      return Number.isFinite(ms) ? ms : Number.POSITIVE_INFINITY;
    };

    const resourceMapEntries = Object.entries(cosmosData.resourceMap) as Array<
      [ResourceTypeIdentityJson, ResourceJson[]]
    >;

    for (const [resourceTypeRef, resources] of resourceMapEntries) {
      for (const resource of resources ?? []) {
        resourceByIdentity[resource.identity] = resource;

        if (typeof resource.path !== 'string' || resource.path.length === 0) continue;
        (provenanceByTypeAndPath[resourceTypeRef] ??= {});
        (provenanceByTypeAndPath[resourceTypeRef][resource.path] ??= []).push(resource);
      }
    }

    for (const [resourceTypeRef, byPath] of Object.entries(provenanceByTypeAndPath)) {
      const canonicalForType: Record<string, ResourceJson> = {};

      for (const [path, candidates] of Object.entries(byPath)) {
        // Earliest timestamp wins; tie-break deterministically by identity.
        candidates.sort((a, b) => {
          const ta = getTimestampMs(a);
          const tb = getTimestampMs(b);
          if (ta !== tb) return ta - tb;
          return String(a.identity).localeCompare(String(b.identity));
        });

        const canonical = candidates[0];
        if (!canonical) continue;
        canonicalForType[path] = canonical;

        for (const candidate of candidates) {
          canonicalIdentityByIdentity[candidate.identity] = canonical.identity;
        }
      }

      canonicalByTypeAndPath[resourceTypeRef] = canonicalForType;
    }

    return {
      resourceByIdentity,
      provenanceByTypeAndPath,
      canonicalByTypeAndPath,
      canonicalIdentityByIdentity,
    };
  }, [cosmosData]);

  // Log after mock naturals are merged
  /* useEffect(() => {
    console.log(
      '[CosmosDataProvider] Loaded Cosmos data:',
      JSON.stringify({
        resourceMap: cosmosData.resourceMap['TYPE-StatefulStrategy'],
      }, null, 2)
    );
  }, [cosmosData]); */

  const value: CosmosDataContextValue = useMemo(() => ({
    cosmosData,
    loading,
    error,
    resolveCanonicalIdentity: (identity: string) => {
      return canonicalIndex.canonicalIdentityByIdentity[identity] ?? identity;
    },
    getCanonicalResourceByIdentity: (resourceTypeRef: ResourceTypeIdentityJson, identity: string) => {
      const canonicalIdentity = canonicalIndex.canonicalIdentityByIdentity[identity] ?? identity;
      const resource = canonicalIndex.resourceByIdentity[canonicalIdentity];
      if (!resource) return undefined;
      if (resource.resourceTypeRef !== resourceTypeRef) return undefined;
      return resource;
    },
    getCanonicalResourceByPath: (resourceTypeRef: ResourceTypeIdentityJson, path: string) => {
      return canonicalIndex.canonicalByTypeAndPath[resourceTypeRef]?.[path];
    },
    getProvenanceByPath: (resourceTypeRef: ResourceTypeIdentityJson, path: string) => {
      return canonicalIndex.provenanceByTypeAndPath[resourceTypeRef]?.[path] ?? [];
    },
  }), [cosmosData, loading, error, canonicalIndex]);

  return (
    <CosmosDataContext.Provider value={value}>
      {children}
    </CosmosDataContext.Provider>
  );
}
