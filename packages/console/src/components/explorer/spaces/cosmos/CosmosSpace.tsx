import type {
  Exec<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Resource_JobJson,
  Resource_ResourceType<PERSON><PERSON>,
  ResourceType<PERSON><PERSON>,
  Step<PERSON>son,
  StrategyState<PERSON>son,
  WorkStep<PERSON>son,
  WorkStepIdentity<PERSON>son,
  BranchStepIdentityJson,
  WhileStepIdentityJson,
  ForStepIdentityJson,
} from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { useCosmosData } from '@/explorer/spaces/cosmos/CosmosDataProvider';
import { useGenesisData } from '@/explorer/spaces/genesis/GenesisDataProvider';
import { useAppSelector, useAppDispatch } from '@/_lib/client/redux/hooks';
import { addMockNatural } from '@/_lib/client/redux/features/mockModeSlice';
import type { CenterSelection } from '@/builders/strategy/contexts/CenterSelectionContext';
import type { SelectedIndex } from '@/builders/strategy/_lib/types';
import React, { useMemo, useRef, useState, useEffect, use<PERSON><PERSON>back } from 'react';
import { Canvas, type ThreeEvent, useFrame } from '@react-three/fiber';
import {
  OrbitControls,
  Box,
  Torus,
  Edges,
  Html,
  Grid,
  MeshPortalMaterial,
  Plane,
  Line,
  Sphere,
} from '@react-three/drei';
import * as THREE from 'three';
import { gsap } from 'gsap';
import { degToRad } from 'three/src/math/MathUtils.js';
import { StrategyEventNormalizer } from './eventNormalizer';
import { TimelineRunner } from './timeline/timelineRunner';
import type { TimelineConfig } from './timeline/timelineTypes';
import { useJobPanelAnimation } from './hooks/useJobPanelAnimation';
import { useSlideTrace } from './hooks/useSlideTrace';
import { PANEL_ANIMATION, getPhaseProgressFractions } from './hooks/animationConstants';
import { SlideTrace } from './SlideTrace';

type StepIdentityJson = WorkStepIdentityJson | BranchStepIdentityJson | WhileStepIdentityJson | ForStepIdentityJson;

export type CosmosSpaceMode = 'builder' | 'runner';

export type CosmosSpaceHandle = {
  runDemoTimeline: () => void;
};

export type CosmosSpaceBuilderBridge = {
  // Strategy content & state
  strategySteps?: StepJson[];
  strategyState?: StrategyStateJson;
  
  // Build step actions
  onBuildWorkStep?: (jobId: JobIdentityJson) => Promise<void>;
  onBuildBranchStep?: (jobIds: JobIdentityJson[]) => Promise<void>;
  onBuildWhileStep?: (jobId: JobIdentityJson) => Promise<void>;
  onBuildForStep?: (jobId: JobIdentityJson) => Promise<void>;
  
  // Selection actions
  onSelectStep?: (sel: SelectedIndex) => void;
  onClearSelection?: () => void;
  
  // Selection state
  activeStep?: StepJson | null;
  
  // Center selection state
  centerSelection?: CenterSelection;
  setCenterSelection?: React.Dispatch<React.SetStateAction<CenterSelection>>;
};

type CosmosSpaceProps = {
  mode?: CosmosSpaceMode;
  onTimelineStateChange?: (isAnimating: boolean) => void;
  builder?: CosmosSpaceBuilderBridge;
};

const orderJobsForCosmosPanels = (jobs: JobJson[]): JobJson[] => {
  const preferredOrder = ['add', 'subtract', 'multiply', 'divide'];
  const preferredIndex = new Map(preferredOrder.map((name, idx) => [name, idx] as const));

  const getName = (job: JobJson): string => {
    const name = job.name;
    if (!name) throw new Error(`Job ${job.identity} missing name`);
    return name.trim().toLowerCase();
  };

  return [...jobs].sort((a, b) => {
    const an = getName(a);
    const bn = getName(b);

    const ai = preferredIndex.get(an);
    const bi = preferredIndex.get(bn);

    if (ai !== undefined && bi !== undefined) return ai - bi;
    if (ai !== undefined) return -1;
    if (bi !== undefined) return 1;

    const byName = an.localeCompare(bn);
    if (byName !== 0) return byName;
    return a.identity.localeCompare(b.identity);
  });
};

const CosmosSpace = React.forwardRef<CosmosSpaceHandle, CosmosSpaceProps>(function CosmosSpace(
  props,
  ref
) {
  const { mode = 'builder', onTimelineStateChange, builder } = props;

  // Builder-provided strategy content & state (optional)
  const strategySteps = useMemo(
    () => builder?.strategySteps ?? [],
    [builder?.strategySteps]
  );
  const strategyState = useMemo(
    () => builder?.strategyState ?? ({} as StrategyStateJson),
    [builder?.strategyState]
  );

  // Builder-provided actions (optional)
  const onBuildWorkStep = builder?.onBuildWorkStep;
  const onBuildBranchStep = builder?.onBuildBranchStep;
  const onBuildWhileStep = builder?.onBuildWhileStep;
  const onBuildForStep = builder?.onBuildForStep;
  const onSelectStep = builder?.onSelectStep;
  const onClearSelection = builder?.onClearSelection;

  // Timeline inputs (feature-flagged; can be moved to Redux later)
  // Runner relies on live timeline events.
  const enableLiveTimelineEvents = true;
  const enableRecordedTimeline = false;
  const autoLoadRecordedTimeline = false;
  const recordedFilePath = '/src/components/explorer/spaces/cosmos/strategyExecution.json';

  // Fetch cosmos and genesis data
  const {
    cosmosData,
    loading: cosmosLoading,
    error: cosmosError,
  } = useCosmosData();

  const { jobMap, resourceMap } = cosmosData;

  // Redux for mock mode
  const mockModeEnabled = useAppSelector((state) => state.config.mockModeEnabled);
  const dispatch = useAppDispatch();

  const {
    schemaGenesis,
    resourceTypeGenesis,
    loading: genesisLoading,
    error: genesisError,
  } = useGenesisData();

  // Log the data when component mounts or data changes
  /* useEffect(() => {
    console.log(' CosmosSpace - Cosmos Data:', {
      cosmosData,
      cosmosLoading,
      cosmosError,
    });
  }, [cosmosData, cosmosLoading, cosmosError]); */

  // Track selected/hovered job identities (domain identities: JOB-*)
  const [activatedJobIdLocal, setActivatedJobIdLocal] = useState<JobIdentityJson | null>(null);
  const [slidingJobId, setSlidingJobId] = useState<JobIdentityJson | null>(null);
  const [hoveredJobId, setHoveredJobId] = useState<JobIdentityJson | null>(null);

  // Track selected/animated strategy step identities (WorkStep panels)
  const [activatedWorkStepIdLocal, setActivatedWorkStepIdLocal] = useState<StepIdentityJson | null>(null);
  const [slidingWorkStepId, setSlidingWorkStepId] = useState<StepIdentityJson | null>(null);
  const [hoveredWorkStepId, setHoveredWorkStepId] = useState<StepIdentityJson | null>(null);

  // Step selection/highlight: if builder provides activeStep, use it as source-of-truth.
  // Otherwise, fall back to local selection for standalone/runner mode.
  const [selectedStepIdLocal, setSelectedStepIdLocal] = useState<StepIdentityJson | null>(null);
  const selectedStepIdFromBuilder = useMemo(() => {
    const step = builder?.activeStep;
    if (!step) return null;
    return String((step as StepJson).identity) as StepIdentityJson;
  }, [builder?.activeStep]);
  const selectedStepId = builder?.activeStep !== undefined ? selectedStepIdFromBuilder : selectedStepIdLocal;

  // Track hovered Natural resource identity (RESOURCE-* / N-*) for hover styling.
  const [hoveredNaturalId, setHoveredNaturalId] = useState<string | null>(null);

  // Track which entity is currently centered. Source-of-truth lives in builder when provided.
  const [centerSelectionLocal, setCenterSelectionLocal] = useState<CenterSelection>(null);

  const centerSelection = builder?.centerSelection !== undefined
    ? builder.centerSelection
    : centerSelectionLocal;
  const setCenterSelection = builder?.setCenterSelection !== undefined
    ? builder.setCenterSelection
    : setCenterSelectionLocal;

  // Derive "activated" ids from the centered selection (shared), falling back to legacy local state when not in StrategyBuilder.
  const activatedJobId = centerSelection?.kind === 'job' ? centerSelection.id : activatedJobIdLocal;
  const activatedWorkStepId = centerSelection?.kind === 'step' ? centerSelection.id : activatedWorkStepIdLocal;

  // Preserve old setter call sites while centralizing truth in centerSelection.
  const setActivatedJobId = useCallback(
    (id: JobIdentityJson | null) => {
      if (id) {
        setCenterSelection({ kind: 'job', id });
        setActivatedJobIdLocal(id);
        return;
      }
      setCenterSelection((prev) => (prev?.kind === 'job' ? null : prev));
      setActivatedJobIdLocal(null);
    },
    [setCenterSelection]
  );

  const setActivatedWorkStepId = useCallback(
    (id: StepIdentityJson | null) => {
      if (id) {
        setCenterSelection({ kind: 'step', id });
        setActivatedWorkStepIdLocal(id);
        return;
      }
      setCenterSelection((prev) => (prev?.kind === 'step' ? null : prev));
      setActivatedWorkStepIdLocal(null);
    },
    [setCenterSelection]
  );

  // Role-binding interaction state (click input role -> click natural to bind).
  const [pendingInputBind, setPendingInputBind] = useState<
    { stepId: StepIdentityJson; inputRoleId: string } | null
  >(null);

  const activatedWorkStepIdRef = useRef<StepIdentityJson | null>(null);
  useEffect(() => {
    activatedWorkStepIdRef.current = activatedWorkStepId;
  }, [activatedWorkStepId]);

  const centerSelectionRef = useRef<typeof centerSelection>(null);
  useEffect(() => {
    centerSelectionRef.current = centerSelection;
  }, [centerSelection]);

  const autoReturnTimeoutRef = useRef<{ stepId: StepIdentityJson; timeoutId: ReturnType<typeof setTimeout> } | null>(null);

  const buildMenuEnabled =
    mode === 'builder' &&
    Boolean(onBuildWorkStep || onBuildBranchStep || onBuildWhileStep || onBuildForStep);
  const [buildMenuJobId, setBuildMenuJobId] = useState<JobIdentityJson | null>(null);
  // When the user clicks a job to activate it, we defer opening the menu until the job reaches the center.
  const [pendingBuildMenuJobId, setPendingBuildMenuJobId] = useState<JobIdentityJson | null>(null);

  useEffect(() => {
    if (!buildMenuEnabled) return;

    // If the active job changes away from the menu job, close the menu.
    if (buildMenuJobId && activatedJobId !== buildMenuJobId) {
      setBuildMenuJobId(null);
    }

    // If we're waiting to open a menu, do it only once the job is activated and no longer sliding.
    if (pendingBuildMenuJobId) {
      const isActivated = activatedJobId === pendingBuildMenuJobId;
      const isSliding = slidingJobId === pendingBuildMenuJobId;
      if (isActivated && !isSliding) {
        setBuildMenuJobId(pendingBuildMenuJobId);
        setPendingBuildMenuJobId(null);
      }
    }
  }, [buildMenuEnabled, activatedJobId, slidingJobId, buildMenuJobId, pendingBuildMenuJobId]);

  // While the timeline is running, track the currently active job (domain identity: JOB-*)
  const [activeAnimatingJobId, setActiveAnimatingJobId] = useState<JobIdentityJson | null>(null);
  const activeAnimatingJobIdRef = useRef<JobIdentityJson | null>(null);

  // Track if animation is running
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    onTimelineStateChange?.(isAnimating);
  }, [isAnimating, onTimelineStateChange]);

  // Track dynamically created natural resources during animation
  const [createdNaturals, setCreatedNaturals] = useState<ResourceJson[]>([]);
  const statefulStrategyRef = useRef<unknown>(null);

  // Temporary display override: we currently show the strategy type as "StatelessStrategy".
  // A later visualization stage will flip this to the true "StatefulStrategy" label.
  const [statefulStrategyDisplayName] = useState<string>('StatelessStrategy');

  // Extract job resources early (needed by effects below)
  const jobResources = useMemo(
    () =>
      (resourceMap[CONSTANTS.SPECIALS.TYPE_Job] ?? []) as Resource_JobJson[],
    [resourceMap]
  );

  const jobNameByJobId = useMemo(() => {
    const m = new Map<JobIdentityJson, string>();
    for (const [jobId, jobData] of jobMap.entries()) {
      const name = String(jobData?.name ?? jobId);
      m.set(jobId, name);
    }
    return m;
  }, [jobMap]);

  // Timeline system refs
  const eventNormalizerRef = useRef<StrategyEventNormalizer | null>(null);
  const timelineRunnerRef = useRef<TimelineRunner | null>(null);

  // Indirection to avoid hook dependency churn in the timeline runner callbacks
  const applyTimelineAnimationRef = useRef<
    ((
      stepGroup: THREE.Group,
      stepIdentity: StepIdentityJson,
      phase: 'PAUSING' | 'PULLING_IN' | 'INSIDE' | 'PULLING_OUT',
      t: number
    ) => void) | null
  >(null);

  // Timeline configuration with hardcoded defaults (can be moved to Redux config later if needed)
  const timelineConfig: TimelineConfig = useMemo(
    () => ({
      pullInDuration: 800,
      pullOutDuration: 600,
      pauseBetweenJobs: 400,
      pauseInside: 1200,
    }),
    []
  );

  // Initialize event normalizer (only needed for live execution mode, not runner mode)
  useEffect(() => {
    // Skip event normalizer in runner mode with steps
    if (mode === 'runner' && strategySteps && strategySteps.length > 0) {
      return;
    }

    if (!eventNormalizerRef.current) {
      const normalizer = new StrategyEventNormalizer(timelineConfig);

      // Subscribe to timeline updates (fired incrementally during live execution)
      // Note: This is disabled in runner mode with steps - see loadAndRunHardcodedTimeline instead
      normalizer.onTimelineUpdate((timeline, statefulStrategy) => {
        // Store statefulStrategy reference for resource creation
        statefulStrategyRef.current = statefulStrategy;

        if (!timelineRunnerRef.current) {
          // First timeline: create and start runner
          const runner = new TimelineRunner(timeline, {
            loop: false,
            loopBreakMs: 3000,
          });

          // Subscribe to animation ticks for JOB animation (legacy mode)
          runner.onTick(({ segment, t }) => {
            // segment.jobId is the domain identity (JOB-*) from execution.jobRef
            const jobIdentity = segment.jobId as JobIdentityJson;

            // Update active job once per segment (avoid setState every frame)
            if (activeAnimatingJobIdRef.current !== jobIdentity) {
              activeAnimatingJobIdRef.current = jobIdentity;
              setActiveAnimatingJobId(jobIdentity);
            }

            const jobRef = jobRefs.current[jobIdentity];
            if (!jobRef?.current) {
              console.warn('No mesh ref for job identity:', jobIdentity, 'Available refs:', Object.keys(jobRefs.current));
              return;
            }

            // This won't work with the new step-based signature, but that's OK
            // because this code path is disabled in runner mode
            // @ts-expect-error - Legacy job animation path, not used in runner mode
            applyTimelineAnimationRef.current?.(jobRef.current, jobIdentity, segment.phase, t);
          });
          runner.start();
          timelineRunnerRef.current = runner;
          setIsAnimating(true);
        } else {
          // Subsequent updates: update timeline seamlessly
          timelineRunnerRef.current.updateTimeline(timeline);
        }
      });

      // Subscribe to completion
      normalizer.onComplete((timeline, statefulStrategy) => {
        setIsAnimating(false);
        activeAnimatingJobIdRef.current = null;
        setActiveAnimatingJobId(null);
        setActivatedJobId(null);
        setSlidingJobId(null);
        setHoveredJobId(null);
      });

      eventNormalizerRef.current = normalizer;
    }
  }, [mode, strategySteps, timelineConfig, setActivatedJobId]);

  // Listen for live events from StrategyBuilder
  useEffect(() => {
    if (!enableLiveTimelineEvents) return;

    const handleGraphEvent = (event: Event) => {
      const customEvent = event as CustomEvent;
      const rawEvent = customEvent.detail;

      if (!eventNormalizerRef.current) return;

      eventNormalizerRef.current.processLiveEvent(rawEvent);
    };

    window.addEventListener('toolproof:graphEvent', handleGraphEvent);

    return () => {
      window.removeEventListener('toolproof:graphEvent', handleGraphEvent);
    };
  }, [enableLiveTimelineEvents]);

  // Load recorded strategy file (feature-flagged)
  useEffect(() => {
    if (!enableRecordedTimeline || !autoLoadRecordedTimeline) return;

    const loadRecordedStrategy = async () => {
      try {
        const response = await fetch(recordedFilePath);
        if (!response.ok) {
          console.warn('Could not load recorded strategy file:', recordedFilePath);
          return;
        }

        const events = await response.json();
        if (!Array.isArray(events) || events.length === 0) {
          console.warn('Recorded strategy file is empty or invalid');
          return;
        }

        // Process events through normalizer
        if (eventNormalizerRef.current) {
          for (const event of events) {
            eventNormalizerRef.current.processLiveEvent(event);
          }
        }
      } catch (error) {
        console.error('Error loading recorded strategy:', error);
      }
    };

    // Only load on mount or when file path changes
    loadRecordedStrategy();
  }, [autoLoadRecordedTimeline, enableRecordedTimeline, recordedFilePath]);

  // Cleanup timeline runner on unmount
  useEffect(() => {
    return () => {
      if (timelineRunnerRef.current) {
        timelineRunnerRef.current.stop();
        timelineRunnerRef.current = null;
      }
    };
  }, []);

  React.useImperativeHandle(
    ref,
    () => ({
      runDemoTimeline: () => runDemoTimelineRef.current(),
    }),
    []
  );

  /**
   * Apply timeline-driven animation to a step mesh
   * Mimics job animation behavior with same GSAP-style phases
   */
  const applyTimelineAnimation = (
    stepGroup: THREE.Group,
    stepIdentity: StepIdentityJson, // The WORK-* or step identity
    phase: 'PAUSING' | 'PULLING_IN' | 'INSIDE' | 'PULLING_OUT',
    t: number
  ) => {
    // Find step's home position in world space (from our panel layout).
    const home = getWorkStepHomePosition(stepIdentity);
    if (!home) return;

    const target = localOffsetToCenterForYaw(strategyStepPanelLayout.yaw, home);
    const { f1, f2, f3, downY } = getPhaseProgressFractions();

    // Track phase transitions for state gating
    if (!stepGroup.userData.timelinePhase) stepGroup.userData.timelinePhase = null;
    const prevPhase = stepGroup.userData.timelinePhase as typeof phase | null;
    if (prevPhase !== phase) {
      stepGroup.userData.timelinePhase = phase;

      if (phase === 'PULLING_IN') {
        // Step is animating to center - start trace
        resetStepSlideTrace(stepIdentity);
        setActiveAnimatingStepId(stepIdentity);
        setActivatedJobId(null);
        setSlidingJobId(null);
      }

      if (phase === 'INSIDE') {
        // Step is at center (executing) - clear trace and activate step
        setActiveAnimatingStepId(null);
        setSlidingJobId(null);
        setHoveredJobId(null);
        setActivatedJobId(null);
        setActivatedWorkStepId(stepIdentity);
        setCenterSelection({ kind: 'step', id: stepIdentity });
      }

      if (phase === 'PULLING_OUT') {
        // Step is returning home - start trace and clear activation
        resetStepSlideTrace(stepIdentity);
        setActiveAnimatingStepId(stepIdentity);
        setActivatedJobId(null);
        setSlidingJobId(null);
        // Keep roles/connectors visible while the step is on its way back.
        // We'll clear activation near the end of the PULLING_OUT motion instead.
      }

      if (phase === 'PAUSING') {
        // Step is idle at home - clear trace and activation
        setActiveAnimatingStepId(null);
        setActivatedJobId(null);
        setSlidingJobId(null);
        setActivatedWorkStepId(null);
        setCenterSelection(null);
      }
    }

    switch (phase) {
      case 'PAUSING':
        // Idle at home (no offset)
        stepGroup.position.set(0, 0, 0);
        break;

      case 'PULLING_IN':
        // Down → in → up (same feel as click animation)
        if (t < f1) {
          const tt = t / f1;
          stepGroup.position.set(0, (1 - tt) * 0 + tt * downY, 0);
        } else if (t < f1 + f2) {
          const tt = (t - f1) / f2;
          stepGroup.position.set(target[0] * tt, downY, target[2] * tt);
        } else {
          const tt = (t - f1 - f2) / f3;
          stepGroup.position.set(target[0], downY * (1 - tt), target[2]);
        }
        
        // Push trace sample
        pushStepSlideTrace(stepIdentity, stepGroup.position);
        break;

      case 'INSIDE':
        // Held at center.
        stepGroup.position.set(target[0], 0, target[2]);
        break;

      case 'PULLING_OUT':
        // Down → out → up
        if (t < f1) {
          const tt = t / f1;
          stepGroup.position.set(target[0], (1 - tt) * 0 + tt * downY, target[2]);
        } else if (t < f1 + f2) {
          const tt = (t - f1) / f2;
          stepGroup.position.set(target[0] * (1 - tt), downY, target[2] * (1 - tt));
        } else {
          const tt = (t - f1 - f2) / f3;
          stepGroup.position.set(0, downY * (1 - tt), 0);
        }
        
        // Push trace sample
        pushStepSlideTrace(stepIdentity, stepGroup.position);

        // Keep roles/connectors during return; clear near the end so they don't pop off too early.
        if (t > 0.8) {
          setActivatedWorkStepId(null);
          setCenterSelection(null);
        }
        
        // Clear animating state when almost done
        if (t > 0.99) {
          setActiveAnimatingStepId(null);
        }
        break;
    }
  };

  // Keep the runner callback pointing at the latest implementation
  applyTimelineAnimationRef.current = applyTimelineAnimation;

  /**
   * Helper to find execution ID from a timeline job ID (which is a JOB-* reference)
   */
  const getExecutionIdForTimelineJob = (timelineJobId: string): string | null => {
    const sfs = statefulStrategyRef.current as { strategyThreadMap?: Record<string, unknown[]> } | null;
    if (!sfs?.strategyThreadMap) return null;

    // Search all threads for work items with matching jobRef
    for (const thread of Object.values(sfs.strategyThreadMap)) {
      for (const workItem of thread) {
        const wi = workItem as { kind?: string; execution?: { jobRef?: string; identity?: string } };
        if (wi.kind === 'work' && wi.execution?.jobRef === timelineJobId) {
          return wi.execution.identity ?? null;
        }
      }
    }
    return null;
  };

  /**
   * Extract newly materialized natural resources from strategyState for a given execution
   * and add them to the scene
   */
  const addCreatedNaturalsForExecution = (executionId: string) => {
    const sfs = statefulStrategyRef.current as { strategyState?: Record<string, unknown> } | null;
    if (!sfs?.strategyState) return;

    const executionState = sfs.strategyState[executionId];
    if (!executionState) return;

    const newNaturals: ResourceJson[] = [];

    // Find all materialized resources with extractedData.identity (natural numbers)
    for (const [roleId, binding] of Object.entries(executionState)) {
      const b = binding as unknown as {
        kind?: string;
        identity?: string;
        extractedData?: { identity?: unknown };
        path?: string;
        timestamp?: string;
      };
      if (b?.kind === 'materialized' && b?.extractedData?.identity !== undefined) {
        const naturalValue = b.extractedData.identity;

        // Check if this natural already exists in cosmos data or created naturals
        const existingInCosmos = resourceMap[CONSTANTS.SPECIALS.TYPE_Natural]?.find(
          (r: ResourceJson) => {
            const extracted = (r as unknown as { extractedData?: { identity?: unknown } }).extractedData;
            return extracted?.identity === naturalValue;
          }
        );

        const existingInCreated = createdNaturals.find((r) => {
          const extracted = (r as unknown as { extractedData?: { identity?: unknown } }).extractedData;
          return extracted?.identity === naturalValue;
        });

        if (!existingInCosmos && !existingInCreated) {
          // Create new natural resource with all properties from strategyState
          const newResource = {
            identity: b.identity || `RESOURCE-${naturalValue}`,
            extractedData: {
              identity: naturalValue,
            },
            resourceTypeRef: CONSTANTS.SPECIALS.TYPE_Natural as string,
            creationContext: {
              resourceRoleRef: roleId,
              executionRef: executionId,
            },
            kind: 'materialized',
            timestamp: b.timestamp || new Date().toISOString(),
            ...(b.path && { path: b.path }), // Include path if present (for mock mode)
          } as unknown as ResourceJson;

          console.log('[COSMOS] Adding created natural:', naturalValue, 'with identity:', newResource.identity, 'path:', b.path);
          newNaturals.push(newResource);
        }
      }
    }

    if (newNaturals.length > 0) {
      setCreatedNaturals((prev) => [...prev, ...newNaturals]);

      // In mock mode, persist naturals to Redux just like StrategyBuilder does for live runs
      if (mockModeEnabled) {
        newNaturals.forEach((resource) => {
          // Only dispatch if it's a mock natural (has path starting with 'mock://natural/')
          const hasPath = 'path' in resource && typeof (resource as { path?: unknown }).path === 'string';
          if (hasPath && ((resource as { path: string }).path.startsWith('mock://natural/'))) {
            console.log('[MOCK MODE] Persisting Natural to Redux from replay:', resource.identity);
            dispatch(addMockNatural(resource));
          }
        });
      }
    }
  };

  const TYPE_ResourceType_ID = 'TYPE-ResourceType';
  const typeRingRadius = 8.5;
  // Align polar angle 0 with the scene's Natural-resource "forward" axis (-Z)
  const typeRingAngleOffset = -Math.PI / 2;
  const typeRing = useMemo(() => {
    const resourceTypeResources =
      (resourceMap[TYPE_ResourceType_ID] ?? []) as Resource_ResourceTypeJson[];

    const allowedTypeIdentities = new Set<string>([
      String(CONSTANTS.SPECIALS.TYPE_Job),
      String(CONSTANTS.SPECIALS.TYPE_StatefulStrategy),
      String(CONSTANTS.SPECIALS.TYPE_Natural),
      String(CONSTANTS.SPECIALS.TYPE_Boolean),
    ]);

    const allTypes = resourceTypeResources
      .map((item) => {
        const extractedData = item.extractedData as ResourceTypeJson;
        return {
          id: String(extractedData.identity ?? ''),
          name: String(extractedData.name ?? extractedData.identity ?? ''),
          description: String(extractedData.description ?? ''),
          resourceFormatId: String(extractedData.resourceFormatRef ?? ''),
        };
      })
      .filter((t) => t.id && allowedTypeIdentities.has(t.id));

    const TYPE_Natural_ID = String(CONSTANTS.SPECIALS.TYPE_Natural);
    const TYPE_Boolean_ID = String(CONSTANTS.SPECIALS.TYPE_Boolean);
    const TYPE_StatefulStrategy_ID = String(CONSTANTS.SPECIALS.TYPE_StatefulStrategy);
    const TYPE_Job_ID = String(CONSTANTS.SPECIALS.TYPE_Job);

    const natural = allTypes.find((t) => t.id === TYPE_Natural_ID);
    const bool = allTypes.find((t) => t.id === TYPE_Boolean_ID);
    const statefulStrategy = allTypes.find((t) => t.id === TYPE_StatefulStrategy_ID);
    const job = allTypes.find((t) => t.id === TYPE_Job_ID);

    if (!natural || !bool || !statefulStrategy || !job) {
      return { types: [], positions: new Map<string, [number, number, number]>() };
    }

    const angleMap = new Map<string, number>();
    angleMap.set(TYPE_Natural_ID, 0);
    angleMap.set(TYPE_Boolean_ID, Math.PI / 2);
    angleMap.set(TYPE_StatefulStrategy_ID, Math.PI);
    angleMap.set(TYPE_Job_ID, (3 * Math.PI) / 2);

    const types = [natural, bool, statefulStrategy, job];
    const positions = new Map<string, [number, number, number]>();
    for (const t of types) {
      const angle = angleMap.get(t.id) ?? 0;
      const adjustedAngle = angle + typeRingAngleOffset;
      positions.set(t.id, [
        Math.cos(adjustedAngle) * typeRingRadius,
        0,
        Math.sin(adjustedAngle) * typeRingRadius,
      ]);
    }

    return { types, positions };
  }, [resourceMap, typeRingAngleOffset]);

  // Default camera: stand "behind" TYPE-Boolean (further outward along its radial).
  // Keep target at the origin so we see the whole scene.
  const initialCamera = useMemo(() => {
    const boolTypeId = String(CONSTANTS.SPECIALS.TYPE_Boolean);
    const boolPos = typeRing.positions.get(boolTypeId);
    if (!boolPos) {
      return { position: [0, 8, 16] as [number, number, number], fov: 55 };
    }

    const radial = new THREE.Vector3(boolPos[0], 0, boolPos[2]);
    if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
    radial.normalize();

    const behindDistance = 8;
    const height = 8;
    const cam = radial.clone().multiplyScalar(typeRingRadius + behindDistance);
    return { position: [cam.x, height, cam.z] as [number, number, number], fov: 55 };
  }, [typeRing.positions]);

  // handleJobSelect is provided by useJobPanelAnimation (see near the bottom of this component).

  const strategyStepPanelLayout = useMemo(() => {
    const typeId = String(CONSTANTS.SPECIALS.TYPE_StatefulStrategy);
    const typePos = typeRing.positions.get(typeId);
    if (!typePos) {
      return { yaw: 0, items: [] as Array<{ step: StepJson; position: [number, number, number]; index: number }> };
    }

    const baseDir = new THREE.Vector3(typePos[0], 0, typePos[2]);
    if (baseDir.lengthSq() < 1e-6) baseDir.set(0, 0, 1);
    baseDir.normalize();

    const yaw = Math.atan2(baseDir.z, baseDir.x);

    const sheetWidth = 1.4;
    const spacing = 0.6;
    const step = sheetWidth + spacing;
    const startRadius = typeRingRadius + sheetWidth;

    const items = (strategySteps ?? []).map((s, idx) => {
      const radius = startRadius + idx * step;
      const p = baseDir.clone().multiplyScalar(radius);
      return { step: s, position: [p.x, 0, p.z] as [number, number, number], index: idx };
    });

    return { yaw, items };
  }, [strategySteps, typeRing.positions]);

  // WorkStep (strategy step panel) activation: animate selected step to center like Jobs.
  const workStepRefs = useRef<Record<string, React.RefObject<THREE.Group | null>>>({});
  const workStepActivationTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const workStepActivationInFlightRef = useRef<boolean>(false);
  const handleWorkStepSelectRef = useRef<(stepId: StepIdentityJson | null) => void>(() => undefined);

  // Use shared slide trace hook for steps
  const {
    slideTrace: stepSlideTrace,
    activeAnimatingId: activeAnimatingStepId,
    setActiveAnimatingId: setActiveAnimatingStepId,
    resetSlideTrace: resetStepSlideTrace,
    pushSlideTrace: pushStepSlideTrace,
  } = useSlideTrace<StepIdentityJson>();

  const getWorkStepHomePosition = useCallback(
    (id: StepIdentityJson): [number, number, number] | null => {
      const entry = strategyStepPanelLayout.items.find(
        (x) => String((x.step as StepJson).identity) === id
      );
      return entry?.position ?? null;
    },
    [strategyStepPanelLayout.items]
  );

  const animateWorkStepTo = useCallback((
    id: StepIdentityJson,
    targetLocal: [number, number, number],
    opts?: { speedMultiplier?: number }
  ): Promise<void> => {
    const group = workStepRefs.current[id]?.current;
    if (!group) return Promise.resolve();

    workStepActivationTimelineRef.current?.kill();
    workStepActivationTimelineRef.current = null;

    const downY = -1.4;
    const speedMultiplier = Math.max(0.1, opts?.speedMultiplier ?? 1);
    const d1 = 0.18 / speedMultiplier;
    const d2 = 0.45 / speedMultiplier;
    const d3 = 0.18 / speedMultiplier;

    return new Promise((resolve) => {
      const tl = gsap.timeline({ onComplete: () => resolve() });
      workStepActivationTimelineRef.current = tl;

      tl.to(group.position, { y: downY, duration: d1, ease: PANEL_ANIMATION.PHASE_1_EASE })
        .to(group.position, {
          x: targetLocal[0],
          z: targetLocal[2],
          duration: d2,
          ease: PANEL_ANIMATION.PHASE_2_EASE,
        })
        .to(group.position, { y: targetLocal[1], duration: d3, ease: PANEL_ANIMATION.PHASE_3_EASE });
    });
  }, []);

  const returnWorkStepHomeRef = useRef<(id: StepIdentityJson) => Promise<void>>(async () => undefined);

  const returnWorkStepHome = async (id: StepIdentityJson) => {
    // Start the animation with roles/connectors still visible
    const animationPromise = animateWorkStepTo(id, [0, 0, 0], { speedMultiplier: 0.25 });

    // Calculate total animation duration
    const speedMultiplier = 0.25;
    const d1 = PANEL_ANIMATION.PHASE_1_DURATION / speedMultiplier;
    const d2 = PANEL_ANIMATION.PHASE_2_DURATION / speedMultiplier;
    const d3 = PANEL_ANIMATION.PHASE_3_DURATION / speedMultiplier;
    const totalDuration = d1 + d2 + d3; // in seconds
    const clearRolesDelay = (totalDuration * 0.8) * 1000; // convert to ms

    // Fade away roles/connectors later in the return so they persist on the way back.
    setTimeout(() => {
      setActivatedWorkStepId(null);
    }, clearRolesDelay);

    await animationPromise;
  };

  returnWorkStepHomeRef.current = returnWorkStepHome;

  const handleWorkStepSelect = (stepId: StepIdentityJson | null) => {
    if (workStepActivationInFlightRef.current) return;

    const run = async () => {
      workStepActivationInFlightRef.current = true;
      try {
        // For now: never return-home due to interaction. Only the auto-return timer may do that.
        if (!stepId) return;
        if (activatedWorkStepId) return;

        // If a job is activated, deactivate it first (so roles/menus don't conflict).
        if (activatedJobId) {
          setBuildMenuJobId(null);
          setPendingBuildMenuJobId(null);
          handleJobSelect(activatedJobId);
        }

        const home = getWorkStepHomePosition(stepId);
        if (!home) {
          setActivatedWorkStepId(null);
          setSlidingWorkStepId(null);
          return;
        }

        setSlidingWorkStepId(stepId);
        await animateWorkStepTo(stepId, localOffsetToCenterForYaw(strategyStepPanelLayout.yaw, home));
        setActivatedWorkStepId(stepId);
        setCenterSelection({ kind: 'step', id: stepId });
        setSlidingWorkStepId(null);
      } finally {
        workStepActivationInFlightRef.current = false;
      }
    };

    void run();
  };

  // Avoid hook dependency churn; always call the latest handler from effects/timeouts.
  handleWorkStepSelectRef.current = handleWorkStepSelect;

  const naturalResources = useMemo(
    () => (resourceMap[CONSTANTS.SPECIALS.TYPE_Natural] ?? []) as ResourceJson[],
    [resourceMap]
  );

  const getNaturalOrdinalAndLabel = (r: ResourceJson, fallbackIdx: number): { ordinal: number; label: string } => {
    const extractedData = (r as unknown as { extractedData?: unknown }).extractedData;
    if (extractedData && typeof extractedData === 'object') {
      const value = (extractedData as Record<string, unknown>).value;
      if (typeof value === 'number' && Number.isFinite(value)) {
        return { ordinal: value, label: String(value) };
      }

      const identity = (extractedData as Record<string, unknown>).identity;
      if (typeof identity === 'number' && Number.isFinite(identity)) {
        return { ordinal: identity, label: String(identity) };
      }
      if (typeof identity === 'string') {
        const parsed = Number(identity);
        if (Number.isFinite(parsed)) {
          return { ordinal: parsed, label: String(identity) };
        }
      }
    }

    return { ordinal: fallbackIdx, label: String(r.identity) };
  };

  const handleNaturalClick = async (r: ResourceJson) => {
    // TODO: Implement binding using BindingsContext (similar to DOM_UI pattern)
    // Will need to select the step, get active execution, then call onbindInputRes
    console.warn('Natural resource binding not yet implemented in CosmosSpace', {
      resource: r.identity,
      pendingInputBind
    });
    setPendingInputBind(null);
  };

  const naturalPanelLayout = useMemo(() => {
    const naturalTypeId = String(CONSTANTS.SPECIALS.TYPE_Natural);
    const naturalTypePos = typeRing.positions.get(naturalTypeId);
    if (!naturalTypePos) return { yaw: 0, items: [] as Array<{ resource: ResourceJson; position: [number, number, number] }> };

    const baseDir = new THREE.Vector3(naturalTypePos[0], 0, naturalTypePos[2]);
    if (baseDir.lengthSq() < 1e-6) baseDir.set(1, 0, 0);
    baseDir.normalize();

    const yaw = Math.atan2(baseDir.z, baseDir.x);

    const allNaturals = [...naturalResources, ...createdNaturals];

    const sorted = [...allNaturals].sort((a, b) => {
      const ao = getNaturalOrdinalAndLabel(a, 0).ordinal;
      const bo = getNaturalOrdinalAndLabel(b, 0).ordinal;
      if (typeof ao === 'number' && typeof bo === 'number' && ao !== bo) return ao - bo;
      return String(a.identity).localeCompare(String(b.identity));
    });

    const sheetWidth = 1.4;
    const spacing = 0.6;
    const step = sheetWidth + spacing;
    const startRadius = typeRingRadius + sheetWidth;

    const items = sorted.map((r, idx) => {
      const { ordinal } = getNaturalOrdinalAndLabel(r, idx);
      const radius = startRadius + ordinal * step;
      const p = baseDir.clone().multiplyScalar(radius);
      return { resource: r, position: [p.x, 0, p.z] as [number, number, number] };
    });

    return { yaw, items };
  }, [naturalResources, createdNaturals, typeRing.positions]);

  const naturalPanelPosByResourceId = useMemo(() => {
    const m = new Map<string, [number, number, number]>();
    for (const { resource, position } of naturalPanelLayout.items) {
      m.set(String(resource.identity), position);
    }
    return m;
  }, [naturalPanelLayout.items]);

  const jobPanelLayout = useMemo(() => {
    const jobTypeId = String(CONSTANTS.SPECIALS.TYPE_Job);
    const jobTypePos = typeRing.positions.get(jobTypeId);
    if (!jobTypePos) return { yaw: 0, items: [] as Array<{ job: JobJson; position: [number, number, number] }> };

    const baseDir = new THREE.Vector3(jobTypePos[0], 0, jobTypePos[2]);
    if (baseDir.lengthSq() < 1e-6) baseDir.set(1, 0, 0);
    baseDir.normalize();

    const yaw = Math.atan2(baseDir.z, baseDir.x);

    const sortedJobs = orderJobsForCosmosPanels(Array.from(jobMap.values()));

    const sheetWidth = 1.4;
    const spacing = 0.6;
    const step = sheetWidth + spacing;
    const startRadius = typeRingRadius + sheetWidth;

    const items = sortedJobs.map((job, idx) => {
      const radius = startRadius + idx * step;
      const p = baseDir.clone().multiplyScalar(radius);
      return { job, position: [p.x, 0, p.z] as [number, number, number] };
    });

    return { yaw, items };
  }, [jobMap, typeRing.positions]);

  // Helper functions for converting world positions to local offsets (accounting for rotation)
  const localOffsetToCenter = (homeWorld: [number, number, number]): [number, number, number] => {
    // Outer group is rotated by jobPanelLayout.yaw. Convert world delta to the inner group's local coords.
    const dx = -homeWorld[0];
    const dz = -homeWorld[2];
    // NOTE: Three.js yaw rotation in XZ is:
    // x' = x*cos(theta) + z*sin(theta)
    // z' = -x*sin(theta) + z*cos(theta)
    // We want to express the world delta (-home) in the rotated panel group's local space,
    // so we apply theta = -yaw.
    const cy = Math.cos(-jobPanelLayout.yaw);
    const sy = Math.sin(-jobPanelLayout.yaw);
    const lx = dx * cy + dz * sy;
    const lz = -dx * sy + dz * cy;
    return [lx, 0, lz];
  };

  const localOffsetToCenterForYaw = (
    yaw: number,
    homeWorld: [number, number, number]
  ): [number, number, number] => {
    const dx = -homeWorld[0];
    const dz = -homeWorld[2];
    const cy = Math.cos(-yaw);
    const sy = Math.sin(-yaw);
    const lx = dx * cy + dz * sy;
    const lz = -dx * sy + dz * cy;
    return [lx, 0, lz];
  };

  const jobPanelPosByJobId = useMemo(() => {
    const m = new Map<JobIdentityJson, [number, number, number]>();
    for (const { job, position } of jobPanelLayout.items) {
      m.set(job.identity, position);
    }
    return m;
  }, [jobPanelLayout.items]);

  const jobEnvelopeByJobId = useMemo(() => {
    const m = new Map<JobIdentityJson, Resource_JobJson>();
    for (const { job } of jobPanelLayout.items) {
      const envelope = jobResources.find(r => (r.extractedData as JobJson).identity === job.identity);
      if (!envelope) continue;
      m.set(job.identity, envelope);
    }
    return m;
  }, [jobPanelLayout.items, jobResources]);

  // When all input roles are bound for the centered WorkStep, wait 1s then move it back home.
  // Track previous bound-count to only trigger on transition from "incomplete" to "complete".
  const prevBoundCountRef = useRef<{ stepId: StepIdentityJson; boundCount: number } | null>(null);

  useEffect(() => {
    const clearAutoReturn = () => {
      if (autoReturnTimeoutRef.current) {
        clearTimeout(autoReturnTimeoutRef.current.timeoutId);
        autoReturnTimeoutRef.current = null;
      }
    };

    const stepId = activatedWorkStepId;
    if (!stepId) {
      clearAutoReturn();
      prevBoundCountRef.current = null;
      return;
    }
    if (centerSelection?.kind !== 'step' || centerSelection.id !== String(stepId)) {
      clearAutoReturn();
      prevBoundCountRef.current = null;
      return;
    }

    // Don't auto-return while the user is still in the middle of a bind interaction.
    if (pendingInputBind && pendingInputBind.stepId === stepId) {
      clearAutoReturn();
      return;
    }

    const step = (strategySteps ?? []).find(
      (s) => String((s as StepJson).identity) === String(stepId)
    ) as StepJson | undefined;
    if (!step || String(step.kind) !== 'work') {
      clearAutoReturn();
      prevBoundCountRef.current = null;
      return;
    }

    const exec = (step as WorkStepJson).execution as ExecutionJson | undefined;
    const jobRef = exec?.jobRef;
    if (!exec || typeof jobRef !== 'string' || !jobRef) {
      clearAutoReturn();
      prevBoundCountRef.current = null;
      return;
    }

    const jobData = jobMap.get(jobRef as JobIdentityJson);
    const inputRoleIds = Object.keys(jobData?.roles?.inputMap ?? {});
    if (inputRoleIds.length === 0) {
      clearAutoReturn();
      prevBoundCountRef.current = null;
      return;
    }

    const inputBindingMap = (exec.roleBindings?.inputBindingMap ?? {}) as Record<string, unknown>;
    const execId = String(exec.identity);
    const bucket = (strategyState as StrategyStateJson)?.[execId as keyof StrategyStateJson] as
      | Record<string, unknown>
      | undefined;

    const isRoleBound = (roleId: string): boolean => {
      // A role is only considered "bound" if:
      // 1. It has an entry in inputBindingMap
      // 2. That entry points to a resource in strategyState
      // 3. That resource is a Natural (TYPE-Natural)
      const resourceId = inputBindingMap[roleId];
      if (!resourceId || (typeof resourceId !== 'string' && typeof resourceId !== 'number')) {
        return false;
      }

      const resource = bucket?.[roleId];
      if (!resource || typeof resource !== 'object') {
        return false;
      }

      const resourceTypeRef = (resource as { resourceTypeRef?: unknown }).resourceTypeRef;
      return String(resourceTypeRef) === String(CONSTANTS.SPECIALS.TYPE_Natural);
    };

    const boundCount = inputRoleIds.filter(isRoleBound).length;
    const totalCount = inputRoleIds.length;
    const allBound = boundCount === totalCount && totalCount > 0;

    // Only schedule auto-return if we just completed the last binding (transition from incomplete to complete).
    const prevEntry = prevBoundCountRef.current;
    const wasIncomplete = !prevEntry || prevEntry.stepId !== stepId || prevEntry.boundCount < totalCount;

    prevBoundCountRef.current = { stepId: stepId, boundCount };

    if (!allBound) {
      clearAutoReturn();
      return;
    }

    // Only schedule if this is a NEW completion (not already scheduled).
    if (!wasIncomplete) {
      // Already at "all bound" from previous render; don't re-schedule.
      return;
    }

    // Already scheduled for this step.
    if (autoReturnTimeoutRef.current?.stepId === stepId) return;

    clearAutoReturn();
    const timeoutId = setTimeout(() => {
      if (activatedWorkStepIdRef.current !== stepId) return;
      const cs = centerSelectionRef.current;
      if (!cs || cs.kind !== 'step' || cs.id !== stepId) return;

      // Deterministic return-home once all inputs are bound.
      void returnWorkStepHomeRef.current(stepId);
    }, 1000);

    autoReturnTimeoutRef.current = { stepId: stepId, timeoutId };

    return () => {
      // Clear on dependency change/unmount.
      if (autoReturnTimeoutRef.current?.stepId === stepId) {
        clearAutoReturn();
      }
    };
  }, [activatedWorkStepId, centerSelection, pendingInputBind, strategySteps, strategyState, jobMap]);

  const groupRef = useRef<THREE.Group>(null);

  // Refs keyed by domain identity (JOB-*)
  const jobRefs = useRef<Record<JobIdentityJson, React.RefObject<THREE.Group | null>>>(
    {} as Record<JobIdentityJson, React.RefObject<THREE.Group | null>>
  );

  const {
    handleJobSelect,
    cancelJobActivation,
    jobSlideTrace,
    resetJobSlideTrace,
    pushJobSlideTrace,
    animateJobHome,
    animateJobToCenter,
  } = useJobPanelAnimation<StepIdentityJson>({
    activatedJobId,
    setActivatedJobId,
    slidingJobId,
    setSlidingJobId,
    setHoveredJobId,
    setCenterSelection,
    jobRefs,
    jobPanelPosByJobId,
    toLocalOffsetForCenter: localOffsetToCenter,
  });

  /**
   * Load and run hardcoded strategy timeline for demo purposes
   * This mimics the legacy loadHardcodedStrategy() method
   */
  const loadAndRunHardcodedTimeline = async () => {
    try {
      // Stage 4 (runner) playback: slightly slower pacing for readability.
      // (>1 means slower; applied to segment durations only.)
      const runnerDurationScale = 1.35;

      // Stop any existing timeline
      if (timelineRunnerRef.current) {
        timelineRunnerRef.current.stop();
        timelineRunnerRef.current = null;
      }

      // Cancel any click-driven step/job animation + clear selection state.
      cancelJobActivation();
      setActivatedJobId(null);
      setSlidingJobId(null);
      setHoveredJobId(null);

      // Reset animation state
      setIsAnimating(false);
      activeAnimatingJobIdRef.current = null;
      setActiveAnimatingJobId(null);

      // Build timeline from strategy steps directly
      if (!strategySteps || strategySteps.length === 0) {
        console.warn('No strategy steps available for timeline animation');
        return;
      }

      // Build timeline segments for each step
      const segments: Array<{
        index: number;
        stepIndex: number;
        stepId: StepIdentityJson;
        phase: 'PAUSING' | 'PULLING_IN' | 'INSIDE' | 'PULLING_OUT';
        startMs: number;
        endMs: number;
        durationMs: number;
      }> = [];

      let currentTime = 0;
      let segmentIndex = 0;

      for (let i = 0; i < strategySteps.length; i++) {
        const step = strategySteps[i];
        const stepId = String(step.identity) as StepIdentityJson;

        // PAUSING phase
        const pauseDurationMs = PANEL_ANIMATION.PHASE_1_DURATION * 1000 * runnerDurationScale;
        segments.push({
          index: segmentIndex++,
          stepIndex: i,
          stepId: stepId,
          phase: 'PAUSING',
          startMs: currentTime,
          endMs: currentTime + pauseDurationMs,
          durationMs: pauseDurationMs,
        });
        currentTime += pauseDurationMs;

        // PULLING_IN phase
        const pullInDurationMs = PANEL_ANIMATION.PHASE_2_DURATION * 1000 * runnerDurationScale;
        segments.push({
          index: segmentIndex++,
          stepIndex: i,
          stepId: stepId,
          phase: 'PULLING_IN',
          startMs: currentTime,
          endMs: currentTime + pullInDurationMs,
          durationMs: pullInDurationMs,
        });
        currentTime += pullInDurationMs;

        // INSIDE phase
        const insideDurationMs = PANEL_ANIMATION.PHASE_1_DURATION * 1000 * runnerDurationScale;
        segments.push({
          index: segmentIndex++,
          stepIndex: i,
          stepId: stepId,
          phase: 'INSIDE',
          startMs: currentTime,
          endMs: currentTime + insideDurationMs,
          durationMs: insideDurationMs,
        });
        currentTime += insideDurationMs;

        // PULLING_OUT phase
        const pullOutDurationMs = PANEL_ANIMATION.PHASE_2_DURATION * 1000 * runnerDurationScale;
        segments.push({
          index: segmentIndex++,
          stepIndex: i,
          stepId: stepId,
          phase: 'PULLING_OUT',
          startMs: currentTime,
          endMs: currentTime + pullOutDurationMs,
          durationMs: pullOutDurationMs,
        });
        currentTime += pullOutDurationMs;
      }

      // Create a simple timeline runner
      const timeline = {
        segments,
        totalDurationMs: currentTime,
      };

      const runner = new TimelineRunner(timeline as never, {
        loop: false,
        loopBreakMs: 3000,
      });

      // Subscribe to animation ticks
      runner.onTick((tick) => {
        const seg = tick.segment as unknown as typeof segments[0];
        const stepIdentity = seg.stepId;
        const t = tick.t;

        // Update active step once per segment
        if (activeAnimatingJobIdRef.current !== (stepIdentity as never)) {
          activeAnimatingJobIdRef.current = stepIdentity as never;
          setActiveAnimatingJobId(stepIdentity as never);
        }

        const stepRef = workStepRefs.current[stepIdentity];
        if (!stepRef?.current) {
          console.warn('No mesh ref for step identity:', stepIdentity, 'Available refs:', Object.keys(workStepRefs.current));
          return;
        }

        applyTimelineAnimationRef.current?.(stepRef.current, stepIdentity, seg.phase, t);
      });

      runner.start();
      timelineRunnerRef.current = runner;
      setIsAnimating(true);

      // Stop on completion (TimelineRunner doesn't have onComplete, use timeout)
      setTimeout(() => {
        setIsAnimating(false);
        activeAnimatingJobIdRef.current = null;
        setActiveAnimatingJobId(null);
        setActivatedJobId(null);
        setSlidingJobId(null);
        setHoveredJobId(null);
      }, timeline.totalDurationMs);
    } catch (error) {
      console.error('Error loading hardcoded strategy:', error);
    }
  };

  const runDemoTimelineRef = useRef<() => void>(() => undefined);
  runDemoTimelineRef.current = () => {
    void loadAndRunHardcodedTimeline();
  };

  // If builder changes centerSelection externally (e.g. from another panel),
  // animate CosmosSpace panels to match.
  const prevCenterSelectionForSyncRef = useRef<typeof centerSelection>(null);
  useEffect(() => {
    if (!builder?.centerSelection || !builder?.setCenterSelection) return;
    if (isAnimating) return;

    const prev = prevCenterSelectionForSyncRef.current;
    prevCenterSelectionForSyncRef.current = centerSelection;

    // Ignore initial mount.
    if (prev === null && centerSelection === null) return;

    const sync = async () => {
      const next = centerSelection;
      if (!next) {
        // If cleared, return whatever was previously centered.
        if (prev?.kind === 'job') {
          await animateJobHome(prev.id, { speedMultiplier: 2 });
        }
        if (prev?.kind === 'step') {
          await animateWorkStepTo(prev.id, [0, 0, 0], { speedMultiplier: 2 });
        }
        return;
      }

      if (next.kind === 'job') {
        const nextJobId = next.id;
        if (prev?.kind === 'job' && prev.id === nextJobId) return;

        // Return previously centered entity home.
        if (prev?.kind === 'job' && prev.id !== nextJobId) {
          await animateJobHome(prev.id, { speedMultiplier: 2 });
        }
        if (prev?.kind === 'step') {
          await animateWorkStepTo(prev.id, [0, 0, 0], { speedMultiplier: 2 });
        }

        await animateJobToCenter(nextJobId);
        return;
      }

      // next.kind === 'step'
      const nextStepId = next.id;
      if (prev?.kind === 'step' && prev.id === nextStepId) return;

      // Return previously centered entity home.
      if (prev?.kind === 'job') {
        await animateJobHome(prev.id, { speedMultiplier: 2 });
      }
      if (prev?.kind === 'step' && prev.id !== nextStepId) {
        await animateWorkStepTo(prev.id, [0, 0, 0], { speedMultiplier: 2 });
      }

      // Only animate steps that exist on the step panel ring (non-work steps are TODO).
      const home = getWorkStepHomePosition(nextStepId);
      if (!home) return;

      setSlidingWorkStepId(nextStepId);
      await animateWorkStepTo(nextStepId, localOffsetToCenterForYaw(strategyStepPanelLayout.yaw, home));
      setSlidingWorkStepId(null);
    };

    void sync();
  }, [
    builder?.centerSelection,
    builder?.setCenterSelection,
    centerSelection,
    isAnimating,
    animateJobHome,
    animateJobToCenter,
    animateWorkStepTo,
    getWorkStepHomePosition,
    strategyStepPanelLayout.yaw,
  ]);


  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
      }}
    >
      <Canvas
        camera={{ position: initialCamera.position, fov: initialCamera.fov }}
        style={{
          width: '100%',
          height: '100%',
        }}
        onPointerMissed={() => {
          if (buildMenuEnabled) {
            setBuildMenuJobId(null);
            setPendingBuildMenuJobId(null);
          }
          handleJobSelect(null);
          // Don't deselect WorkStep on pointer-missed to allow role binding interactions
          // handleWorkStepSelect(null);
        }}
        onError={(error) => {
          console.error(' R3F Canvas error:', error);
        }}
      >
        <SceneLighting />

        {/* ResourceTypes (data-driven from Cosmos TYPE-ResourceType) */}
        <group name='types'>
          {typeRing.types.map((t) => {
            const position = typeRing.positions.get(t.id);
            if (!position) return null;

            const sphereRadius = 0.5;
            const cubeSize = sphereRadius * 2;
            // True insphere: sphere fits inside the cube (radius == cubeSize / 2)
            const wireSphereRadius = cubeSize / 2;

            return (
              <group key={t.id} position={position}>
                {/* Genesis-inspired "insphere": cube edges + faint wireframe sphere */}
                <mesh>
                  <boxGeometry args={[cubeSize, cubeSize, cubeSize]} />
                  <meshBasicMaterial visible={false} />
                  <Edges color={'black'} opacity={1} transparent={true} />
                </mesh>
                <mesh>
                  <sphereGeometry args={[wireSphereRadius, 32, 32]} />
                  <meshBasicMaterial
                    color={'black'}
                    wireframe={true}
                    transparent={true}
                    opacity={0.2}
                  />
                </mesh>
                <Html distanceFactor={10} position={[0, 1.1, 0]} center>
                  <h1
                    style={{
                      padding: '1px 4px',
                      backgroundColor: 'white',
                      borderRadius: '20%',
                      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                    }}
                  >
                    {t.id === String(CONSTANTS.SPECIALS.TYPE_StatefulStrategy)
                      ? statefulStrategyDisplayName
                      : t.name}
                  </h1>
                </Html>
              </group>
            );
          })}
        </group>

        <group ref={groupRef} name='jobs'>
          {jobPanelLayout.items.map(({ job, position }) => {
            const jobId = job.identity;

            if (!jobRefs.current[jobId]) {
              jobRefs.current[jobId] = React.createRef<THREE.Group | null>();
            }

            const label = String(job.name ?? jobId);
            const jobIdForBuilder = jobId;
            const isActive =
              hoveredJobId === jobId ||
              activatedJobId === jobId ||
              slidingJobId === jobId;

            return (
              <group
                key={job.identity}
                name={`job-home-${label}`}
                position={position}
                rotation={[0, jobPanelLayout.yaw, 0]}
              >
                {/* Trace behind the sliding job only (semi-transparent ghost planes). */}
                {slidingJobId === jobId && jobSlideTrace.length > 1 ? (
                  <SlideTrace
                    trace={jobSlideTrace}
                    name={`job-trace-${label}`}
                  />
                ) : null}

                {/* Inner group is animated (offset from home). */}
                <group ref={jobRefs.current[jobId]} name={`job-group-${label}`}>
                  <Plane args={[1.4, 0.9]} rotation={[-Math.PI / 2, 0, 0]}>
                    <meshStandardMaterial
                      color={isActive ? '#FFFF00' : '#ff6b6b'}
                      emissive={isActive ? '#FFFF00' : '#000000'}
                      emissiveIntensity={isActive ? 3.0 : 0}
                      toneMapped={!isActive}
                      roughness={isActive ? 0.15 : 1}
                    />
                  </Plane>
                  <Html distanceFactor={10} position={[0, 0.35, 0]} center>
                    <h1
                      style={{
                        padding: '1px 4px',
                        backgroundColor: 'white',
                        borderRadius: '20%',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                    >
                      {label}
                    </h1>
                  </Html>

                  {/* Click/hover target */}
                  <mesh
                    position={[0, 0.01, 0]}
                    rotation={[-Math.PI / 2, 0, 0]}
                    onPointerOver={(e) => {
                      e.stopPropagation();
                      setHoveredJobId(jobId);
                    }}
                    onPointerOut={(e) => {
                      e.stopPropagation();
                      setHoveredJobId((prev) => (prev === jobId ? null : prev));
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      // If a WorkStep is active, send it back before activating a Job.
                      if (activatedWorkStepId) return;
                      handleJobSelect(jobId);

                      if (buildMenuEnabled) {
                        // Only allow the menu to appear once the job has reached center.
                        if (activatedJobId === jobId && slidingJobId !== jobId) {
                          setBuildMenuJobId((prev) => (prev === jobId ? null : jobId));
                          setPendingBuildMenuJobId(null);
                        } else {
                          // Defer menu open until activation completes.
                          setPendingBuildMenuJobId(jobId);
                          setBuildMenuJobId(null);
                        }
                      }
                    }}
                  >
                    <planeGeometry args={[1.6, 1.1]} />
                    <meshBasicMaterial transparent opacity={0} />
                  </mesh>

                  {buildMenuEnabled && buildMenuJobId === jobId && activatedJobId === jobId && slidingJobId !== jobId ? (
                    <Html distanceFactor={10} position={[0, 0.35, 0]} center style={{ pointerEvents: 'none' }}>
                      <div
                        onMouseDown={(e) => e.stopPropagation()}
                        onClick={(e) => e.stopPropagation()}
                        style={{
                          transform: 'translateY(118px)',
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 6,
                          padding: 10,
                          background: 'rgba(255,255,255,0.96)',
                          border: '1px solid rgba(0,0,0,0.15)',
                          borderRadius: 10,
                          boxShadow: '0 10px 25px rgba(0,0,0,0.18)',
                          minWidth: 140,
                          pointerEvents: 'auto',
                        }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: 10 }}>
                          <div style={{ fontSize: 12, fontWeight: 700, color: '#111' }}>Build Step</div>
                          <button
                            type='button'
                            title='Close'
                            style={{
                              fontSize: 12,
                              width: 22,
                              height: 22,
                              lineHeight: '22px',
                              textAlign: 'center',
                              borderRadius: 999,
                              border: '1px solid rgba(0,0,0,0.18)',
                              background: '#ffffff',
                              cursor: 'pointer',
                            }}
                            onClick={() => {
                              // Close the menu and send the job back home.
                              setBuildMenuJobId(null);
                              setPendingBuildMenuJobId(null);
                              handleJobSelect(jobId);
                            }}
                          >
                            ×
                          </button>
                        </div>

                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 6 }}>
                          <button
                            type='button'
                            style={{
                              fontSize: 12,
                              padding: '6px 8px',
                              borderRadius: 8,
                              border: '1px solid rgba(0,0,0,0.15)',
                              background: '#f3f4f6',
                              cursor: 'pointer',
                            }}
                            onClick={async () => {
                              setBuildMenuJobId(null);
                              setPendingBuildMenuJobId(null);
                              try {
                                await onBuildWorkStep?.(jobIdForBuilder);
                              } finally {
                                // After creating the step, send the job back home.
                                handleJobSelect(jobId);
                              }
                            }}
                          >
                            Work
                          </button>

                          <button
                            type='button'
                            style={{
                              fontSize: 12,
                              padding: '6px 8px',
                              borderRadius: 8,
                              border: '1px solid rgba(0,0,0,0.15)',
                              background: '#f3f4f6',
                              cursor: 'pointer',
                            }}
                            onClick={async () => {
                              setBuildMenuJobId(null);
                              await onBuildBranchStep?.([jobIdForBuilder]);
                            }}
                          >
                            Branch
                          </button>

                          <button
                            type='button'
                            style={{
                              fontSize: 12,
                              padding: '6px 8px',
                              borderRadius: 8,
                              border: '1px solid rgba(0,0,0,0.15)',
                              background: '#f3f4f6',
                              cursor: 'pointer',
                            }}
                            onClick={async () => {
                              setBuildMenuJobId(null);
                              await onBuildForStep?.(jobIdForBuilder);
                            }}
                          >
                            For
                          </button>

                          <button
                            type='button'
                            style={{
                              fontSize: 12,
                              padding: '6px 8px',
                              borderRadius: 8,
                              border: '1px solid rgba(0,0,0,0.15)',
                              background: '#f3f4f6',
                              cursor: 'pointer',
                            }}
                            onClick={async () => {
                              setBuildMenuJobId(null);
                              await onBuildWhileStep?.(jobIdForBuilder);
                            }}
                          >
                            While
                          </button>
                        </div>
                      </div>
                    </Html>
                  ) : null}
                </group>
              </group>
            );
          })}
        </group>

        {/* Natural resources: extend outward from TYPE-Natural at the same angle */}
        {(() => {
          const naturalTypeId = String(CONSTANTS.SPECIALS.TYPE_Natural);
          const naturalTypePos = typeRing.positions.get(naturalTypeId);
          if (!naturalTypePos || naturalPanelLayout.items.length === 0) return null;

          const baseDir = new THREE.Vector3(naturalTypePos[0], 0, naturalTypePos[2]);
          if (baseDir.lengthSq() < 1e-6) baseDir.set(0, 0, -1);
          baseDir.normalize();

          const yaw = Math.atan2(baseDir.z, baseDir.x);

          const sheetWidth = 1.4;
          const sheetDepth = 0.9;

          const showBindInvitationOutline =
            Boolean(pendingInputBind) &&
            centerSelection?.kind === 'step' &&
            String(activatedWorkStepId ?? '') === String(pendingInputBind?.stepId ?? '');

          return (
            <group name='naturals'>
              {naturalPanelLayout.items.map(({ resource: r, position }) => {
                const { label } = getNaturalOrdinalAndLabel(r, 0);
                const isHovered = hoveredNaturalId === String(r.identity);
                return (
                  <group key={r.identity} position={position} rotation={[0, yaw, 0]}>
                    <group scale={isHovered ? 1.04 : 1}>
                      <Plane args={[sheetWidth, sheetDepth]} rotation={[-Math.PI / 2, 0, 0]}>
                        <meshStandardMaterial color={isHovered ? '#ffd166' : '#ff6b6b'} />
                        {showBindInvitationOutline ? <Edges color='black' /> : null}
                      </Plane>
                    </group>

                    <Html
                      distanceFactor={10}
                      position={[0, 0.35, 0]}
                      center
                      style={{ pointerEvents: 'none' }}
                    >
                      <h1
                        style={{
                          padding: '1px 4px',
                          backgroundColor: 'white',
                          borderRadius: '20%',
                          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        }}
                      >
                        {label}
                      </h1>
                    </Html>

                    {/* Click target for binding */}
                    <mesh
                      position={[0, 0.01, 0]}
                      rotation={[-Math.PI / 2, 0, 0]}
                      onPointerOver={(e) => {
                        e.stopPropagation();
                        setHoveredNaturalId(String(r.identity));
                      }}
                      onPointerOut={(e) => {
                        e.stopPropagation();
                        setHoveredNaturalId((prev) => (prev === String(r.identity) ? null : prev));
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        void handleNaturalClick(r);
                      }}
                    >
                      <planeGeometry args={[1.6, 1.1]} />
                      <meshBasicMaterial transparent opacity={0} />
                    </mesh>
                  </group>
                );
              })}
            </group>
          );
        })()}

        {/* Strategy steps: extend outward from TYPE-StatefulStrategy at the same angle */}
        {(() => {
          if (!strategyStepPanelLayout.items.length) return null;

          const sheetWidth = 1.4;
          const sheetDepth = 0.9;

          const getWorkJobRef = (s: StepJson): string | null => {
            if ((s as StepJson).kind !== 'work') return null;
            const exec = (s as WorkStepJson).execution as ExecutionJson | undefined;
            const jobRef = exec?.jobRef;
            return typeof jobRef === 'string' && jobRef.length > 0 ? jobRef : null;
          };

          return (
            <group name='strategy-steps'>
              {/* Draw links in world space (not inside rotated/translated panel groups). */}
              {strategyStepPanelLayout.items.map(({ step: s, position, index }) => {
                const jobRef = getWorkJobRef(s);
                const lineTo = jobRef ? jobPanelPosByJobId.get(jobRef as JobIdentityJson) : undefined;
                if (!lineTo) return null;

                const stepKind = String((s as StepJson).kind ?? 'step');
                const stepId = String((s as StepJson).identity ?? `${stepKind}-${index}`) as StepIdentityJson;
                const isRunnerMode = mode === 'runner';
                const isRunnerHighlightActive =
                  isRunnerMode &&
                  isAnimating &&
                  activeAnimatingJobId != null &&
                  String(activeAnimatingJobId) === String(stepId);

                // Stage 4 (runner): start with all step→job links dimmed.
                // While animating, only the current step’s link is full opacity.
                const linkOpacity = isRunnerMode ? (isRunnerHighlightActive ? 1.0 : 0.15) : 1.0;

                const linkColor = isRunnerMode
                  ? (isRunnerHighlightActive ? '#000000' : '#777777')
                  : '#000000';

                return (
                  <Line
                    key={`step-link-${(s as StepJson).identity ?? index}`}
                    points={[
                      [position[0], 0.02, position[2]],
                      [lineTo[0], 0.02, lineTo[2]],
                    ]}
                    lineWidth={1}
                    color={linkColor}
                    transparent
                    opacity={linkOpacity}
                    depthWrite={false}
                    // Purely visual; don't steal clicks.
                    raycast={() => null}
                  />
                );
              })}

              {strategyStepPanelLayout.items.map(({ step: s, position, index }) => {
                const stepKind = String((s as StepJson).kind ?? 'step');
                const stepId = String((s as StepJson).identity ?? `${stepKind}-${index}`) as StepIdentityJson;

                if (!workStepRefs.current[stepId]) {
                  workStepRefs.current[stepId] = React.createRef<THREE.Group | null>();
                }

                const jobRef = getWorkJobRef(s);
                const jobName = jobRef ? jobNameByJobId.get(jobRef as JobIdentityJson) : undefined;
                const label =
                  stepKind === 'work'
                    ? `Work ${index + 1}${jobName ? `: ${jobName}` : jobRef ? `: ${jobRef}` : ''}`
                    : `${stepKind} ${index + 1}`;

                const isActivated =
                  activatedWorkStepId === stepId || slidingWorkStepId === stepId;
                const isSelected = selectedStepId === stepId && !isActivated;
                const isHovered = hoveredWorkStepId === stepId && !isActivated && !isSelected;

                // Color hierarchy: activated (yellow) > selected (cyan) > hovered (light purple) > normal (purple)
                let color = '#8b5cf6'; // normal purple
                let emissive = '#000000';
                let emissiveIntensity = 0;
                let roughness = 1;
                let toneMapped = true;

                if (isActivated) {
                  color = '#FFFF00';
                  emissive = '#FFFF00';
                  emissiveIntensity = 2.0;
                  roughness = 0.15;
                  toneMapped = false;
                } else if (isSelected) {
                  color = '#06b6d4'; // cyan
                  emissive = '#06b6d4';
                  emissiveIntensity = 0.6;
                  roughness = 0.4;
                  toneMapped = false;
                } else if (isHovered) {
                  color = '#a78bfa'; // light purple
                  emissiveIntensity = 0.2;
                }

                return (
                  <group
                    key={stepId}
                    name={`step-home-${stepId}`}
                    position={position}
                    rotation={[0, strategyStepPanelLayout.yaw, 0]}
                  >
                    {/* Inner group is animated (offset from home). */}
                    <group ref={workStepRefs.current[stepId]} name={`step-group-${stepId}`}>
                      <Plane args={[sheetWidth, sheetDepth]} rotation={[-Math.PI / 2, 0, 0]}>
                        <meshStandardMaterial
                          color={color}
                          emissive={emissive}
                          emissiveIntensity={emissiveIntensity}
                          toneMapped={toneMapped}
                          roughness={roughness}
                        />
                      </Plane>

                      <Html distanceFactor={10} position={[0, 0.35, 0]} center>
                        <h1
                          style={{
                            padding: '1px 4px',
                            backgroundColor: 'white',
                            borderRadius: '20%',
                            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                          }}
                        >
                          {label}
                        </h1>
                      </Html>

                      {/* Click/hover target */}
                      <mesh
                        position={[0, 0.01, 0]}
                        rotation={[-Math.PI / 2, 0, 0]}
                        onPointerOver={(e) => {
                          e.stopPropagation();
                          setHoveredWorkStepId(stepId);
                        }}
                        onPointerOut={(e) => {
                          e.stopPropagation();
                          setHoveredWorkStepId((prev) => (prev === stepId ? null : prev));
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          // Left click: activate/center the step (if no step is currently activated)
                          // If a step is already centered, just select this one
                          if (!activatedWorkStepId) {
                            handleWorkStepSelect(stepId);
                          }

                          // Selection/highlighting: prefer builder-provided selection callbacks when available.
                          if (builder?.activeStep !== undefined && onSelectStep && onClearSelection) {
                            if (String((s as StepJson).kind) !== 'work') {
                              // TODO: wire Branch/While/For case selection (SelectedIndex.caseIndex)
                              console.warn('CosmosSpace selection for non-work steps not yet implemented', {
                                stepKind: String((s as StepJson).kind),
                                stepId,
                              });
                              return;
                            }

                            if (selectedStepId === stepId) {
                              onClearSelection();
                            } else {
                              onSelectStep({ stepIndex: index, caseIndex: null });
                            }
                            return;
                          }

                          setSelectedStepIdLocal((prev) => (prev === stepId ? null : stepId));
                        }}
                      >
                        <planeGeometry args={[1.6, 1.1]} />
                        <meshBasicMaterial transparent opacity={0} />
                      </mesh>
                    </group>
                  </group>
                );
              })}

              {/* Step slide trace - yellow trail during animation */}
              {activeAnimatingStepId && stepSlideTrace.length > 1 ? (
                <SlideTrace
                  trace={stepSlideTrace}
                  name='step-slide-trace'
                  homePosition={getWorkStepHomePosition(activeAnimatingStepId) ?? undefined}
                  yawRotation={strategyStepPanelLayout.yaw}
                />
              ) : null}
            </group>
          );
        })()}

        <Torus
          name='base'
          scale={1.5}
          args={[5.6, 0.01, 16, 100]}
          position={[0, 0, 0]}
          rotation={[Math.PI / 2, 0, 0]}
        >
          <meshStandardMaterial color='#ff6b6b' />
        </Torus>

        {/* Secondary role ring for selected (non-activated) step */}
        {(() => {
          if (!selectedStepId || activatedWorkStepId === selectedStepId) return null;

          const step = (strategySteps ?? []).find((s) => String((s as StepJson).identity) === selectedStepId) as StepJson | undefined;
          if (!step || String((step as StepJson).kind) !== 'work') return null;

          const exec = (step as WorkStepJson).execution as ExecutionJson | undefined;
          const jobRef = exec?.jobRef;
          if (!exec || typeof jobRef !== 'string' || !jobRef) return null;

          const jobData = jobMap.get(jobRef as JobIdentityJson);
          if (!jobData) return null;

          type RoleEntry = {
            id: string;
            name: string;
            kind: 'input' | 'output';
            resourceTypeId: string;
          };

          const rolesByType = new Map<string, RoleEntry[]>();
          const inputMap = jobData?.roles?.inputMap ?? {};
          const outputMap = jobData?.roles?.outputMap ?? {};

          for (const [roleId, info] of Object.entries(inputMap)) {
            const resourceTypeId = String(info?.resourceTypeRef ?? '');
            if (!resourceTypeId) continue;
            const arr = rolesByType.get(resourceTypeId) ?? [];
            arr.push({ id: roleId, name: String(info?.name ?? roleId), kind: 'input', resourceTypeId });
            rolesByType.set(resourceTypeId, arr);
          }
          for (const [roleId, info] of Object.entries(outputMap)) {
            const resourceTypeId = String(info?.resourceTypeRef ?? '');
            if (!resourceTypeId) continue;
            const arr = rolesByType.get(resourceTypeId) ?? [];
            arr.push({ id: roleId, name: String(info?.name ?? roleId), kind: 'output', resourceTypeId });
            rolesByType.set(resourceTypeId, arr);
          }

          if (rolesByType.size === 0) return null;

          const up = new THREE.Vector3(0, 1, 0);
          const roleRingRadius = 1.1;

          // Position the secondary ring inside the main role ring (closer to origin)
          // TYPE-Natural is at typeRingRadius, main role ring orbits at typeRingRadius
          const secondaryRingOffset = typeRingRadius - 0.6; // Inside, closer to origin

          return Array.from(rolesByType.entries()).flatMap(([resourceTypeId, entries]) => {
            const typeCenter = typeRing.positions.get(resourceTypeId);
            if (!typeCenter) return [];

            // Static position: place ring at fixed distance from origin toward TYPE-Natural
            const radial = new THREE.Vector3(typeCenter[0], 0, typeCenter[2]);
            if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
            radial.normalize();

            const center = radial.clone().multiplyScalar(secondaryRingOffset);
            center.y = typeCenter[1];

            const tangent = new THREE.Vector3().crossVectors(up, radial).normalize();

            const guidePoints: [number, number, number][] = Array.from({ length: 65 }, (_, i) => {
              const theta = (i / 64) * Math.PI * 2;
              const x = Math.cos(theta) * roleRingRadius;
              const y = Math.sin(theta) * roleRingRadius;
              const p = new THREE.Vector3()
                .copy(center)
                .add(tangent.clone().multiplyScalar(x))
                .add(up.clone().multiplyScalar(y));
              return [p.x, p.y, p.z];
            });

            const inputs = entries.filter((e) => e.kind === 'input').sort((a, b) => a.name.localeCompare(b.name));
            const outputs = entries.filter((e) => e.kind === 'output').sort((a, b) => a.name.localeCompare(b.name));

            const makeRolePos = (i: number, count: number, kind: 'input' | 'output'): [number, number, number] => {
              const denom = Math.max(count, 1);
              const theta = ((i + 0.5) / denom) * Math.PI;
              const x = Math.cos(theta) * roleRingRadius;
              const y = Math.sin(theta) * roleRingRadius * (kind === 'input' ? 1 : -1);
              const p = new THREE.Vector3()
                .copy(center)
                .add(tangent.clone().multiplyScalar(x))
                .add(up.clone().multiplyScalar(y));
              return [p.x, p.y, p.z];
            };

            // Check for bound roles
            const execId = String(exec.identity);
            const bucket = (strategyState as StrategyStateJson)?.[execId as keyof StrategyStateJson] as Record<string, unknown> | undefined;

            return [
              <Line
                key={`secondary-${selectedStepId}-${resourceTypeId}-ring-guide`}
                points={guidePoints}
                lineWidth={1}
                color='#06b6d4'
                opacity={0.5}
                transparent
                raycast={() => null}
              />,
              ...[...inputs, ...outputs].map((role) => {
                const isInput = role.kind === 'input';
                const roleIndex = isInput
                  ? inputs.findIndex((r) => r.id === role.id)
                  : outputs.findIndex((r) => r.id === role.id);
                const roleCount = isInput ? inputs.length : outputs.length;
                const rolePosition = makeRolePos(roleIndex, roleCount, role.kind);

                let isBound = false;
                if (isInput) {
                  const entry = bucket?.[role.id];
                  if (entry && typeof entry === 'object') {
                    const rt = (entry as { resourceTypeRef?: unknown }).resourceTypeRef;
                    isBound = String(rt) === String(CONSTANTS.SPECIALS.TYPE_Natural);
                  }
                }

                const canBindHere = Boolean(
                  pendingInputBind &&
                  centerSelection?.kind === 'step' &&
                  centerSelection.id === activatedWorkStepId
                );

                const handleRoleClick = async () => {
                  // TODO: Implement role-to-role binding using BindingsContext
                  console.warn('Role-to-role binding not yet implemented in CosmosSpace', {
                    execId,
                    targetRole: role.id,
                    pendingInputBind
                  });
                  setPendingInputBind(null);
                };

                return (
                  <group key={`secondary-${selectedStepId}-${resourceTypeId}-${role.id}`}>
                    <Sphere
                      position={rolePosition}
                      args={[0.18, 16, 16]}
                      onClick={canBindHere ? handleRoleClick : undefined}
                      onPointerOver={canBindHere ? () => document.body.style.cursor = 'pointer' : undefined}
                      onPointerOut={canBindHere ? () => document.body.style.cursor = 'auto' : undefined}
                    >
                      {isBound ? (
                        <meshStandardMaterial
                          color={isInput ? '#545757' : '#3d3259'}
                          emissive={isInput ? '#545757' : '#3d3259'}
                          emissiveIntensity={canBindHere ? 0.6 : 0.3}
                          roughness={0.3}
                          transparent
                          opacity={canBindHere ? 1.0 : 0.7}
                        />
                      ) : (
                        <>
                          <meshBasicMaterial transparent opacity={0.01} depthWrite={false} colorWrite={false} />
                          <Edges color={isInput ? '#545757' : '#3d3259'} opacity={canBindHere ? 1.0 : 0.5} transparent />
                        </>
                      )}
                    </Sphere>
                  </group>
                );
              }),
            ];
          });
        })()}

        {(() => {
          // Roles/connectors can be shown either for an activated Job, or for an activated WorkStep.
          const activeMode: 'job' | 'work-step' | null = activatedWorkStepId
            ? 'work-step'
            : activatedJobId
              ? 'job'
              : null;
          
          console.log('[CosmosSpace] Role rendering check:', {
            activeMode,
            activatedWorkStepId,
            activatedJobId,
            hasStrategySteps: Boolean(strategySteps && strategySteps.length > 0),
            mode,
          });
          
          if (!activeMode) return null;

          const roleSourceKey = activeMode === 'work-step'
            ? `workstep:${activatedWorkStepId}`
            : `job:${activatedJobId}`;

          const jobPosition = [0, 0, 0] as [number, number, number];

          let jobData: JobJson | null = null;
          let connectorStartGroupRef: React.RefObject<THREE.Group | null> | null = null;

          if (activeMode === 'job') {
            const activeJobId = activatedJobId;
            if (!activeJobId) return null;
            jobData = jobMap.get(activeJobId) ?? null;
            const jobEnvelope = jobEnvelopeByJobId.get(activeJobId);
            if (!jobEnvelope) return null;
            connectorStartGroupRef = jobRefs.current[activeJobId] ?? null;
          } else {
            const stepId = activatedWorkStepId as string;
            const step = (strategySteps ?? []).find((s) => String((s as StepJson).identity) === stepId) as StepJson | undefined;
            console.log('[CosmosSpace] Looking for step:', {
              stepId,
              foundStep: Boolean(step),
              stepKind: step ? String((step as StepJson).kind) : 'N/A',
              totalSteps: strategySteps?.length ?? 0,
            });
            if (!step) return null;
            if (String((step as StepJson).kind) !== 'work') return null;
            const exec = (step as WorkStepJson).execution as ExecutionJson | undefined;
            const jobRef = exec?.jobRef;
            console.log('[CosmosSpace] Step execution:', {
              hasExec: Boolean(exec),
              jobRef,
              hasJobData: jobRef ? jobMap.has(jobRef as JobIdentityJson) : false,
            });
            if (typeof jobRef !== 'string' || !jobRef) return null;
            jobData = jobMap.get(jobRef as JobIdentityJson) ?? null;
            connectorStartGroupRef = workStepRefs.current[stepId] ?? null;
          }

          if (!jobData) return null;

          type RoleEntry = {
            id: string;
            name: string;
            description: string;
            kind: 'input' | 'output';
            resourceTypeId: string;
          };

          const rolesByType = new Map<string, RoleEntry[]>();
          const inputMap = jobData?.roles?.inputMap ?? {};
          const outputMap = jobData?.roles?.outputMap ?? {};

          for (const [roleId, info] of Object.entries(inputMap)) {
            const resourceTypeId = String(info?.resourceTypeRef ?? '');
            if (!resourceTypeId) continue;
            const arr = rolesByType.get(resourceTypeId) ?? [];
            arr.push({
              id: roleId,
              name: String(info?.name ?? roleId),
              description: String(info?.description ?? ''),
              kind: 'input',
              resourceTypeId,
            });
            rolesByType.set(resourceTypeId, arr);
          }
          for (const [roleId, info] of Object.entries(outputMap)) {
            const resourceTypeId = String(info?.resourceTypeRef ?? '');
            if (!resourceTypeId) continue;
            const arr = rolesByType.get(resourceTypeId) ?? [];
            arr.push({
              id: roleId,
              name: String(info?.name ?? roleId),
              description: String(info?.description ?? ''),
              kind: 'output',
              resourceTypeId,
            });
            rolesByType.set(resourceTypeId, arr);
          }

          if (rolesByType.size === 0) return null;

          const up = new THREE.Vector3(0, 1, 0);
          const roleRingRadius = 1.6;

          const activeWorkStepId = activatedWorkStepId;
          const activeWorkStep = activeMode === 'work-step'
            ? (strategySteps ?? []).find((s) => String((s as StepJson).identity) === String(activeWorkStepId))
            : undefined;
          const activeWorkExecution = activeWorkStep && String((activeWorkStep as StepJson).kind) === 'work'
            ? ((activeWorkStep as WorkStepJson).execution as ExecutionJson | undefined)
            : undefined;

          const boundCurves: Array<{
            key: string;
            points: [number, number, number][];
            color: string;
          }> = [];

          return Array.from(rolesByType.entries()).flatMap(([resourceTypeId, entries]) => {
            const typeCenter = typeRing.positions.get(resourceTypeId);
            if (!typeCenter) return [];

            const center = new THREE.Vector3(typeCenter[0], typeCenter[1], typeCenter[2]);
            const radial = new THREE.Vector3(center.x, 0, center.z);
            if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
            radial.normalize();
            const tangent = new THREE.Vector3().crossVectors(up, radial).normalize();

            const guidePoints: [number, number, number][] = Array.from({ length: 65 }, (_, i) => {
              const theta = (i / 64) * Math.PI * 2;
              const x = Math.cos(theta) * roleRingRadius;
              const y = Math.sin(theta) * roleRingRadius;
              const p = new THREE.Vector3()
                .copy(center)
                .add(tangent.clone().multiplyScalar(x))
                .add(up.clone().multiplyScalar(y));
              return [p.x, p.y, p.z];
            });

            const inputs = entries
              .filter((e) => e.kind === 'input')
              .sort((a, b) => a.name.localeCompare(b.name));
            const outputs = entries
              .filter((e) => e.kind === 'output')
              .sort((a, b) => a.name.localeCompare(b.name));

            const makeRolePos = (
              i: number,
              count: number,
              kind: 'input' | 'output'
            ): [number, number, number] => {
              const denom = Math.max(count, 1);
              const theta = ((i + 0.5) / denom) * Math.PI;
              const x = Math.cos(theta) * roleRingRadius;
              const y = Math.sin(theta) * roleRingRadius * (kind === 'input' ? 1 : -1);
              const p = new THREE.Vector3()
                .copy(center)
                .add(tangent.clone().multiplyScalar(x))
                .add(up.clone().multiplyScalar(y));
              return [p.x, p.y, p.z];
            };

            return [
              <Line
                key={`${roleSourceKey}-${resourceTypeId}-role-ring-guide`}
                points={guidePoints}
                lineWidth={1}
                color='black'
                // This is purely visual; it sits exactly under the role spheres, so disable hit-testing.
                raycast={() => null}
              />,
              ...[...inputs, ...outputs].map((role) => {
                const isInput = role.kind === 'input';
                const roleIndex = isInput
                  ? inputs.findIndex((r) => r.id === role.id)
                  : outputs.findIndex((r) => r.id === role.id);
                const roleCount = isInput ? inputs.length : outputs.length;
                const rolePosition = makeRolePos(roleIndex, roleCount, role.kind);

                const canBindHere =
                  isInput &&
                  activeMode === 'work-step' &&
                  centerSelection?.kind === 'step' &&
                  centerSelection?.id === String(activatedWorkStepId ?? '') &&
                  typeof activatedWorkStepId === 'string' &&
                  activatedWorkStepId.length > 0;

                const isSelectedForBind =
                  Boolean(pendingInputBind) &&
                  pendingInputBind?.stepId === String(activatedWorkStepId ?? '') &&
                  pendingInputBind?.inputRoleId === role.id;

                let isBoundToNatural = false;
                let boundNaturalId: string | null = null;
                if (isInput && activeMode === 'work-step' && activeWorkExecution) {
                  const execId = String(activeWorkExecution.identity);
                  const bucket = (strategyState as StrategyStateJson)?.[execId as keyof StrategyStateJson] as Record<string, unknown> | undefined;
                  const entry = bucket?.[role.id];
                  if (entry && typeof entry === 'object') {
                    const rt = (entry as { resourceTypeRef?: unknown }).resourceTypeRef;
                    if (String(rt) === String(CONSTANTS.SPECIALS.TYPE_Natural)) {
                      isBoundToNatural = true;
                      boundNaturalId = String((entry as { identity?: unknown }).identity ?? '');
                    }
                  }
                }

                if (isBoundToNatural && boundNaturalId) {
                  const endPos = naturalPanelPosByResourceId.get(boundNaturalId);
                  if (endPos) {
                    const start = new THREE.Vector3(rolePosition[0], rolePosition[1] + 0.02, rolePosition[2]);
                    const end = new THREE.Vector3(endPos[0], 0.02, endPos[2]);
                    const mid = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.5);
                    const ctrl = mid.clone().add(new THREE.Vector3(0, 1.1, 0));
                    const pts: [number, number, number][] = [];
                    const n = 22;
                    for (let i = 0; i <= n; i++) {
                      const t = i / n;
                      const a = start.clone().multiplyScalar((1 - t) * (1 - t));
                      const b = ctrl.clone().multiplyScalar(2 * (1 - t) * t);
                      const c = end.clone().multiplyScalar(t * t);
                      const p = a.add(b).add(c);
                      pts.push([p.x, p.y, p.z]);
                    }
                    boundCurves.push({
                      key: `${roleSourceKey}-${resourceTypeId}-${role.id}-bound-curve`,
                      points: pts,
                      color: '#00cc66',
                    });
                  }
                }

                return (
                  <RoleSphereWithLabel
                    key={`${roleSourceKey}-${resourceTypeId}-${role.id}`}
                    position={rolePosition}
                    roleName={role.name}
                    roleType={isInput ? 'Input' : 'Output'}
                    color={isInput ? '#545757' : '#3d3259'}
                    connectorColor={isInput ? '#00cc66' : '#cc0033'}
                    groupRef={groupRef}
                    jobGroupRef={connectorStartGroupRef}
                    isAnimating={isAnimating}
                    jobPosition={jobPosition}
                    isFilled={isInput && isBoundToNatural}
                    isSelected={isSelectedForBind}
                    canBind={canBindHere}
                    onRoleClick={
                      canBindHere
                        ? () => {
                          if (!activatedWorkStepId) return;

                          // TODO: Implement unbinding using BindingsContext.onClearInputBinding
                          if (isInput && isBoundToNatural) {
                            console.warn('Role unbinding not yet implemented in CosmosSpace', {
                              activatedWorkStepId,
                              roleId: role.id
                            });
                            return;
                          }

                          setPendingInputBind((prev) => {
                            const next = { stepId: String(activatedWorkStepId) as StepIdentityJson, inputRoleId: role.id };
                            if (prev && prev.stepId === next.stepId && prev.inputRoleId === next.inputRoleId) {
                              return null;
                            }
                            return next;
                          });
                        }
                        : undefined
                    }
                  />
                );
              })
              ,
              ...boundCurves.map((c) => (
                <Line
                  key={c.key}
                  points={c.points}
                  lineWidth={2}
                  color={c.color}
                  // Purely visual; don't steal clicks from Naturals.
                  raycast={() => null}
                  onPointerDown={(e) => {
                    e.stopPropagation();
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                />
              )),
            ];
          });
        })()}

        <Grid
          infiniteGrid
          fadeDistance={40}
          position={[0, -2, 0]}
          args={[10, 10]}
          cellSize={1}
          cellThickness={0.5}
          cellColor='#4ecdc4'
        />

        <mesh
          // Invisible ground-plane click catcher. Keep it horizontal so it can't block clicks on far-side objects.
          position={[0, -1.9, 0]}
          rotation={[-Math.PI / 2, 0, 0]}
          onClick={(e) => {
            e.stopPropagation();
            handleJobSelect(null);
            // Don't deselect WorkStep on background click to allow role binding interactions
            // handleWorkStepSelect(null);
            setPendingInputBind(null);
          }}
        >
          <planeGeometry args={[100, 100]} />
          <meshBasicMaterial transparent opacity={0} />
        </mesh>

        <OrbitControls
          target={[0, 0, 0]}
          minPolarAngle={0}
          maxPolarAngle={Math.PI / 2}
          minAzimuthAngle={-Math.PI}
          maxAzimuthAngle={Math.PI}
          minDistance={5}
          maxDistance={32}
          enableDamping
          dampingFactor={0.05}
        />
      </Canvas>
    </div>
  );
});

export default CosmosSpace;


// Lighting component
function SceneLighting() {
  return (
    <>
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <directionalLight
        position={[-10, -10, -5]}
        intensity={0.5}
        color='#4ecdc4'
      />
      <pointLight position={[0, 5, 0]} intensity={0.5} color='#ff6b6b' />
    </>
  );
}

const RoleSphereWithLabel = ({
  position,
  roleName,
  roleType,
  color,
  connectorColor,
  groupRef,
  jobGroupRef,
  isAnimating,
  jobPosition, // Add the starting job position
  isFilled = false,
  isSelected = false,
  canBind = false,
  onRoleClick,
}: {
  position: [number, number, number];
  roleName: string;
  roleType: string;
  color: string;
  connectorColor: string;
  groupRef: React.RefObject<THREE.Group | null>;
  jobGroupRef?: React.RefObject<THREE.Group | null> | null;
  isAnimating: boolean;
  jobPosition: [number, number, number]; // Starting position (job cube)
  isFilled?: boolean;
  isSelected?: boolean;
  canBind?: boolean;
  onRoleClick?: () => void;
}) => {
  const [hovered, setHovered] = useState(false);

  const cubeMeshRef = useRef<THREE.Object3D | null>(null);

  const [linePoints, setLinePoints] = useState<
    [[number, number, number], [number, number, number]]
  >([jobPosition, position]);

  // Keep the connector start aligned with the rotating jobs group.
  // The role endpoint stays fixed in world space.
  useFrame(() => {
    const start = new THREE.Vector3(jobPosition[0], jobPosition[1], jobPosition[2]);

    // Prefer the real cube mesh world position (includes pull-in/out translation)
    const jobGroup = jobGroupRef?.current;
    if (jobGroup) {
      if (!cubeMeshRef.current || cubeMeshRef.current.parent == null) {
        jobGroup.traverse((child) => {
          if (cubeMeshRef.current) return;
          // Find first mesh child (R3F augments Object3D types, so just check type string)
          if (child.type === 'Mesh') {
            cubeMeshRef.current = child as unknown as THREE.Object3D;
          }
        });
      }
      if (cubeMeshRef.current) {
        cubeMeshRef.current.getWorldPosition(start);
      }
    } else if (groupRef.current) {
      // Fallback: rotate the original local job position by wheel rotation
      start.applyEuler(groupRef.current.rotation);
    }

    const end: [number, number, number] = position;
    setLinePoints([
      [start.x, start.y, start.z],
      end,
    ]);
  });

  return (
    <group>
      <Line
        points={linePoints}
        lineWidth={2}
        color={connectorColor}
        opacity={0.7}
        // Purely visual; don't steal clicks from the role sphere.
        raycast={() => null}
      />
      <Sphere
        position={position}
        args={[0.2, 16, 16]}
        onPointerOver={(e) => {
          if (isAnimating) return;
          e.stopPropagation();
          setHovered(true);
        }}
        onPointerOut={(e) => {
          if (isAnimating) return;
          e.stopPropagation();
          setHovered(false);
        }}
        onPointerDown={(e) => {
          e.stopPropagation();
        }}
        onClick={(e) => {
          e.stopPropagation();
          if (isAnimating) return;
          if (canBind && onRoleClick) {
            onRoleClick();
          }
        }}
      >
        {isFilled ? (
          <meshStandardMaterial
            color={color}
            emissive={color}
            emissiveIntensity={hovered ? 0.8 : 0.45}
            roughness={0.2}
          />
        ) : (
          <>
            {/* Outlined sphere: nearly invisible fill (but still receives raycasts) + edges. */}
            <meshBasicMaterial
              transparent
              opacity={0.01}
              depthWrite={false}
              colorWrite={false}
            />
            <Edges color={hovered || isSelected ? '#FFFF00' : color} />
          </>
        )}
      </Sphere>
      <Html
        position={[position[0], position[1] + 0.55, position[2]]}
        distanceFactor={10}
        center
        style={{
          pointerEvents: 'none',
          opacity: hovered || isSelected ? 1 : 0,
          transition: 'opacity 0.2s',
        }}
      >
        <div
          className='bg-[#d1ced1] drop-shadow-xl rounded-xl px-4 py-2 w-40 flex items-center justify-center text-center'
          style={{ pointerEvents: 'none' }}
        >
          <div className='text-xl'>{roleName}</div>
        </div>
      </Html>
    </group>
  );
};
