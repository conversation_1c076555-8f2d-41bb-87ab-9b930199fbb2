import { use<PERSON><PERSON><PERSON>, useThree, type ThreeEvent } from "@react-three/fiber";
import { Html, Line, OrbitControls, Edges, Grid } from "@react-three/drei";
import * as THREE from "three";
import gsap from "gsap";
import type { ElementRef } from "react";
import { useEffect, useMemo, useRef, useState } from "react";

import type { ResourceTypeGenesisKey } from "@/explorer/spaces/genesis/_lib/types";
import R3FPortal from "../cosmos/R3FPortal";
import { useGenesisData } from "./GenesisDataProvider";
import { useCosmosData } from "../cosmos/CosmosDataProvider";
import GenesisPortalAnimation from "./GenesisPortalAnimation";

import { dependencies } from '@toolproof-npm/schema';

type OrbitControlsImpl = ElementRef<typeof OrbitControls>;
type DreiLineImpl = ElementRef<typeof Line>;

const PORTAL_MOVING_RESOURCE_TYPES: ResourceTypeGenesisKey[] = [
    "Natural",
    "Boolean",
    "StatefulStrategy",
    "Job",
];

const PORTAL_MOVING_RESOURCE_TYPES_SET = new Set<ResourceTypeGenesisKey>(
  PORTAL_MOVING_RESOURCE_TYPES
);

const atomCount = 30;
// Schema axis nodes are derived from the generated dependency map exported by @toolproof-npm/schema.
// Preserve insertion order from the JSON so animation sequencing stays stable.
const schemaAxisDependencies = new Map<ResourceTypeGenesisKey, ResourceTypeGenesisKey[]>(
  Object.entries(dependencies as Record<string, string[]>).map(([key, deps]) => [
    key as ResourceTypeGenesisKey,
    (deps ?? []) as ResourceTypeGenesisKey[],
  ])
);

const sphereRadius = 0.15;
const spacing = 1.2;
const worldTiltX = Math.PI / 6;
const worldYawY = Math.PI / 8;

// Speeds up the composition animations for Genesis stages 1 and 2.
// 10 => 10x faster (1000ms/frame -> 100ms/frame)
const GENESIS_COMPOSITION_SPEED = 30;
const COMPOSITION_FRAME_MS = Math.max(1, Math.round(1000 / GENESIS_COMPOSITION_SPEED));

export type GenesisStage = 0 | 1 | 2 | 3 | 4;

type GenesisProps = {
  stage?: GenesisStage;
  orbitControlsRef?: React.RefObject<OrbitControlsImpl | null>;
  showDetailedTooltip?: boolean;
  onResourceTypesReady?: (ready: boolean) => void;
  onPortalReady?: (ready: boolean) => void;
};


function mulberry32(seed: number) {
  return function () {
    let t = (seed += 0x6d2b79f5);
    t = Math.imul(t ^ (t >>> 15), t | 1);
    t ^= t + Math.imul(t ^ (t >>> 7), t | 61);
    return ((t ^ (t >>> 14)) >>> 0) / 4294967296;
  };
}

// Generate animation frames for the composition sequence
function generateCompositionFrames(
  dependencies: Map<ResourceTypeGenesisKey, ResourceTypeGenesisKey[]>
) {
  type NodeState = { index: number; opacity: number };
  type Frame = Map<ResourceTypeGenesisKey, NodeState>;
  const frames: Frame[] = [];

  const currentVisible = new Map<ResourceTypeGenesisKey, NodeState>();

  for (const [key, deps] of dependencies.entries()) {
    // Add the new node at the next available index
    const nextIndex = currentVisible.size + 1; // +1 because index 0 is the stem
    currentVisible.set(key, { index: nextIndex, opacity: 1 });
    frames.push(new Map(currentVisible));

    // If this node has dependencies, fade them before removing
    if (deps.length > 0) {
      // Fade frame: mark dependencies as fading
      const fadingFrame = new Map<ResourceTypeGenesisKey, NodeState>();
      for (const [k, state] of currentVisible.entries()) {
        if (deps.includes(k)) {
          fadingFrame.set(k, { ...state, opacity: 0.3 });
        } else {
          fadingFrame.set(k, state);
        }
      }
      frames.push(fadingFrame);

      // Remove dependencies
      for (const dep of deps) {
        currentVisible.delete(dep);
      }

      // Compact: reassign indices sequentially starting from 1
      const remainingKeys = Array.from(currentVisible.keys());
      currentVisible.clear();
      remainingKeys.forEach((k, idx) => {
        currentVisible.set(k, { index: idx + 1, opacity: 1 });
      });
      frames.push(new Map(currentVisible));
    }
  }

  return frames;
}

const MAIN_GROUP_POSITION = new THREE.Vector3(0, 1, 0);

function ResourceNodeItem({
  resourceTypeKey,
  nodeState,
  spacing,
  sphereRadius,
  setHoveredKey,
  stage,
  parentZ, // Z position relative to main group, effectively -nodeState.index * spacing
  stemX,
  orbitControlsRef,
  portalPosition,
  movesToPortal,
  moverRank,
  moverCount,
  onCameraAnimationComplete,
}: {
  resourceTypeKey: ResourceTypeGenesisKey;
  nodeState: { index: number; opacity: number };
  spacing: number;
  sphereRadius: number;
  setHoveredKey: (key: ResourceTypeGenesisKey | null) => void;
  stage: GenesisStage;
  parentZ: number;
  stemX: number;
  orbitControlsRef: React.MutableRefObject<OrbitControlsImpl | null>;
  portalPosition: THREE.Vector3;
  movesToPortal: boolean;
  moverRank: number;
  moverCount: number;
  onCameraAnimationComplete?: () => void;
}) {
  const groupRef = useRef<THREE.Group>(null);
  const sphereMatRef = useRef<THREE.MeshBasicMaterial>(null);
  const cameraOffsetRef = useRef<THREE.Vector3 | null>(null);
  const finalAnimationTriggeredRef = useRef(false);

  const edgesRef = useRef<THREE.LineSegments | null>(null); // Ref to the Edges (LineSegments)
  const { camera } = useThree();

  // Random offset for organic loop feel
  const randomOffset = useMemo(() => Math.random() * 10, []);

  const handleNodeClick = (e: ThreeEvent<PointerEvent>) => {
    e.stopPropagation();
    if (!groupRef.current) return;

    const targetPos = new THREE.Vector3();
    groupRef.current.getWorldPosition(targetPos);

    const direction = new THREE.Vector3().subVectors(camera.position, targetPos).normalize();
    const newCamPos = targetPos.clone().add(direction.multiplyScalar(4));

    gsap.to(camera.position, {
      x: newCamPos.x,
      y: newCamPos.y,
      z: newCamPos.z,
      duration: 1.5,
      ease: "power3.inOut"
    });

    if (orbitControlsRef.current) {
      gsap.to(orbitControlsRef.current.target, {
        x: targetPos.x,
        y: targetPos.y,
        z: targetPos.z,
        duration: 1.5,
        ease: "power3.inOut"
      });
    }
  };

  const startTimeRef = useRef(-1);
  const [isLabelVisible, setIsLabelVisible] = useState(true);

  useFrame((state) => {
    if (!groupRef.current) return;

    if (stage === 3 && movesToPortal) {
      if (startTimeRef.current === -1) {
        startTimeRef.current = state.clock.elapsedTime;
      }
      const time = state.clock.elapsedTime;
      const duration = 2; // seconds for full trip
      const safeMoverCount = Math.max(moverCount, 1);
      const safeMoverRank = Math.min(Math.max(moverRank, 1), safeMoverCount);
      const stagger = (safeMoverCount - safeMoverRank) * 1.0;

      const elapsed = time - startTimeRef.current;
      const progress = Math.min(Math.max((elapsed - stagger) / duration, 0), 1);

      if (progress > 0.9 && isLabelVisible) {
        setIsLabelVisible(false);
      }

      // Ease function for smoother start
      const ease = 1 - Math.pow(1 - progress, 3); // cubic outish

      // Calculate target world position (Portal) relative to this node's parent
      const targetLocalX = portalPosition.x - stemX;
      const targetLocalY = portalPosition.y - MAIN_GROUP_POSITION.y;
      const targetLocalZ = portalPosition.z - parentZ;

      const startLocal = new THREE.Vector3(0, nodeState.index * spacing, 0);
      const endLocal = new THREE.Vector3(targetLocalX, targetLocalY, targetLocalZ);

      // Interpolate position
      groupRef.current.position.lerpVectors(startLocal, endLocal, ease);

      if (moverRank === 1 && orbitControlsRef.current && progress > 0) {
        const worldPos = new THREE.Vector3();
        groupRef.current.getWorldPosition(worldPos);

        if (!cameraOffsetRef.current) {
           const currentDir = new THREE.Vector3().subVectors(camera.position, worldPos).normalize();
           cameraOffsetRef.current = currentDir.multiplyScalar(4);
        }

        // Final Portal Alignment Animation
        if (progress > 0.9 && !finalAnimationTriggeredRef.current) {
           finalAnimationTriggeredRef.current = true;
           
           // Calculate portal forward direction based on its rotation
           const portalEuler = new THREE.Euler(-Math.PI / 2, Math.PI, Math.PI / 2);
           const portalQuat = new THREE.Quaternion().setFromEuler(portalEuler);
           const forward = new THREE.Vector3(0, 0, 1).applyQuaternion(portalQuat);
           
           // Target position: slightly in front of portal? Or right at it?
           // User said "rotate camera to portal orientation and also the position"
           // Let's go to portal position, looking forward relative to it
           const finalCamPos = portalPosition.clone().add(forward.clone().multiplyScalar(2)); // Offset back a bit
           const finalTarget = portalPosition.clone();

           gsap.to(camera.position, {
             x: finalCamPos.x,
             y: finalCamPos.y,
             z: finalCamPos.z,
             duration: 1.0,
             ease: "power2.inOut"
           });
           gsap.to(orbitControlsRef.current.target, {
             x: finalTarget.x,
             y: finalTarget.y,
             z: finalTarget.z,
             duration: 1.0,
             ease: "power2.inOut",
             onComplete: () => {
                onCameraAnimationComplete?.();
             }
           });
           
           // Stop the chase update since GSAP is taking over
           cameraOffsetRef.current = null;
        }

        if (cameraOffsetRef.current) {
          const targetCamPos = worldPos.clone().add(cameraOffsetRef.current);
          targetCamPos.y -= 1;
          camera.position.lerp(targetCamPos, 0.1);
          orbitControlsRef.current.target.lerp(worldPos, 0.1);
        }
      }

      let currentOpacity = 1;
      if (progress > 0.7) {
        currentOpacity = 1 - (progress - 0.7) / 0.3;
      }
      currentOpacity = Math.max(0, currentOpacity);

      if (sphereMatRef.current) {
        sphereMatRef.current.opacity = currentOpacity * 0.2;
        sphereMatRef.current.transparent = true;
      }
      if (edgesRef.current) {

        if (edgesRef.current.material) {
          edgesRef.current.material.opacity = currentOpacity;
          edgesRef.current.material.transparent = true;
          edgesRef.current.material.needsUpdate = true;
        }
      }

    } else {
      // Reset to normal
      if (!isLabelVisible) setIsLabelVisible(true);
      startTimeRef.current = -1;
      cameraOffsetRef.current = null;
      finalAnimationTriggeredRef.current = false;

      const targetY = nodeState.index * spacing;

      // Smooth return if switching back
      groupRef.current.position.lerp(new THREE.Vector3(0, targetY, 0), 0.1);

      if (sphereMatRef.current) sphereMatRef.current.opacity = nodeState.opacity * 0.2;
      if (edgesRef.current) {
        if (edgesRef.current.material) edgesRef.current.material.opacity = nodeState.opacity;
      }
    }
  });

  return (
    <group
      ref={groupRef}
      position={[0, nodeState.index * spacing, 0]}
    >
      <mesh
        onClick={handleNodeClick}
        onPointerOver={(e) => {
          e.stopPropagation();
          setHoveredKey(resourceTypeKey);
        }}
        onPointerOut={(e) => {
          e.stopPropagation();
          setHoveredKey(null);
        }}
      >
        <boxGeometry
          args={[sphereRadius * 2, sphereRadius * 2, sphereRadius * 2]}
        />
        <meshBasicMaterial visible={false} />
        <Edges
          ref={edgesRef}
          color={"blue"}
          transparent={true}
          // Initial opacity
          opacity={nodeState.opacity}
        />
      </mesh>
      <mesh>
        <sphereGeometry args={[sphereRadius, 32, 32]} />
        <meshBasicMaterial
          ref={sphereMatRef}
          color={"blue"}
          wireframe={true}
          transparent={true}
          opacity={nodeState.opacity * 0.2}
        />
      </mesh>
      {stage >= 2 && isLabelVisible && (
        <Html position={[sphereRadius + 0.3, 0, 0]}>
          <div
            style={{
              padding: "4px 8px",
              background: `rgba(200,220,255,0.9)`, // Fixed opacity for bg to avoid complex html update
              color: "black",
              borderRadius: 3,
              fontSize: 11,
              whiteSpace: "nowrap",
              pointerEvents: "none",
              fontWeight: 500,

            }}
          >
            {resourceTypeKey}
          </div>
        </Html>
      )}
    </group>
  );
}

function SchemaConnector({
  stemX,
  targetZs,
  visible,
}: {
  stemX: number;
  targetZs: number[];
  visible: boolean;
}) {
  const currentZsRef = useRef<number[]>([]);
  const [smoothedPoints, setSmoothedPoints] = useState<
    [number, number, number][]
  >([]);

  useEffect(() => {
    // Snap length changes (nodes added/removed), but keep values for smooth lerp.
    if (currentZsRef.current.length !== targetZs.length) {
      currentZsRef.current = targetZs.slice();
      // Immediately update points when count changes
      setSmoothedPoints(
        currentZsRef.current.map(
          (z) => [stemX, 0, z] as [number, number, number]
        )
      );
    }
  }, [targetZs, stemX]);

  useFrame((_, delta) => {
    if (!visible) return;
    const smoothing = 14;
    const alpha = 1 - Math.exp(-smoothing * delta);

    const zs = currentZsRef.current;
    if (zs.length !== targetZs.length) {
      currentZsRef.current = targetZs.slice();
    } else {
      for (let i = 0; i < targetZs.length; i++) {
        zs[i] = THREE.MathUtils.lerp(zs[i], targetZs[i], alpha);
      }
    }

    setSmoothedPoints(
      currentZsRef.current.map((z) => [stemX, 0, z] as [number, number, number])
    );
  });

  if (!visible || smoothedPoints.length < 2) return null;
  return <Line points={smoothedPoints} color="white" lineWidth={1} />;
}

function SchemaNode(props: {
  stemX: number;
  targetZ: number;
  spacing: number;
  sphereRadius: number;
  schemaKey: ResourceTypeGenesisKey;
  nodeOpacity: number;
  resourceTypeFrame: Map<
    ResourceTypeGenesisKey,
    { index: number; opacity: number }
  >;
  hasSchema: boolean;
  showResourceType: boolean;
  stage: GenesisStage;
  hoveredKey: ResourceTypeGenesisKey | null;
  setHoveredKey: (
    key:
      | ResourceTypeGenesisKey
      | null
      | ((prev: ResourceTypeGenesisKey | null) => ResourceTypeGenesisKey | null)
  ) => void;
  showDetailedTooltip: boolean;
  schemaGenesis: unknown;
  orbitControlsRef: React.MutableRefObject<OrbitControlsImpl | null>;
  portalPosition: THREE.Vector3;
  onCameraAnimationComplete?: () => void;
}) {
  const {
    stemX,
    targetZ,
    spacing,
    sphereRadius,
    schemaKey,
    nodeOpacity,
    resourceTypeFrame,
    hasSchema,
    showResourceType,
    stage,
    hoveredKey,
    setHoveredKey,
    showDetailedTooltip,
    schemaGenesis,
    orbitControlsRef,
    portalPosition,
    onCameraAnimationComplete,
  } = props;

  const groupRef = useRef<THREE.Group>(null);
  const didInitRef = useRef(false);
  const { camera } = useThree();

  const handleSchemaClick = (e: ThreeEvent<PointerEvent>) => {
    e.stopPropagation();
    if (!groupRef.current) return;

    const targetPos = new THREE.Vector3();
    groupRef.current.getWorldPosition(targetPos);

    const direction = new THREE.Vector3().subVectors(camera.position, targetPos).normalize();
    const newCamPos = targetPos.clone().add(direction.multiplyScalar(4));

    gsap.to(camera.position, {
      x: newCamPos.x,
      y: newCamPos.y,
      z: newCamPos.z,
      duration: 1.5,
      ease: "power3.inOut"
    });

    if (orbitControlsRef.current) {
      gsap.to(orbitControlsRef.current.target, {
        x: targetPos.x,
        y: targetPos.y,
        z: targetPos.z,
        duration: 1.5,
        ease: "power3.inOut"
      });
    }
  };

  useFrame((_, delta) => {
    if (!groupRef.current) return;
    const smoothing = 14;
    const alpha = 1 - Math.exp(-smoothing * delta);

    // Initialize once to avoid snapping whenever targetZ changes.
    if (!didInitRef.current) {
      groupRef.current.position.set(stemX, 0, targetZ);
      didInitRef.current = true;
      return;
    }

    groupRef.current.position.x = stemX;
    groupRef.current.position.y = 0;
    groupRef.current.position.z = THREE.MathUtils.lerp(
      groupRef.current.position.z,
      targetZ,
      alpha
    );
  });

  const resourceTypeTargetYs = useMemo(() => {
    const maxIndex = resourceTypeFrame.size;
    return Array.from({ length: maxIndex + 1 }).map((_, i) => i * spacing);
  }, [resourceTypeFrame, spacing]);

  const portalMovingRanks = useMemo(() => {
    const movers = Array.from(resourceTypeFrame.entries())
      .filter(([key]) => PORTAL_MOVING_RESOURCE_TYPES_SET.has(key))
      .sort((a, b) => a[1].index - b[1].index);

    const rankByKey = new Map<ResourceTypeGenesisKey, number>();
    movers.forEach(([key], idx) => {
      rankByKey.set(key, idx + 1);
    });

    return {
      moverCount: movers.length,
      rankByKey,
    };
  }, [resourceTypeFrame]);


  const PortalLine = () => {
    const lineRef = useRef<DreiLineImpl | null>(null); // Drei Line component ref
    const [endPoint, setEndPoint] = useState(new THREE.Vector3(0, 0, 0));
    const targetVec = useRef(new THREE.Vector3(0, 0, 0));

    useFrame((state, delta) => {
      if (stage === 3) {

        const targetLocalX = portalPosition.x - stemX;
        const targetLocalY = portalPosition.y - MAIN_GROUP_POSITION.y;
        const targetLocalZ = portalPosition.z - targetZ;

        targetVec.current.set(targetLocalX, targetLocalY, targetLocalZ);

        // Lerp current end point towards target
        const smoothing = 2;
        const alpha = 1 - Math.exp(-smoothing * delta);

        setEndPoint(prev => {
          const neat = prev.clone().lerp(targetVec.current, alpha);
          return neat;
        });

      } else {
        // Retract
        if (endPoint.length() > 0.1) {
          setEndPoint(prev => prev.clone().lerp(new THREE.Vector3(0, 0, 0), 0.1));
        }
      }
    });

    if (stage < 3 && endPoint.length() < 0.1) return null;

    return (
      <Line
        points={[[0, 0, 0], endPoint]}
        color="white"
        lineWidth={1}
        transparent
        opacity={0.5}
      />
    )
  };

  return (
    <group ref={groupRef}>
      {/* Vertical connector line for ResourceType axis instances */}

      {showResourceType && resourceTypeTargetYs.length > 1 && stage < 3 && (
        <Line
          points={resourceTypeTargetYs.map(
            (y) => [0, y, 0] as [number, number, number]
          )}
          color="white"
          lineWidth={1}
        />
      )}

      <PortalLine />

      {/* ResourceType axis instance nodes */}
      {showResourceType &&
        Array.from(resourceTypeFrame.entries()).map(
          ([resourceTypeKey, nodeState]) => (
            <ResourceNodeItem
              key={`resourceType-${resourceTypeKey}`}
              resourceTypeKey={resourceTypeKey}
              nodeState={nodeState}
              spacing={spacing}
              sphereRadius={sphereRadius}
              setHoveredKey={setHoveredKey}
              stage={stage}
              stemX={stemX}
              parentZ={targetZ} // The Z pos of this SchemaNode
              orbitControlsRef={orbitControlsRef}
              portalPosition={portalPosition}
              movesToPortal={PORTAL_MOVING_RESOURCE_TYPES_SET.has(resourceTypeKey)}
              moverCount={portalMovingRanks.moverCount}
              moverRank={portalMovingRanks.rankByKey.get(resourceTypeKey) ?? 0}
              onCameraAnimationComplete={onCameraAnimationComplete}
            />
          )
        )}

      {/* Schema axis node */}
      <mesh
        onClick={handleSchemaClick}
        onPointerOver={(e) => {
          e.stopPropagation();
          setHoveredKey(schemaKey);
        }}
        onPointerOut={(e) => {
          e.stopPropagation();
          setHoveredKey((prev) => (prev === schemaKey ? null : prev));
        }}
      >
        <boxGeometry
          args={[sphereRadius * 2, sphereRadius * 2, sphereRadius * 2]}
        />
        <meshBasicMaterial visible={false} />
        <Edges
          color={hasSchema ? "black" : "gray"}
          opacity={nodeOpacity}
          transparent={true}
        />
      </mesh>
      <mesh>
        <sphereGeometry args={[Math.sqrt(3) * sphereRadius, 32, 32]} />
        <meshBasicMaterial
          color={hasSchema ? "black" : "gray"}
          wireframe={true}
          transparent={true}
          opacity={nodeOpacity * 0.2}
        />
      </mesh>

      {/* Permanent label - only show from stage 1 onwards */}
      {stage >= 1 && (
        <Html position={[Math.sqrt(3) * sphereRadius + 0.1, 0.1, 0]} distanceFactor={7}>
          <div
            style={{
              padding: "4px 8px",
              background: `rgba(255,255,255,${nodeOpacity * 0.9})`,
              color: "black",
              borderRadius: 3,
              fontSize: 11,
              whiteSpace: "nowrap",
              pointerEvents: "none",
              fontWeight: 500,
              opacity: nodeOpacity,
            }}
          >
            {schemaKey}
          </div>
        </Html>
      )}

      {hoveredKey === schemaKey && (
        <Html position={[0, sphereRadius + 0.35, 0]} center>
          <div
            style={{
              padding: "8px 10px",
              background: "rgba(0,0,0,0.9)",
              color: "white",
              borderRadius: 4,
              fontSize: 11,
              maxWidth: "1200px",
              maxHeight: "300px",
              overflow: "auto",
              pointerEvents: "none",
            }}
          >
            <div
              style={{
                fontWeight: "bold",
                marginBottom: showDetailedTooltip ? "4px" : 0,
                fontSize: 12,
              }}
            >
              {schemaKey}
            </div>
            {showDetailedTooltip && (
              <pre
                style={{
                  margin: 0,
                  whiteSpace: "pre-wrap",
                  wordBreak: "break-word",
                }}
              >
                {JSON.stringify(
                  (schemaGenesis as Record<string, unknown>)[schemaKey],
                  null,
                  2
                )}
              </pre>
            )}
          </div>
        </Html>
      )}
    </group>
  );
}

function AtomNode({
  position,
  isStem,
  sphereRadius,
  orbitControlsRef
}: {
  position: [number, number, number];
  isStem: boolean;
  sphereRadius: number;
  orbitControlsRef: React.MutableRefObject<OrbitControlsImpl | null>;
}) {
  const { camera } = useThree();
  const meshRef = useRef<THREE.Group>(null);

  const handleClick = (e: ThreeEvent<PointerEvent>) => {
    e.stopPropagation();
    if (!meshRef.current) return;

    const targetPos = new THREE.Vector3();
    meshRef.current.getWorldPosition(targetPos);

    const direction = new THREE.Vector3().subVectors(camera.position, targetPos).normalize();
    const newCamPos = targetPos.clone().add(direction.multiplyScalar(4));

    gsap.to(camera.position, {
      x: newCamPos.x,
      y: newCamPos.y,
      z: newCamPos.z,
      duration: 1.5,
      ease: "power3.inOut"
    });

    if (orbitControlsRef.current) {
      gsap.to(orbitControlsRef.current.target, {
        x: targetPos.x,
        y: targetPos.y,
        z: targetPos.z,
        duration: 1.5,
        ease: "power3.inOut"
      });
    }
  };

  return (
    <group position={position} ref={meshRef}>
      <mesh onClick={handleClick}>
        <boxGeometry
          args={[
            sphereRadius * 2,
            sphereRadius * 2,
            sphereRadius * 2,
          ]}
        />
        <meshBasicMaterial visible={false} />
        <Edges color="white" />
      </mesh>
      {isStem && (
        <mesh>
          <sphereGeometry
            args={[Math.sqrt(3) * sphereRadius, 32, 32]}
          />
          <meshBasicMaterial
            color="white"
            wireframe={true}
            transparent={true}
            opacity={0.2}
          />
        </mesh>
      )}
    </group>
  );
}

export default function GenesisSpace({
  stage = 2,
  orbitControlsRef: externalOrbitControlsRef,
  showDetailedTooltip = false,
  onResourceTypesReady,
  onPortalReady,
}: GenesisProps) {
  const { schemaGenesis, resourceTypeGenesis, loading, error } =
    useGenesisData();
  const {
    cosmosData: cosmosSpaceData,
    loading: cosmosLoading,
    error: cosmosError,
  } = useCosmosData();
  const resourceMap = cosmosSpaceData.resourceMap;
  const [hoveredKey, setHoveredKey] = useState<ResourceTypeGenesisKey | null>(
    null
  );
  const internalOrbitControlsRef = useRef<OrbitControlsImpl | null>(null);
  const orbitControlsRef = externalOrbitControlsRef ?? internalOrbitControlsRef;
  const [compositionFrameIndex, setCompositionFrameIndex] = useState(0);
  const [
    resourceTypeCompositionFrameIndex,
    setResourceTypeCompositionFrameIndex,
  ] = useState(0);

  // Portal state
  const [isPortalActive, setIsPortalActive] = useState(false);
  const [showPortalAnimation, setShowPortalAnimation] = useState(false);
  // DOM-only overlays are handled by ExplorerEntry.
  const [resourcesGoingToPortal, setResourcesGoingToPortal] = useState<{ key: ResourceTypeGenesisKey; index: number; opacity: number }[]>([]);

  const loggedStage2Completion = useRef(false);

  const compositionFrames = useMemo(
    () => generateCompositionFrames(schemaAxisDependencies),
    []
  );
  const resourceTypeCompositionFrames = useMemo(
    () => generateCompositionFrames(schemaAxisDependencies),
    []
  );

  // Animate through schema axis composition frames when in stage 1
  useEffect(() => {
    if (stage !== 1) {
      setCompositionFrameIndex(0);
      return;
    }

    if (compositionFrameIndex >= compositionFrames.length - 1) {
      return; // Animation complete
    }

    const timer = setTimeout(() => {
      setCompositionFrameIndex((prev) => prev + 1);
    }, COMPOSITION_FRAME_MS); // 1 second per frame (scaled)

    return () => clearTimeout(timer);
  }, [stage, compositionFrameIndex, compositionFrames.length]);

  useEffect(() => {
    if (stage < 2) {
      setResourceTypeCompositionFrameIndex(0);
      loggedStage2Completion.current = false;
      setResourcesGoingToPortal([]);
      onResourceTypesReady?.(false);
      onPortalReady?.(false);
      return;
    }

    if (
      resourceTypeCompositionFrameIndex >=
      resourceTypeCompositionFrames.length - 1
    ) {
      if (!loggedStage2Completion.current) {
        const baseFrame =
          resourceTypeCompositionFrames[
          resourceTypeCompositionFrames.length - 1
          ];

        const baseCount = baseFrame?.size ?? 0;
        const portalMovers = PORTAL_MOVING_RESOURCE_TYPES.map((key, i) => ({
          key,
          index: baseCount + i + 1,
          opacity: 1,
        }));

        console.log(
          "Stage 2 Animation Complete. Resources going to portal:",
          portalMovers
        );

        setResourcesGoingToPortal(portalMovers);
        onResourceTypesReady?.(true);
        loggedStage2Completion.current = true;
      }
      return; // Animation complete
    }

    const timer = setTimeout(() => {
      setResourceTypeCompositionFrameIndex((prev) => prev + 1);
    }, COMPOSITION_FRAME_MS); // 1 second per frame (scaled)

    return () => clearTimeout(timer);
  }, [
    stage,
    resourceTypeCompositionFrameIndex,
    resourceTypeCompositionFrames,
    resourceTypeCompositionFrames.length,
    onResourceTypesReady,
    onPortalReady
  ]);

  useEffect(() => {
    // console.log('[Genesis] stage prop:', stage);
    const schemaKeys = Object.keys(schemaGenesis || {}).sort();
    const resourceTypeKeys = Object.keys(resourceTypeGenesis || {}).sort();

    const sample = (keys: string[], n = 12) => keys.slice(0, n);

    const schemaSet = new Set(schemaKeys);
    const resourceTypeSet = new Set(resourceTypeKeys);
    const onlyInSchema = schemaKeys.filter((k) => !resourceTypeSet.has(k));
    const onlyInResourceTypes = resourceTypeKeys.filter(
      (k) => !schemaSet.has(k)
    );

    console.group("[Genesis] $defs summary");
    console.log("schema $defs count:", schemaKeys.length);
    console.log("resource-type $defs count:", resourceTypeKeys.length);
    console.log("schema $defs sample:", sample(schemaKeys));
    console.log("resource-type $defs sample:", sample(resourceTypeKeys));

    if (onlyInSchema.length || onlyInResourceTypes.length) {
      console.warn("⚠ $defs mismatch detected");
      if (onlyInSchema.length)
        console.log("only in schema $defs:", onlyInSchema);
      if (onlyInResourceTypes.length)
        console.log("only in resource-type $defs:", onlyInResourceTypes);
    } else {
      console.log("✅ $defs sets match");
    }
    console.groupEnd();
  }, [stage, schemaGenesis, resourceTypeGenesis]);

  useEffect(() => {
    if (cosmosError) {
      console.error("[Genesis] Cosmos data error:", cosmosError);
      return;
    }
    if (cosmosLoading) return;

    console.log(
      "[Genesis] Loaded Cosmos data:",
      JSON.stringify(
        {
          resourceMap,
        },
        null,
        2
      )
    );
  }, [cosmosError, cosmosLoading, resourceMap]);


  console.log("resourcesGoingToPortal", resourcesGoingToPortal);

  const centerOffsetX = ((atomCount - 1) * spacing) / 2;
  const atomToSchemaStemIndex = 12;
  const stemX = atomToSchemaStemIndex * spacing - centerOffsetX;

  const atomLinePoints = useMemo(() => {
    return Array.from({ length: atomCount }).map((_, index) => {
      const x = index * spacing - centerOffsetX;
      return [x, 0, 0] as [number, number, number];
    });
  }, [centerOffsetX]);

  const schemaAxisCurrentFrame = useMemo(() => {
    if (stage === 0) {
      return new Map(
        Array.from(schemaAxisDependencies.keys()).map((key, idx) => [
          key,
          { index: idx + 1, opacity: 1 },
        ])
      );
    } else if (stage === 1) {
      return (
        compositionFrames[compositionFrameIndex] ||
        new Map<ResourceTypeGenesisKey, { index: number; opacity: number }>()
      );
    } else {
      // Stage 2: Only show ResourceType (the final result of stage 1 composition)
      return new Map([["ResourceType", { index: 1, opacity: 1 }]]);
    }
  }, [stage, compositionFrameIndex, compositionFrames]);

  const schemaTargetZs = useMemo(() => {

    const maxIndex = schemaAxisCurrentFrame.size;
    return Array.from({ length: maxIndex + 1 }).map((_, i) => -i * spacing);
  }, [schemaAxisCurrentFrame]);

  const resourceTypeCurrentFrame = useMemo(() => {
    if (stage < 2) {
      return new Map<
        ResourceTypeGenesisKey,
        { index: number; opacity: number }
      >();
    }
    const base =
      resourceTypeCompositionFrames[resourceTypeCompositionFrameIndex] ||
      new Map<ResourceTypeGenesisKey, { index: number; opacity: number }>();

    const stage2Complete =
      resourceTypeCompositionFrameIndex >=
      resourceTypeCompositionFrames.length - 1;

    if (!stage2Complete) return base;

    const extended = new Map(base);
    let nextIndex = extended.size + 1;
    for (const key of PORTAL_MOVING_RESOURCE_TYPES) {
      if (extended.has(key)) continue;
      extended.set(key, { index: nextIndex, opacity: 1 });
      nextIndex += 1;
    }
    return extended;
  }, [
    stage,
    resourceTypeCompositionFrameIndex,
    resourceTypeCompositionFrames,
  ]);

  const created = ({ scene }: { scene: THREE.Scene }) => {
    scene.background = new THREE.Color("skyblue");
  };

  const showAtom = stage >= 0;
  const showSchema = stage >= 1;
  const showResourceType = stage >= 2 && resourceTypeCurrentFrame.size > 0;

  const portalPosition = useMemo(() => {
    let maxIndex = 0;
    for (const state of resourceTypeCurrentFrame.values()) {
      if (state.index > maxIndex) maxIndex = state.index;
    }


    return new THREE.Vector3(-3, (maxIndex + 2) * spacing, -1);
  }, [resourceTypeCurrentFrame]);

  useEffect(() => {
    if (stage === 4) {
      setIsPortalActive(true);

      const timer = setTimeout(() => {
        setShowPortalAnimation(true);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [stage, orbitControlsRef]);


  // Camera State Management for Stage 3
  const savedCameraStateRef = useRef<{ position: THREE.Vector3; target: THREE.Vector3 } | null>(null);

  useEffect(() => {
    // When entering Stage 3, capture the current camera state
    if (stage === 3) {
       if (orbitControlsRef.current) {
         savedCameraStateRef.current = {
           position: orbitControlsRef.current.object.position.clone(),
           target: orbitControlsRef.current.target.clone()
         };
       }
    } 
    // When leaving Stage 3 (specifically going back), restore it
    else if (stage < 3 && savedCameraStateRef.current && orbitControlsRef.current) {
       gsap.to(orbitControlsRef.current.object.position, {
         x: savedCameraStateRef.current.position.x,
         y: savedCameraStateRef.current.position.y,
         z: savedCameraStateRef.current.position.z,
         duration: 1.5,
         ease: "power3.inOut"
       });
       gsap.to(orbitControlsRef.current.target, {
         x: savedCameraStateRef.current.target.x,
         y: savedCameraStateRef.current.target.y,
         z: savedCameraStateRef.current.target.z,
         duration: 1.5,
         ease: "power3.inOut"
       });
       savedCameraStateRef.current = null;
    }
  }, [stage, orbitControlsRef]);

  useEffect(() => {
    if (stage === 3) {
      onPortalReady?.(false);
      const totalCount = Array.from(resourceTypeCurrentFrame.keys()).filter((k) =>
        PORTAL_MOVING_RESOURCE_TYPES_SET.has(k)
      ).length;


      const count = Math.max(totalCount, 1);

      const duration = 2;
      const spacingdelay = 1.0;


      const maxStagger = (count - 1) * spacingdelay;
      const totalTime = maxStagger + duration;

      // Timeout for visual sync of the items arriving, BUT we wait for camera via callback now.
      // Actually we should rely on the callback for the final stage transition.
      // But onPortalReady might be used for other things?
      // User wants "when rotation completed". That is the callback.
      // So we do NOT call onPortalReady(true) here.
      
      return () => {};
    } else if (stage < 3) {
      onPortalReady?.(false);
    } else if (stage === 4) {
      onPortalReady?.(true); // Should be ready if we are at stage 4
    }
  }, [stage, resourceTypeCurrentFrame, onPortalReady]);

  if (loading) return null;
  if (error) return null;

  if (showPortalAnimation) {
    return (
      <GenesisPortalAnimation
        resources={resourcesGoingToPortal}
        orbitControlsRef={orbitControlsRef}
      />
    );
  }



  const handlePortalActiveChange = (active: boolean) => {
    // Only allow activation if we have resources
    if (resourcesGoingToPortal.length === 0) return;

    setIsPortalActive(active);
  };

  return (
    <>
      <group position={MAIN_GROUP_POSITION}>
          <Line points={atomLinePoints} color="white" visible={showAtom} />
          <SchemaConnector
            stemX={stemX}
            targetZs={schemaTargetZs}
            visible={showSchema}
          />

          <group visible={showAtom}>
            {Array.from({ length: atomCount }).map((_, index) => (
              <AtomNode
                key={index}
                position={[index * spacing - centerOffsetX, 0, 0]}
                isStem={index === atomToSchemaStemIndex}
                sphereRadius={sphereRadius}
                orbitControlsRef={orbitControlsRef}
              />
            ))}
          </group>

          <group visible={showSchema}>
            {(() => {
              const currentFrame = schemaAxisCurrentFrame;

              return Array.from(currentFrame.entries()).map(
                ([schemaKey, nodeState]) => {
                  const hasSchema =
                    (schemaGenesis as Record<string, unknown>)[schemaKey] !==
                    undefined;
                  return (
                    <SchemaNode
                      key={`schema-${schemaKey}`}
                      stemX={stemX}
                      targetZ={-nodeState.index * spacing}
                      spacing={spacing}
                      sphereRadius={sphereRadius}
                      schemaKey={schemaKey as ResourceTypeGenesisKey}
                      nodeOpacity={nodeState.opacity}
                      resourceTypeFrame={resourceTypeCurrentFrame}
                      hasSchema={hasSchema}
                      showResourceType={showResourceType}
                      stage={stage}
                      hoveredKey={hoveredKey}
                      setHoveredKey={setHoveredKey}
                      showDetailedTooltip={showDetailedTooltip}
                      schemaGenesis={schemaGenesis}
                      orbitControlsRef={orbitControlsRef}
                      portalPosition={portalPosition}
                      onCameraAnimationComplete={() => onPortalReady?.(true)}
                    />
                  );
                }
              );
            })()}
          </group>
        </group>

        <Grid
          position={[0, -0.01, 0]}
          args={[10.5, 10.5]}
          cellColor="black"
          sectionColor="black"
          sectionThickness={.5}
          cellThickness={0.5}
          fadeDistance={20}
          fadeStrength={1}
          followCamera
          infiniteGrid
        />

        <group
          position={portalPosition}
          scale={0.3}
          rotation={[Math.PI / 2, Math.PI, Math.PI / 2]}
          visible={stage === 3 || stage === 4}
        >
          <R3FPortal
            isPortalActive={isPortalActive}
            setIsPortalActive={handlePortalActiveChange}
            orbitControlsRef={orbitControlsRef}
          />
        </group>

        <ambientLight intensity={0.8} />
        <directionalLight position={[5, 5, 5]} intensity={1} />
    </>
  );
}
