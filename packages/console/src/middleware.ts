import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

const ENFORCE_TEAM_ROUTES_COOKIE = 'tp_enforce_team_routes';

function shouldEnforceTeamRoutes(req: any): boolean {
  // Always enforce in production.
  if (process.env.NODE_ENV === 'production') return true;
  const raw = req?.cookies?.get?.(ENFORCE_TEAM_ROUTES_COOKIE)?.value;
  // In dev/test, default to NOT enforcing until explicitly enabled.
  if (!raw) return false;
  return !(raw === '0' || raw.toLowerCase() === 'false');
}

export default withAuth(
  function middleware(req) {
    try {
      console.log('Middleware function called for:', req.nextUrl.pathname);
      const token = req.nextauth.token;
      const isRestrictedRoute = req.nextUrl.pathname.startsWith('/admin') ||
                               req.nextUrl.pathname.startsWith('/builders');
      const enforceTeamRoutes = shouldEnforceTeamRoutes(req);

      console.log('Middleware: isRestrictedRoute:', isRestrictedRoute, 'token exists:', !!token, 'enforceTeamRoutes:', enforceTeamRoutes);
      if (token) {
        console.log('Middleware: token keys:', Object.keys(token));
        console.log('Middleware: token.isTeamMember:', token.isTeamMember);
      }

      if (isRestrictedRoute) {
        // Check if user is authenticated
        if (!token) {
          console.log('Middleware: No token, redirecting to /auth');
          return NextResponse.redirect(new URL('/auth', req.url));
        }

        // Check if user is a team member (optional in non-production when explicitly disabled)
        if (enforceTeamRoutes) {
          // Ensure we check for both false and undefined
          const isTeamMember = token.isTeamMember === true;
          console.log('Middleware: token.isTeamMember', token.isTeamMember, 'isTeamMember check:', isTeamMember);
          if (!isTeamMember) {
            console.log('Middleware: Not a team member, redirecting to /403');
            return NextResponse.redirect(
              new URL('/403?error=unauthorized&message=Access restricted to team members only', req.url)
            );
          }
        }
        
        console.log('Middleware: Access allowed');
      }

      return NextResponse.next();
    } catch (error) {
      console.error('Middleware error:', error);
      // If there's a JWT decryption error, redirect to auth to clear the session
      const isRestrictedRoute = req.nextUrl.pathname.startsWith('/admin') ||
                               req.nextUrl.pathname.startsWith('/builders');
      if (isRestrictedRoute) {
        return NextResponse.redirect(new URL('/auth?error=session_expired', req.url));
      }
      return NextResponse.next();
    }
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        try {
          const isRestrictedRoute = req.nextUrl.pathname.startsWith('/admin') ||
                                   req.nextUrl.pathname.startsWith('/builders');
          
          console.log('Middleware authorized callback: isRestrictedRoute:', isRestrictedRoute, 'token exists:', !!token);
          
          // For restricted routes, always require authentication.
          // Return true if token exists (even if not team member) so our middleware function can handle the team member check
          if (isRestrictedRoute) {
            const result = !!token;
            console.log('Middleware authorized callback: returning', result);
            return result;
          }
          
          // For other routes, allow access
          return true;
        } catch (error) {
          console.error('Middleware authorized callback error:', error);
          // If there's a decryption error, treat as unauthorized
          return false;
        }
      },
    },
  }
);

export const config = {
  matcher: [
    '/admin/:path*',
    '/builders/:path*',
  ],
};
