import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      isTeamMember: boolean
    }
  }

  interface User {
    id: string
    name?: string | null
    email?: string | null
    isTeamMember?: boolean
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id?: string
    isTeamMember?: boolean
  }
}
