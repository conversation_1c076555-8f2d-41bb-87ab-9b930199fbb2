import { NextResponse } from 'next/server';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { Client } from '@langchain/langgraph-sdk';
import { RemoteGraph } from '@langchain/langgraph/remote';
import { Command } from '@langchain/langgraph';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/_lib/setup/authOptions';
import { cookies } from 'next/headers';
import type {
    GraphEndEvent,
    GraphUpdateEvent,
    ResumeGraphDebugEvent,
    ResumeStartEvent,
    RunGraphErrorEvent,
    RunGraphInterruptEvent,
} from '@/_lib/types/runGraphEvents';

const ENFORCE_TEAM_ROUTES_COOKIE = 'tp_enforce_team_routes';

async function shouldEnforceTeamRoutes(): Promise<boolean> {
    if (process.env.NODE_ENV === 'production') return true;
    const cookieStore = await cookies();
    const raw = cookieStore.get(ENFORCE_TEAM_ROUTES_COOKIE)?.value;
    if (!raw) return false;
    return raw === '1';
}

export const runtime = 'nodejs';

export async function POST(req: Request) {
    try {
        // Check authentication and (optionally) team member status.
        // In production we always enforce team membership.
        // In dev, this is controlled by the same cookie used by middleware/layouts.
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }
        const enforceTeamRoutes = await shouldEnforceTeamRoutes();
        if (enforceTeamRoutes && !session.user.isTeamMember) {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
        }

        const { threadId, resumeValue, useLocalLanggraph } = (await req.json()) as { threadId: string; resumeValue: any; useLocalLanggraph?: boolean };

        if (!threadId) {
            return NextResponse.json({ error: 'Missing threadId' }, { status: 400 });
        }

        // We can optionally check if resumeValue is present, but null might be valid for some interrupts.
        // For our NodeWorkStep use case, we expect an object.
        console.log('@@RESUME_GRAPH', { threadId, resumeValue });

        const urlLocal = `http://localhost:2024`;
        const urlRemote = process.env.LANGGRAPH_REMOTE_API_URL || `https://engine-core-a7953b216e1d518b84f7f1f2cab2edfa.us.langgraph.app`;
        const apiUrl = process.env.LANGGRAPH_API_URL || ((useLocalLanggraph ?? true) ? urlLocal : urlRemote);
        const graphId = CONSTANTS.ENGINE.GraphRunStrategy;

        const apiKey = process.env.LANGCHAIN_API_KEY;
        if (!(useLocalLanggraph ?? true) && !apiKey) {
            return NextResponse.json({ error: 'Missing LANGCHAIN_API_KEY' }, { status: 500 });
        }

        const client = new Client({ apiUrl, apiKey: apiKey ?? '' });
        const remoteGraph = new RemoteGraph({ graphId, url: apiUrl, apiKey: apiKey ?? '' });

        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 30 * 60 * 1000);

        const { readable, writable } = new TransformStream();
        const writer = writable.getWriter();

        (async () => {
            try {
                // Resume the graph by invoking with Command to resolve the interrupt
                const stream = await remoteGraph.stream(
                    new Command({ resume: resumeValue }),
                    {
                        configurable: { thread_id: threadId },
                        signal: controller.signal,
                        streamMode: 'updates',
                    }
                );

                // Emit resume start event
                try {
                    const resumeStartEvent: ResumeStartEvent = { kind: 'resume_start', threadId };
                    await writer.write(new TextEncoder().encode(JSON.stringify(resumeStartEvent) + '\n'));
                } catch { /* ignore */ }

                let eventIdx = 0;
                for await (let event of stream) {
                    try {
                        eventIdx++;

                        // Check for another interrupt
                        if (event && typeof event === 'object' && 'event' in event && event.event === 'interrupt') {
                            console.log('@@RESUME_GRAPH_INTERRUPT_DETECTED', event);
                            const interruptEvent: RunGraphInterruptEvent = {
                                kind: 'interrupt',
                                message: event.value || 'Graph execution interrupted again',
                                threadId
                            };
                            await writer.write(new TextEncoder().encode(JSON.stringify(interruptEvent) + '\n'));
                            break;
                        }

                        // Handle regular events (similar to run-graph route)
                        if (Array.isArray(event) && event.length === 2 && typeof event[0] === 'string') {
                            event = event[1];
                        }

                        const updateObj = (event && typeof event === 'object') ? (event as Record<string, unknown>) : {};

                        // Check for interrupt data in node updates
                        for (const [nodeName, nodeUpdate] of Object.entries(updateObj)) {
                            const nodeUpdateObj = (nodeUpdate && typeof nodeUpdate === 'object') ? (nodeUpdate as Record<string, unknown>) : {};
                            if (nodeUpdateObj['interruptData']) {
                                const interruptDataRaw = nodeUpdateObj['interruptData'] as unknown;
                                const interruptData = (interruptDataRaw && typeof interruptDataRaw === 'object') ? (interruptDataRaw as Record<string, unknown>) : {};
                                if (interruptData.needsUserInput) {
                                    console.log('@@RESUME_GRAPH_INTERRUPT_DATA_DETECTED', { nodeName, interruptData });

                                    const interruptEvent: RunGraphInterruptEvent = {
                                        kind: 'interrupt',
                                        message: interruptData.message,
                                        interruptData,
                                        threadId: threadId
                                    };
                                    await writer.write(new TextEncoder().encode(JSON.stringify(interruptEvent) + '\n'));
                                }
                            }
                        }

                        const nodeKeys = Object.keys(updateObj);
                        if (eventIdx <= 3) {
                            console.log('@@RESUME_GRAPH_UPDATE_SHAPE', { nodeKeys });
                            const shapeDebug: ResumeGraphDebugEvent = { kind: 'resume_graph_debug', label: 'update_shape', nodeKeys };
                            await writer.write(new TextEncoder().encode(JSON.stringify(shapeDebug) + '\n'));
                        }

                        // Forward the event to client
                        const resumeEvent: GraphUpdateEvent = { kind: 'graph_update', event: updateObj };
                        await writer.write(new TextEncoder().encode(JSON.stringify(resumeEvent) + '\n'));

                    } catch (inner) {
                        console.log('@@RESUME_GRAPH_STREAM_ERROR', { message: (inner as Error)?.message || String(inner) });
                        const errEvent: RunGraphErrorEvent = { kind: 'error', message: (inner as Error)?.message || String(inner) };
                        const line = JSON.stringify(errEvent) + '\n';
                        try { await writer.write(new TextEncoder().encode(line)); } catch { }
                    }
                }

                // Emit graph_end once stream completes
                console.log('@@RESUME_GRAPH_END');
                const endEvent: GraphEndEvent = { kind: 'graph_end' };
                const lineEnd = JSON.stringify(endEvent) + '\n';
                try { await writer.write(new TextEncoder().encode(lineEnd)); } catch { }

            } catch (err) {
                const errEvent: RunGraphErrorEvent = { kind: 'error', message: (err as Error)?.message || String(err) };
                const line = JSON.stringify(errEvent) + '\n';
                try { await writer.write(new TextEncoder().encode(line)); } catch { }
            } finally {
                clearTimeout(timeout);
                try { await writer.close(); } catch { }
                if (!controller.signal.aborted) controller.abort();
            }
        })();

        return new Response(readable, {
            status: 200,
            headers: {
                'Content-Type': 'application/x-ndjson; charset=utf-8',
                'Cache-Control': 'no-cache, no-transform',
                'Connection': 'keep-alive',
            },
        });
    } catch (e) {
        return NextResponse.json({ error: (e as Error)?.message || 'Unexpected error' }, { status: 500 });
    }
}
