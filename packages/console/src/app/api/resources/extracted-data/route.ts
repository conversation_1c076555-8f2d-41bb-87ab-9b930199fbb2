import { NextRequest, NextResponse } from 'next/server';
import { Storage } from '@google-cloud/storage';
import path from 'path';
import { existsSync } from 'fs';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

function ensureGcpADC(): void {
  if (!process.env.GOOGLE_APPLICATION_CREDENTIALS && !process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {
    const localKeyPath = path.join(process.cwd(), 'gcp-key.json');
    if (existsSync(localKeyPath)) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = localKeyPath;
    }
  }
}

function normalizeObjectPath(input: string): string {
  // Disallow full gs:// URLs and strip accidental leading slashes.
  return input
    .trim()
    .replace(/^gs:\/\//i, '')
    .replace(/^\/+/, '');
}

function buildError(message: string, status: number): NextResponse {
  return NextResponse.json({ error: message }, { status });
}

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    ensureGcpADC();

    const { searchParams } = new URL(req.url);
    const objectPathRaw = searchParams.get('path');
    if (!objectPathRaw) return buildError('Missing required query param: path', 400);

    const bucketName = process.env.TP_RESOURCES_BUCKET || 'tp-resources';
    const objectPath = normalizeObjectPath(objectPathRaw);
    if (!objectPath) return buildError('Invalid path', 400);

    const storage = new Storage();
    const file = storage.bucket(bucketName).file(objectPath);

    const [exists] = await file.exists();
    if (!exists) return buildError(`Not found: ${objectPath}`, 404);

    const [buf] = await file.download();
    const text = buf.toString('utf8');

    try {
      const json = JSON.parse(text) as unknown;
      return NextResponse.json(json, {
        status: 200,
        headers: {
          // The client already caches; keep this dynamic to avoid stale issues.
          'Cache-Control': 'no-store',
        },
      });
    } catch {
      // If the object isn't JSON, return it as text for easier debugging.
      return new NextResponse(text, {
        status: 200,
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-store',
        },
      });
    }
  } catch (e) {
    const message = e instanceof Error ? e.message : String(e);
    return buildError(`Failed to load extractedData: ${message}`, 500);
  }
}
