import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/_lib/setup/authOptions';
import { cookies } from 'next/headers';

const ENFORCE_TEAM_ROUTES_COOKIE = 'tp_enforce_team_routes';

async function shouldEnforceTeamRoutes(): Promise<boolean> {
  if (process.env.NODE_ENV === 'production') return true;
  const cookieStore = await cookies();
  const raw = cookieStore.get(ENFORCE_TEAM_ROUTES_COOKIE)?.value;
  if (!raw) return false;
  return raw === '1';
}

export const runtime = 'nodejs';

interface StrategyExecutionEventRecord {
  type: string;
  label: string;
  payload: unknown;
  ts: string;
}
interface StrategyExecutionSession {
  startedAt: string;
  endedAt?: string;
  events: StrategyExecutionEventRecord[];
  statefulStrategy?: unknown;
}

export async function POST(req: Request) {
  try {
    // Check authentication and (optionally) team member status.
    // In production we always enforce team membership.
    // In dev, this is controlled by the same cookie used by middleware/layouts.
    const authSession = await getServerSession(authOptions);

    if (!authSession) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const enforceTeamRoutes = await shouldEnforceTeamRoutes();
    if (enforceTeamRoutes && !authSession.user.isTeamMember) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { session, writeStrategyExecutionFile } = (await req.json()) as { session?: StrategyExecutionSession; writeStrategyExecutionFile?: boolean };
    if (!session) {
      return NextResponse.json({ error: 'Missing session' }, { status: 400 });
    }
    // Basic shape validation
    if (!Array.isArray(session.events) || !session.startedAt) {
      return NextResponse.json({ error: 'Invalid session shape' }, { status: 400 });
    }

    const baseDir = path.join(process.cwd(), 'src', 'components', 'spaces', 'cosmos', 'hardcoded');
    const execPath = path.join(baseDir, 'strategyExecution.json');

    const output = {
      meta: {
        savedAt: new Date().toISOString(),
        totalEvents: session.events.length,
      },
      session,
    };

    const shouldWriteStrategyExecutionFile = writeStrategyExecutionFile ?? true;
    if (!shouldWriteStrategyExecutionFile) {
      return NextResponse.json({ ok: true, skippedWrite: true, totalEvents: session.events.length });
    }
    try {
      await fs.writeFile(execPath, JSON.stringify(output, null, 2), 'utf8');
      console.log('@@STRATEGY_API_WRITE_OK', { execPath, totalEvents: session.events.length });
    } catch (writeErr) {
      console.log('@@STRATEGY_API_WRITE_ERROR', { execPath, message: (writeErr as Error)?.message });
      throw writeErr;
    }

    return NextResponse.json({ ok: true, executionPath: execPath, totalEvents: session.events.length });
  } catch (e) {
    console.log('@@STRATEGY_API_ERROR', { message: (e as Error)?.message });
    return NextResponse.json({ error: (e as Error)?.message || 'Unexpected error' }, { status: 500 });
  }
}
