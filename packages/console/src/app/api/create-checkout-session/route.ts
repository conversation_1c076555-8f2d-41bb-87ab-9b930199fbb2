import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-11-17.clover',
  typescript: true,
});

export const runtime = 'nodejs';

export async function POST(req: Request) {
  try {
    const { goalId, goalName, amount } = await req.json();

    if (!goalId || !goalName) {
      return NextResponse.json(
        { error: 'Missing goalId or goalName' },
        { status: 400 }
      );
    }

    // Default to $10 if no amount specified (minimum donation)
    const donationAmount = amount ? Math.round(amount * 100) : 1000; // $10.00 in cents

    // Create Checkout Session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `Donation to: ${goalName}`,
              description: `Support this research goal financially`,
            },
            unit_amount: donationAmount,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/donation/success?session_id={CHECKOUT_SESSION_ID}&goal_id=${goalId}`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/donation/cancel?goal_id=${goalId}`,
      metadata: {
        goalId,
        goalName,
      },
    });

    return NextResponse.json({ sessionId: session.id, url: session.url });
  } catch (error) {
    console.error('Stripe checkout error:', error);
    return NextResponse.json(
      { error: (error as Error).message || 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}

