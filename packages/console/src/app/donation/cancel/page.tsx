'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';

export default function DonationCancelPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const goalId = searchParams.get('goal_id');

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-xl overflow-hidden">
        {/* Cancel Header */}
        <div className="bg-gradient-to-r from-gray-400 to-gray-500 px-6 py-8 text-center">
          <div className="mx-auto w-16 h-16 bg-white rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-10 h-10 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-white">Donation Cancelled</h1>
          <p className="text-gray-100 mt-2">No payment was processed</p>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          <div className="text-center">
            <p className="text-gray-700">
              Your donation was cancelled. No charges were made to your account.
            </p>
            <p className="text-sm text-gray-500 mt-2">
              If you experienced any issues, please try again or contact support.
            </p>
          </div>

          {/* Actions */}
          <div className="space-y-3 pt-4">
            <button
              onClick={() => router.push('/')}
              className="w-full px-4 py-2 bg-[#7A0019] text-[#FFCC33] rounded-md font-semibold hover:bg-[#5A0013] transition-colors"
            >
              Return to Goals
            </button>
            <Link
              href="/"
              className="block w-full text-center px-4 py-2 text-sm text-gray-600 hover:text-[#7A0019] transition-colors"
            >
              Go to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

