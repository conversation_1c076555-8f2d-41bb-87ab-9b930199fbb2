'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';

export default function DonationSuccessPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [goalName, setGoalName] = useState<string>('');
  const goalId = searchParams.get('goal_id');

  useEffect(() => {
    // You can fetch the goal name from the session metadata if needed
    // For now, we'll use a generic message
    if (goalId) {
      // In a real app, you might fetch goal details here
      setGoalName('this research goal');
    }
  }, [goalId]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-xl overflow-hidden">
        {/* Success Header */}
        <div className="bg-gradient-to-r from-green-500 to-green-600 px-6 py-8 text-center">
          <div className="mx-auto w-16 h-16 bg-white rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-10 h-10 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-white">Thank You!</h1>
          <p className="text-green-100 mt-2">Your donation was successful</p>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          <div className="text-center">
            <p className="text-gray-700">
              Your contribution to <span className="font-semibold text-[#7A0019]">{goalName || 'this research goal'}</span> has been received.
            </p>
            <p className="text-sm text-gray-500 mt-2">
              We appreciate your support in advancing research and innovation.
            </p>
          </div>

          {/* Actions */}
          <div className="space-y-3 pt-4">
            <button
              onClick={() => router.push('/')}
              className="w-full px-4 py-2 bg-[#7A0019] text-[#FFCC33] rounded-md font-semibold hover:bg-[#5A0013] transition-colors"
            >
              Return to Goals
            </button>
            <Link
              href="/"
              className="block w-full text-center px-4 py-2 text-sm text-gray-600 hover:text-[#7A0019] transition-colors"
            >
              Go to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

