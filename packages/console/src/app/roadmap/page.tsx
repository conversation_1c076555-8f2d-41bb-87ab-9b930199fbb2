
interface Task {
    description: string;
    locations: string[];
    status: 'awaiting' | 'active' | 'completed';
    hints?: string[];
}

interface Assignment {
    name: string;
    tasks: Task[];
}

const taskStatusOrder: Record<Task['status'], number> = {
    'active': 0,
    awaiting: 1,
    completed: 2,
};

const statusSections: Array<Task['status']> = ['active', 'awaiting', 'completed'];

function getStatusLabel(status: Task['status']) {
    switch (status) {
        case 'active':
            return 'Active';
        case 'awaiting':
            return 'Awaiting';
        case 'completed':
            return 'Completed';
    }
}

function getStatusTextClass(status: Task['status']) {
    switch (status) {
        case 'active':
            return 'font-semibold';
        case 'awaiting':
            return '';
        case 'completed':
            return 'line-through';
    }
}

function getStatusBadgeClass(status: Task['status']) {
    switch (status) {
        case 'active':
            return 'font-semibold';
        case 'awaiting':
            return '';
        case 'completed':
            return '';
    }
}

function getStatusRowClass(status: Task['status']) {
    switch (status) {
        case 'active':
            return 'bg-blue-50';
        case 'awaiting':
            return 'bg-gray-50';
        case 'completed':
            return 'bg-gray-50';
    }
}

const devMap = new Map<string, string>([
    ['dev_0', 'René'],
    ['dev_1', 'Ronak'],
    ['dev_2', 'Kanika'],
    ['dev_3', 'Abhishek'],
]);

const taskMap: Map<string, Task[]> = new Map([
    ['dev_0', [
        {
            description: 'Implement CosmosSpace2 in R3F',
            locations: [],
            status: 'awaiting',
        },
        {
            description: 'Implement CosmosSpace in R3F',
            locations: [],
            status: 'awaiting',
        },
        {
            description: 'Implement GenesisSpace in R3F',
            locations: [],
            status: 'awaiting',
        },
        {
            description: 'Implement portal system for space-transition in R3F',
            locations: [],
            status: 'awaiting',
        },
        {
            description: 'Understand Kanika’s Admin Panel',
            locations: [],
            status: 'awaiting',
        },
        {
            description: 'Understand Ronak’s Interrupt',
            locations: [],
            status: 'awaiting',
        },
        {
            description: 'Move Resource-creation logic from Persistence to Shared',
            locations: [],
            status: 'awaiting',
        },
        {
            description: 'Make Engine mock-mode aware',
            locations: [],
            status: 'awaiting',
        },
        {
            description: 'FeatureToggle',
            locations: [],
            status: 'awaiting',
        },
    ]],
    ['dev_1', [
        {
            description: 'Improve Data Loading and Reactivity',
            locations: ['console'],
            status: 'active',
            hints: ['We only need to merge?'],
        },
        {
            description: 'Separate bucket for StatefulStrategy',
            locations: ['console/StrategyBuilder', 'persistence'],
            status: 'completed',
            hints: ['I have created a new bucket called "strategies"'],
        },
        {
            description: 'Help René Feature-Toggle behavior',
            locations: ['console/configSlice', 'console/StrategyBuilder', 'engine/NodeWorkStep'],
            status: 'awaiting',
            hints: ['interrupt', 'parallelization', 'conditioning'],
        },
        {
            description: 'Persistence must not be LangGraph coupled--create a separate package to host a service that can invoke LangGraph',
            locations: ['persistence', 'engine'],
            status: 'awaiting',
            hints: ['Clean up LangGraph dependencies in persistence package']
        },
        {
            description: 'Implement paralellization of StrategyRuns',
            locations: ['engine'],
            status: 'completed',
            hints: ['Implement algorithm to find what steps can run in parallel', 'Convert statefulStrategy.statelessStrategy.steps to strategyRun.strategyThreadMap']
        },
        {
            description: 'Implement conditional steps (BranchStep, WhileStep, ForStep)',
            locations: ['engine', 'console/StrategyBuilder'],
            status: 'active',
            hints: ['Mutate strategyRun.strategyThreadMap during the run', 'Clone steps when looping']
        },
        {
            description: 'Fix GitHub Actions for packages',
            locations: ['schema', 'shared', 'validation', 'persistence', 'visualization'],
            status: 'completed',
        },
        {
            description: 'Rename calculator deployment to numerical',
            locations: [],
            status: 'active',
        },
    ]],
    ['dev_2', [
        {
            description: 'Implement Admin Panel for Resource Management (Update, Delete, View)',
            locations: ['console/AdminPanel', 'console/firebaseAdminHelpers', 'schema/genesis/generated/types/types.d.ts'],
            status: 'completed',
            hints: ['Have a look at types.d.ts in schema to understand the Resource structure (especially the difference between Resource and ResourcePotentialOutput)']
        },
        {
            description: 'Implement Access Control for Admin Panel and Builders so that only team members can access the routes',
            locations: ['console/AdminPanel', 'console/builders'],
            status: 'active',
        },
        {
            description: 'Improve StrategyBuilder UI/UX',
            locations: ['console/StrategyBuilderControllerDOM'],
            status: 'active',
            hints: []
        },
    ]],
    ['dev_3', [
        {
            description: 'Improve portal transition from GenesisSpace to CosmosSpace',
            locations: ['console/GenesisSpace', 'console/CosmosSpace'],
            status: 'active',
            hints: [],
        },
        {
            description: 'Fix bugs in GenesisSpace',
            locations: ['console/GenesisSpace'],
            status: 'active',
            hints: ['Beautigy display of JSON data', 'Labels glitching at center of scene'],
        },
        {
            description: 'ImproveCosmosSpace',
            locations: ['console/CosmosSpace'],
            status: 'awaiting',
        },
        {
            description: 'Implement XR-version of GenesisSpace',
            locations: ['console/GenesisSpace'],
            status: 'completed',
        },
    ]],
]);

const assignments: Assignment[] = Array.from(taskMap.entries()).map(([name, tasks]) => ({
    name,
    tasks,
}));

export default function Page() {
    const renderData = assignments.filter((developer) => developer.name !== 'dev_0');

    const sortTasks = (tasks: Task[]) =>
        [...tasks].sort((a, b) => {
            const orderDiff = taskStatusOrder[a.status] - taskStatusOrder[b.status];
            if (orderDiff !== 0) return orderDiff;
            return a.description.localeCompare(b.description);
        });

    return (
        <div className="p-8 max-w-6xl mx-auto">
            <h1 className="text-3xl font-bold mb-8">Developer Tasks</h1>

            <div className="grid gap-6 md:grid-cols-3">
                {renderData.map((developer) => (
                    <div key={developer.name} className="border rounded-lg p-6 shadow-sm">
                        <h2 className="text-xl font-semibold mb-4">
                            {devMap.get(developer.name) ?? developer.name}
                        </h2>

                        {statusSections
                            .map((status) => {
                                const tasks = sortTasks(developer.tasks).filter((t) => t.status === status);
                                return { status, tasks };
                            })
                            .filter((section) => section.tasks.length > 0)
                            .map((section, sectionIndex, sections) => (
                                <div key={section.status}>
                                    <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">
                                        {section.status}
                                    </div>

                                    <ul className="space-y-3 list-disc list-inside">
                                        {section.tasks.map((task, index) => (
                                            <li key={`${section.status}-${index}`} className="text-sm">
                                                <div className={`rounded px-2 py-1 ${getStatusRowClass(task.status)}`}>
                                                    <div className="inline">
                                                        <span className={getStatusTextClass(task.status)}>{task.description}</span>
                                                    </div>
                                                    <div className="text-xs text-blue-600 mt-1 ml-5">
                                                        {task.locations.join(', ')}
                                                    </div>
                                                    {task.hints && task.hints.length > 0 && (
                                                        <div className="text-xs text-gray-500 mt-1 ml-5">
                                                            Hints: {task.hints.join(', ')}
                                                        </div>
                                                    )}
                                                </div>
                                            </li>
                                        ))}
                                    </ul>

                                    {sectionIndex < sections.length - 1 && (
                                        <hr className="my-4" />
                                    )}
                                </div>
                            ))}
                    </div>
                ))}
            </div>
        </div>
    );
}