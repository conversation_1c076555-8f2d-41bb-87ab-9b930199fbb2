import { SessionProvider } from '@/components/_providers/SessionProvider'
import Client<PERSON>rovider from '@/components/_providers/ClientProvider'
import StoreProvider from '@/components/_providers/StoreProvider'
import SideBar from '@/components/chat/SideBar'
import { authOptions } from '@/_lib/setup/authOptions';
import '@/_lib/setup/globals.css'
import type { Metadata, Viewport } from 'next'
import { getServerSession } from 'next-auth'

export const metadata: Metadata = {
  title: 'ToolProof',
  description: 'a set of proof-based tools to integrate human and artificial intelligence', // ATTENTION
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession(authOptions)

  return (
    <html lang='en' className='h-full w-full overflow-x-hidden'>
      <body className='h-full w-full overflow-x-hidden'>
        <SessionProvider session={session}>
          <StoreProvider>
            <ClientProvider>
              <div className='flex h-full w-full'>
                {/* Sidebar: fixed width */}
                {/* <div className='hidden sm:block sm:w-[300px] h-full w-full bg-white text-white'>
              <SideBar />
            </div> */}

                {/* Graph area: fills remaining space */}
                <div className='flex-1 w-full' style={{ maxWidth: '100vw', minWidth: 0, overflowX: 'hidden' }}>
                  {children}
                </div>
              </div>
            </ClientProvider>
          </StoreProvider>
        </SessionProvider>
      </body>
    </html>
  )

}
