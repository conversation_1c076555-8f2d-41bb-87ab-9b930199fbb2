'use client';

import { PageLayout } from '@/components/_root/PageLayout';
import { Carousel } from '@/components/_root/Carousel';
import TextCarouselSlide from '@/components/_root/TextCarouselSlide';
import ExplorerEntry from '@/components/explorer/ExplorerEntry';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function Page() {
  const router = useRouter();
  const [currentSlide, setCurrentSlide] = useState(0);
  const explorerSlideIndex = 2; // Explorer is on slide 3 (0-indexed = 2)

  const handleSlideChange = (index: number) => {
    setCurrentSlide(index);
  };

  const handleFullscreenClick = () => {
    router.push('/explorer');
  };

  return (
    <PageLayout contentBg="bg-[#E6F3FF] border-y-2 border-[#FFCC33]">
      <div className="relative w-full h-full min-h-0 overflow-hidden" style={{ maxWidth: '100vw', minWidth: 0 }}>
        {/* Full Width Full Height Carousel */}
        <Carousel
          autoPlay={false}
          showIndicators={true}
          showArrows={true}
          showFullscreenButton={currentSlide === explorerSlideIndex}
          onFullscreenClick={handleFullscreenClick}
          onSlideChange={handleSlideChange}
        >
          {[
            <TextCarouselSlide
              key="slide-1"
              title="Welcome to ToolProof"
              content={[
                'Get to know an open source software platform that aims to integrate human and artificial intelligence through proof-based tools. Check out the demo on the next slide or/and read the documentation to learn more.',
                'NB: ToolProof is currently in early development and is not yet functional. These pages are for demonstration and documentation purposes only.'
              ]}
              backgroundColor="bg-gradient-to-br from-blue-50 to-indigo-100"
            />,
            <ExplorerEntry key="explorer" />,
          ]}
        </Carousel>
      </div>
    </PageLayout>
  );
}
