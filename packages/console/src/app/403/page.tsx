'use client';

import { PageLayout } from '@/components/_root/PageLayout';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

export default function ForbiddenPage() {
  const searchParams = useSearchParams();
  const message = searchParams.get('message') || 'Access restricted to team members only';

  return (
    <PageLayout>
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] px-4">
        <div className="text-center max-w-2xl">
          {/* 403 Icon */}
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-red-100 border-4 border-red-300">
              <svg
                className="w-12 h-12 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>
          </div>

          {/* Error Code */}
          <h1 className="text-6xl font-bold text-[#7A0019] mb-4">403</h1>

          {/* Title */}
          <h2 className="text-3xl font-semibold text-gray-800 mb-4">Forbidden</h2>

          {/* Message */}
          <p className="text-lg text-gray-600 mb-8">{message}</p>

          {/* Description */}
          <p className="text-base text-gray-500 mb-8">
            You don't have permission to access this page. This area is restricted to team members only.
            If you believe this is an error, please contact your administrator.
          </p>

          {/* Action Button */}
          <Link
            href="/"
            className="inline-block px-6 py-3 bg-[#7A0019] text-white rounded-md hover:bg-[#5a0014] transition-colors font-medium"
          >
            Return to Home
          </Link>
        </div>
      </div>
    </PageLayout>
  );
}

