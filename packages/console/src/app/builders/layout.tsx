import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/_lib/setup/authOptions';
import { cookies } from 'next/headers';

const ENFORCE_TEAM_ROUTES_COOKIE = 'tp_enforce_team_routes';

async function shouldEnforceTeamRoutes(): Promise<boolean> {
  if (process.env.NODE_ENV === 'production') return true;
  const cookieStore = await cookies();
  const raw = cookieStore.get(ENFORCE_TEAM_ROUTES_COOKIE)?.value;
  // In dev/test, default to NOT enforcing until explicitly enabled.
  if (!raw) return false;
  return !(raw === '0' || raw.toLowerCase() === 'false');
}

export default async function BuildersLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  try {
    const session = await getServerSession(authOptions);
    const enforceTeamRoutes = await shouldEnforceTeamRoutes();

    // Redirect to auth if not authenticated
    if (!session) {
      redirect('/auth');
    }

    // Redirect to 403 if not a team member
    if (enforceTeamRoutes && !session.user.isTeamMember) {
      redirect('/403?error=unauthorized&message=Access restricted to team members only');
    }

    return <>{children}</>;
  } catch (error) {
    console.error('Builders layout error:', error);
    // Check if it's a JWT decryption error
    const errorObj = error as { name?: string; message?: string };
    if (errorObj?.name === 'JWEDecryptionFailed' || errorObj?.message?.includes('decryption')) {
      console.error('JWT decryption failed - session token is invalid. Redirecting to auth.');
      redirect('/auth?error=session_expired');
    }
    // For other errors, also redirect to auth
    redirect('/auth?error=session_expired');
  }
}

