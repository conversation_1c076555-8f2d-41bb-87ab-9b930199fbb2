# Visualization (Library): Runtime + Primitives

This package provides the reusable building blocks for Toolproof’s 3D experiences.

It is intentionally *domain-agnostic*: it does not know what “Genesis” or “Cosmos” means.
Instead, it provides a generic **runtime** (Canvas/XR shell + orchestration) and a set of **primitives** that console-owned “spaces” can use to build domain visuals.

## What this package is for

- **Runtime shell**: a standardized React Three Fiber environment (camera, controls, XR integration) that console can mount consistently.
- **Runtime orchestration**: a controller that manages which space is active and can run transitions.
- **Primitives**: small, reusable 3D building blocks (including portal primitives) that spaces can compose.

## Runtime

The runtime is responsible for:

- Owning the R3F `<Canvas>` and XR wiring.
- Applying camera configuration (position/target/FOV) consistently.
- Integrating orbit controls (and disabling them during XR sessions).
- Managing active space state and portal-driven transitions.

The runtime does *not* define which spaces exist or what they render. Console drivers do that.

## Primitives

Primitives are small components/helpers that help spaces build consistent visuals.
The most important primitive category today is **portals**, used by the runtime controller to animate transitions between spaces.

Primitives should:

- Stay independent of console application state.
- Avoid assuming specific resource schemas beyond what the component explicitly accepts.
- Be composable and easy to animate externally (via refs/handles) when needed.

## How console uses it

The console package composes the visualization runtime with domain visuals:

- **Drivers** decide what to render and when.
- **Spaces** render domain scenes (e.g. Cosmos, Genesis) and emit events/descriptor data.
- The **RuntimeController** coordinates transitions and renders portal overlays.

If you’re looking for the “Explorer architecture” overview (Runtime + Spaces + Drivers), see:

- core/packages/console/src/components/explorer/EXPLORER.md

## Where to look

- Runtime controller: core/packages/visualization/src/runtime/RuntimeController.tsx
- Runtime view/shell: core/packages/visualization/src/runtime/RuntimeView.tsx
- Portal primitives: core/packages/visualization/src/primitives/