{"resourceMap": {"TYPE-Boolean": [{"identity": "RESOURCE-iZX1cxZ9ImJRzty9Ob4G", "resourceTypeRef": "TYPE-Boolean", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-E02nf2JW8Wll3tTEgNP1"}, "kind": "materialized", "path": "TYPE-Boolean/90658ec3ef0a0347245cec29460a482e59b0470c07ff7ea94016a41e90623e13", "timestamp": "2026-01-04T17:59:27.810Z", "extractedData": {"identity": true}}, {"identity": "RESOURCE-qadnfFGjZsjpqLI0Du1d", "resourceTypeRef": "TYPE-Boolean", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-ATfe2F0Z0WyVIQSCnI7g"}, "kind": "materialized", "path": "TYPE-Boolean/559d06682e4b15855450c4f8837ee5c4acdea5d508c27899bf079dbb745162be", "timestamp": "2026-01-05T10:25:38.748Z", "extractedData": {"identity": false}}], "TYPE-Job": [{"identity": "RESOURCE-00veetsTYJNcIpaGiWiC", "resourceTypeRef": "TYPE-Job", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-2GO2BdD9vfCy5Zp7dLpY"}, "kind": "materialized", "path": "TYPE-Job/8dc1a346da15a757e70a6f3b2fb21f057853b5965502dd9976346b7c63330850", "timestamp": "2026-01-09T13:49:52.261Z", "extractedData": {"identity": "JOB-BMX7PW3vYBhKs9DbGDuS", "name": "Divide", "description": "dummy-description", "implementationUri": "http://*************/divide", "roles": {"inputMap": {"ROLE-LIbcSHOT8p4uu0MY6Lpl": {"resourceTypeRef": "TYPE-Natural", "name": "Dividend", "description": "dummy-description"}, "ROLE-OhXZNvtszNPse4XAA4fk": {"resourceTypeRef": "TYPE-Natural", "name": "Divisor", "description": "dummy-description"}}, "outputMap": {"ROLE-iUQoZWm3SslvkKAiox9g": {"resourceTypeRef": "TYPE-Natural", "name": "Quotient", "description": "dummy-description"}, "ROLE-morUGJ2XPmUmMb5UkvQ5": {"resourceTypeRef": "TYPE-Natural", "name": "<PERSON><PERSON><PERSON>", "description": "dummy-description"}, "ROLE-ErrorOutput": {"resourceTypeRef": "TYPE-Error", "name": "ErrorOutput", "description": "Represents error outputs from job executions."}}}}}, {"identity": "RESOURCE-3QVDuP2RI5vF8T6IWWzT", "resourceTypeRef": "TYPE-Job", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-MLC0wMlkd5RkQ9HO8yAV"}, "kind": "materialized", "path": "TYPE-Job/0149b6defb6c1ae3b31ce82d641574da623aa4b76c1af5f48db1fc2ec85b952a", "timestamp": "2026-01-13T06:57:32.267Z", "extractedData": {"identity": "JOB-bzePRd0ZJ0QMN8ZycXL0", "name": "Double", "description": "double", "implementationUri": "http://*************/double", "roles": {"inputMap": {"ROLE-XjqaruvyjrT3dIkDOW86": {"resourceTypeRef": "TYPE-Natural", "name": "N", "description": "desc"}}, "outputMap": {"ROLE-7AXmzIGERZQJ8YAlOr3o": {"resourceTypeRef": "TYPE-Natural", "name": "Doubled", "description": "desc"}, "ROLE-ErrorOutput": {"resourceTypeRef": "TYPE-Error", "name": "ErrorOutput", "description": "Represents error outputs from job executions."}}}}}, {"identity": "RESOURCE-8B2womnRNAFRSK4eUS4k", "resourceTypeRef": "TYPE-Job", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-rIbCu1PPpRa40kF4Bg0h"}, "kind": "materialized", "path": "TYPE-Job/ce39a56e015f36795fe1e6a0e0d3d652368df40d5ee9218bb7026b1573b5fad0", "timestamp": "2026-01-12T18:38:58.009Z", "extractedData": {"identity": "JOB-LessThan", "name": "<PERSON><PERSON><PERSON>", "description": "dummy-description", "implementationUri": "https://toolproof-engine/less-than", "roles": {"inputMap": {"ROLE-u4vW7PDlX6V9IGJoNxhl": {"resourceTypeRef": "TYPE-Natural", "name": "DynamicSource", "description": "dummy-description"}, "ROLE-xLaQo8L1fMxKTQUJcaJn": {"resourceTypeRef": "TYPE-Natural", "name": "StaticTarget", "description": "dummy-description"}}, "outputMap": {"ROLE-C4FKVwXipgngzPQc3MDc": {"resourceTypeRef": "TYPE-Boolean", "name": "Decision", "description": "dummy-description"}, "ROLE-ErrorOutput": {"resourceTypeRef": "TYPE-Error", "name": "ErrorOutput", "description": "Represents error outputs from job executions."}}}}}, {"identity": "RESOURCE-GCb6VUB1eaIYVVSqmWD9", "resourceTypeRef": "TYPE-Job", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-GdUaRPzAzlXDitsmfWqi"}, "kind": "materialized", "path": "TYPE-Job/79e973d4205f305a19ee7fcad4df7a524f578df8e8cc5f3d3419cb96852dc2d7", "timestamp": "2026-01-09T13:45:34.299Z", "extractedData": {"identity": "JOB-8DMl9CSHnhIJm1GqmZdu", "name": "Subtract", "description": "dummy-description", "implementationUri": "http://*************/subtract", "roles": {"inputMap": {"ROLE-JxfvRsSfrhv1f7hljNNT": {"resourceTypeRef": "TYPE-Natural", "name": "Minuend", "description": "dummy-description"}, "ROLE-8miAFrfm44xImZyKDYlT": {"resourceTypeRef": "TYPE-Natural", "name": "Subtrahend", "description": "dummy-description"}}, "outputMap": {"ROLE-tmDkwin89G0fJevAiJAk": {"resourceTypeRef": "TYPE-Natural", "name": "Difference", "description": "dummy-description"}, "ROLE-ErrorOutput": {"resourceTypeRef": "TYPE-Error", "name": "ErrorOutput", "description": "Represents error outputs from job executions."}}}}}, {"identity": "RESOURCE-rs0gzo1P4xLOyHsLloT0", "resourceTypeRef": "TYPE-Job", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-giX4VmkPwULaiSHuwtZN"}, "kind": "materialized", "path": "TYPE-Job/fef48aaf2b29ab80fda202c996ba861c9674a1bc04a480d30c6e7d297d2b23a0", "timestamp": "2026-01-09T13:47:19.416Z", "extractedData": {"identity": "JOB-5GIxeers93hC8lEHeDX3", "name": "Multiply", "description": "dummy-description", "implementationUri": "http://*************/multiply", "roles": {"inputMap": {"ROLE-nSwFCAZ9fclBI67P9Oex": {"resourceTypeRef": "TYPE-Natural", "name": "Multiplicand", "description": "dummy-description"}, "ROLE-TIwsruD2XvanYjXl7HzC": {"resourceTypeRef": "TYPE-Natural", "name": "Multiplier", "description": "dummy-description"}}, "outputMap": {"ROLE-rOgs9eznABxyuPXj4Jr6": {"resourceTypeRef": "TYPE-Natural", "name": "Product", "description": "dummy-description"}, "ROLE-ErrorOutput": {"resourceTypeRef": "TYPE-Error", "name": "ErrorOutput", "description": "Represents error outputs from job executions."}}}}}, {"identity": "RESOURCE-sugVSOpJrDhSLGKDz7Gh", "resourceTypeRef": "TYPE-Job", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-EznnnBIEPtTy3Eu2egl8"}, "kind": "materialized", "path": "TYPE-Job/9fc9aa459323492635cfe4f89f1289138d7e150ea8e4f3459e31f59871e1a316", "timestamp": "2026-01-09T13:42:55.051Z", "extractedData": {"identity": "JOB-t9RE4SiDfvhQ4viHRMHB", "name": "Add", "description": "dummy-description", "implementationUri": "http://*************/add", "roles": {"inputMap": {"ROLE-D53eIsRbftGjnBflOJ1y": {"resourceTypeRef": "TYPE-Natural", "name": "AddendOne", "description": "dummy-description"}, "ROLE-CYqdRXOEmYUIfjTSa5zg": {"resourceTypeRef": "TYPE-Natural", "name": "AddendTwo", "description": "dummy-description"}}, "outputMap": {"ROLE-1q8WRPXa6VylxapLzu1y": {"resourceTypeRef": "TYPE-Natural", "name": "Sum", "description": "dummy-description"}, "ROLE-ErrorOutput": {"resourceTypeRef": "TYPE-Error", "name": "ErrorOutput", "description": "Represents error outputs from job executions."}}}}}], "TYPE-ResourceFormat": [{"identity": "RESOURCE-9FZ1Bx1Xt9gTCbhn2wge", "resourceTypeRef": "TYPE-ResourceFormat", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-rX54w3c1oKQ7kIfOa0Zk"}, "kind": "materialized", "path": "TYPE-ResourceFormat/b1457e6ac4c1dff4f2bf2b7fa5a06d55db8753db478c7ef1338049bf5effaf90", "timestamp": "2025-12-21T15:05:19.945Z", "extractedData": {"identity": "FORMAT-ApplicationJob", "name": "application/job", "description": "dummy-description"}}, {"identity": "RESOURCE-rNAZnIV5DNsdTSsqj5o5", "resourceTypeRef": "TYPE-ResourceFormat", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-EcLCAx7LSM2mNVb87waw"}, "kind": "materialized", "path": "TYPE-ResourceFormat/4e1292f437e262dbaa6e866aa853fef09aaa8483c0b1bd0067c99a99c6caf838", "timestamp": "2025-12-20T14:40:10.749Z", "extractedData": {"identity": "FORMAT-ApplicationJson", "name": "application/json", "description": "dummy-description"}}], "TYPE-ResourceType": [{"identity": "RESOURCE-FZ9IhwC1N4I4AeQgDHKY", "resourceTypeRef": "TYPE-ResourceType", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-NJNhtySoREcTKkxqfAoO"}, "kind": "materialized", "path": "TYPE-ResourceType/2160c93e243918fb1b47e47d408b39aef1a47e75a04f5884e6b5c1e8b70f7eac", "timestamp": "2025-12-23T18:51:59.380Z", "extractedData": {"identity": "TYPE-StatefulStrategy", "name": "StatefulStrategy", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StatelessStrategyWrapper"}, {"$ref": "#/$defs/StrategyStateWrapper"}], "properties": {"identity": {"$ref": "#/$defs/StatefulStrategyIdentity"}}, "required": ["identity"], "unevaluatedProperties": false, "$anchor": "StatefulStrategy", "$defs": {"StatelessStrategyWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"statelessStrategy": {"$ref": "#/$defs/StatelessStrategy"}}, "required": ["statelessStrategy"], "$anchor": "StatelessStrategyWrapper"}, "StrategyStateWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"strategyState": {"$ref": "#/$defs/StrategyState"}}, "required": ["strategyState"], "$anchor": "StrategyStateWrapper"}, "StatefulStrategyIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StatefulStrategyIdentity", "pattern": "^STATEFUL_STRATEGY-.+$"}, "StatelessStrategy": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"identity": {"$ref": "#/$defs/StatelessStrategyIdentity"}, "steps": {"type": "array", "items": {"$ref": "#/$defs/Step"}, "uniqueItems": true}}, "required": ["identity", "steps"], "unevaluatedProperties": false, "$anchor": "StatelessStrategy"}, "StrategyState": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/$defs/ResourceMissing"}, {"$ref": "#/$defs/ResourcePotentialInput"}, {"$ref": "#/$defs/ResourcePotentialOutput"}, {"$ref": "#/$defs/Resource"}]}, "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "$anchor": "StrategyState", "propertyNames": {"$ref": "#/$defs/ExecutionIdentity"}}, "StatelessStrategyIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "StatelessStrategyIdentity", "pattern": "^STATELESS_STRATEGY-.+$"}, "Step": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStep"}, {"$ref": "#/$defs/BranchStep"}, {"$ref": "#/$defs/WhileStep"}, {"$ref": "#/$defs/ForStep"}], "unevaluatedProperties": false, "$anchor": "Step"}, "ResourceMissing": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "missing"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourceMissing"}, "ResourcePotentialInput": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "potential-input"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourcePotentialInput"}, "ResourcePotentialOutput": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "potential-output"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourcePotentialOutput"}, "Resource": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceMetaBase"}, {"properties": {"extractedData": {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonData"}, "$comment": "This will be overlayed at runtime to match the data structure of the underlying type's extractionSchema. At compile time, we guarantee it has an identity property."}}, "required": ["extractedData"]}], "unevaluatedProperties": false, "$anchor": "Resource"}, "ResourceRoleIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceRoleIdentity", "pattern": "^ROLE-.+$"}, "ExecutionIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ExecutionIdentity", "pattern": "^EXECUTION-.+$"}, "WorkStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"execution": {"$ref": "#/$defs/Execution"}, "identity": {"$ref": "#/$defs/WorkStepIdentity"}, "kind": {"const": "work"}}, "required": ["identity", "kind", "execution"], "$anchor": "WorkStep"}, "BranchStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"cases": {"type": "array", "items": {"$ref": "#/$defs/Conditional"}, "minItems": 1, "uniqueItems": true}, "identity": {"$ref": "#/$defs/BranchStepIdentity"}, "kind": {"const": "branch"}}, "required": ["identity", "kind", "cases"], "$anchor": "BranchStep"}, "WhileStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"case": {"$ref": "#/$defs/Conditional"}, "identity": {"$ref": "#/$defs/WhileStepIdentity"}, "kind": {"const": "while"}}, "required": ["identity", "kind", "case"], "$anchor": "WhileStep"}, "ForStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/StepKind"}], "properties": {"case": {"$ref": "#/$defs/Conditional"}, "identity": {"$ref": "#/$defs/ForStepIdentity"}, "kind": {"const": "for"}}, "required": ["identity", "kind", "case"], "$anchor": "ForStep"}, "ResourceBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"identity": {"$ref": "#/$defs/ResourceIdentity"}, "resourceTypeRef": {"$ref": "#/$defs/ResourceTypeIdentity"}}, "required": ["identity", "resourceTypeRef"], "$anchor": "ResourceBase"}, "ResourceKind": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"kind": {"enum": ["missing", "potential-input", "potential-output", "materialized"]}}, "required": ["kind"], "$anchor": "ResourceKind"}, "CreationContextWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"creationContext": {"$ref": "#/$defs/CreationContext"}}, "required": ["creationContext"], "$anchor": "CreationContextWrapper"}, "ResourceMetaBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CreationContextWrapper"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "materialized"}}, "required": ["kind"]}, {"$ref": "#/$defs/Timestamp"}, {"$ref": "#/$defs/Path"}], "$anchor": "ResourceMetaBase"}, "JsonData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"$ref": "#/$defs/JsonData"}}, {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonData"}}], "$anchor": "JsonData"}, "StepKind": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"kind": {"type": "string", "enum": ["work", "branch", "while", "for"]}}, "required": ["kind"], "$anchor": "<PERSON><PERSON><PERSON>"}, "Execution": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "This will be overlayed at runtime to specify roleBindings corresponding to the roles of the underlying job.", "$ref": "#/$defs/RoleBindingsWrapper"}], "properties": {"identity": {"$ref": "#/$defs/ExecutionIdentity"}, "jobRef": {"$ref": "#/$defs/JobIdentity"}}, "required": ["identity", "jobRef"], "$anchor": "Execution"}, "WorkStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkStepIdentity", "pattern": "^WORKSTEP-.+$"}, "Conditional": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"what": {"$ref": "#/$defs/WorkStep"}, "when": {"$ref": "#/$defs/WorkStep"}}, "required": ["when", "what"], "unevaluatedProperties": false, "$anchor": "Conditional"}, "BranchStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "BranchStepIdentity", "pattern": "^BRANCHSTEP-.+$"}, "WhileStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WhileStepIdentity", "pattern": "^WHILESTEP-.+$"}, "ForStepIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ForStepIdentity", "pattern": "^FORSTEP-.+$"}, "ResourceIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceIdentity", "$comment": "", "pattern": "^RESOURCE-.+$"}, "ResourceTypeIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceTypeIdentity", "pattern": "^TYPE-.+$"}, "CreationContext": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"executionRef": {"$ref": "#/$defs/ExecutionIdentity"}, "resourceRoleRef": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "required": ["resourceRoleRef", "executionRef"], "$anchor": "CreationContext"}, "Timestamp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}}, "required": ["timestamp"], "$anchor": "Timestamp"}, "Path": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "$anchor": "Path"}, "RoleBindingsWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roleBindings": {"$ref": "#/$defs/RoleBindings"}}, "required": ["roleB<PERSON>ings"], "$anchor": "RoleBindingsWrapper"}, "JobIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "JobIdentity", "$comment": "", "pattern": "^JOB-.+$"}, "RoleBindings": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}, "outputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}}, "required": ["inputBindingMap", "outputBindingMap"], "unevaluatedProperties": false, "$anchor": "RoleBindings"}, "RoleBindingMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceIdentity"}, "$anchor": "RoleBindingMap", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}}}}}, {"identity": "RESOURCE-J8J6y6jtLwhEXIMLSbLB", "resourceTypeRef": "TYPE-ResourceType", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-W7xF9nBE9v3aM66MmLQl"}, "kind": "materialized", "path": "TYPE-ResourceType/57dd88e2398232edbf505ea78f7979b4dd1695147586da8a128a7a7485613c2e", "timestamp": "2025-12-21T16:12:03.350Z", "extractedData": {"identity": "TYPE-Boolean", "name": "Boolean", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Boolean", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity"], "properties": {"identity": {"$ref": "#/$defs/BooleanIdentity"}}, "$defs": {"BooleanIdentity": {"type": "boolean"}}, "additionalProperties": false}}}, {"identity": "RESOURCE-KADYQjah6aW3pbXI90Hd", "resourceTypeRef": "TYPE-ResourceType", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-g3HUBDm1sHuUPiFjewIc"}, "kind": "materialized", "path": "TYPE-ResourceType/2651122cf64c738c12827379a2a771023b591a9baeba4357edfb430302a0ebad", "timestamp": "2025-12-21T16:07:47.538Z", "extractedData": {"identity": "TYPE-Natural", "name": "Natural", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Natural", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity"], "properties": {"identity": {"$ref": "#/$defs/NaturalIdentity"}}, "$defs": {"NaturalIdentity": {"type": "integer"}}, "additionalProperties": false}}}, {"identity": "RESOURCE-trStXoFCKcFvipGB1TIR", "resourceTypeRef": "TYPE-ResourceType", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-RE352M9mIWcEXJLm941o"}, "kind": "materialized", "path": "TYPE-ResourceType/2b55cf134b0617bd3daea963001707b38c6fec69a4c1fadda363b9116148ad1e", "timestamp": "2025-12-21T17:09:25.012Z", "extractedData": {"identity": "TYPE-Job", "name": "Job", "description": "dummy-description", "resourceFormatRef": "FORMAT-ApplicationJob", "extractionSchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/RolesWrapper"}], "properties": {"identity": {"$ref": "#/$defs/JobIdentity"}, "implementationUri": {"type": "string", "format": "uri"}}, "required": ["identity", "implementationUri"], "unevaluatedProperties": false, "$anchor": "Job", "$defs": {"Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Named"}, {"$ref": "#/$defs/Described"}], "$anchor": "Documented"}, "RolesWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roles": {"$ref": "#/$defs/Roles"}}, "required": ["roles"], "$anchor": "RolesWrapper"}, "JobIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "JobIdentity", "$comment": "", "pattern": "^JOB-.+$"}, "Named": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Named"}, "Described": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Described"}, "Roles": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputMap": {"$ref": "#/$defs/RoleMap"}, "outputMap": {"$ref": "#/$defs/RoleMap"}}, "required": ["inputMap", "outputMap"], "unevaluatedProperties": false, "$anchor": "Roles"}, "RoleMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceRoleValue"}, "$anchor": "RoleMap", "propertyNames": {"$ref": "#/$defs/ResourceRoleIdentity"}}, "ResourceRoleValue": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"properties": {"resourceTypeRef": {"$ref": "#/$defs/ResourceTypeIdentity"}}, "required": ["resourceTypeRef"], "$comment": "A ResourceRole does not have a self-contained identity, as it is always defined in the context of a RoleMap. ResourceRoleValue uses the Value suffix to mean 'the value stored in this map' (RoleMap.additionalProperties)."}, {"$ref": "#/$defs/Documented"}], "$anchor": "ResourceRoleValue"}, "ResourceRoleIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceRoleIdentity", "pattern": "^ROLE-.+$"}, "ResourceTypeIdentity": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceTypeIdentity", "pattern": "^TYPE-.+$"}}}, "extractorUri": "https://extractors.toolproof.com/v0/DummyExtractor.js"}}], "TYPE-StatefulStrategy": [{"identity": "RESOURCE-LPNeDexdKlbrskF8bE1b", "resourceTypeRef": "TYPE-StatefulStrategy", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-tSCK8ca6P1TFtY17Td0C"}, "kind": "materialized", "path": "TYPE-StatefulStrategy/8acd91a23ae1548ce04cbc6988bf53c8a5ce63eae1629951ee4b5ea5447c8333", "timestamp": "2026-01-14T06:29:00.767Z", "extractedData": {"identity": "STATEFUL_STRATEGY-4rvPKqAQhc078pnOYdS2", "statelessStrategy": {"identity": "STATELESS_STRATEGY-dJvQRMnobe7oey6ux7KN", "steps": [{"identity": "WORK-8Zy9yQVu4zBJ6CxmuRzX", "kind": "work", "execution": {"identity": "EXECUTION-8Zy9yQVu4zBJ6CxmuRzX", "jobRef": "JOB-t9RE4SiDfvhQ4viHRMHB", "roleBindings": {"inputBindingMap": {"ROLE-D53eIsRbftGjnBflOJ1y": "RESOURCE-laX0gnWQYusLBm04FhSD", "ROLE-CYqdRXOEmYUIfjTSa5zg": "RESOURCE-Cni0keiEwwYzslBtpmaB"}, "outputBindingMap": {"ROLE-1q8WRPXa6VylxapLzu1y": "RESOURCE-28VGL2fHMCgTrPTIg84S", "ROLE-ErrorOutput": "RESOURCE-UxGek4m6KJhRvdL38Yev"}}}}, {"identity": "WORK-5IWcGBoou4BVZZvc8CZT", "kind": "work", "execution": {"identity": "EXECUTION-5IWcGBoou4BVZZvc8CZT", "jobRef": "JOB-5GIxeers93hC8lEHeDX3", "roleBindings": {"inputBindingMap": {"ROLE-nSwFCAZ9fclBI67P9Oex": "RESOURCE-siAC6bGV118lrWQHp2SM", "ROLE-TIwsruD2XvanYjXl7HzC": "RESOURCE-cBRwPLansYRxhoTEq6tq"}, "outputBindingMap": {"ROLE-rOgs9eznABxyuPXj4Jr6": "RESOURCE-Hl5EWq1500rxCVFM3HEn", "ROLE-ErrorOutput": "RESOURCE-gUprIx9CCqpWpieYmC3j"}}}}, {"identity": "WORK-dfhIEK9pXwUQ0udBHZW0", "kind": "work", "execution": {"identity": "EXECUTION-dfhIEK9pXwUQ0udBHZW0", "jobRef": "JOB-8DMl9CSHnhIJm1GqmZdu", "roleBindings": {"inputBindingMap": {"ROLE-JxfvRsSfrhv1f7hljNNT": "RESOURCE-IxT2SMwWK4T20Ww1dnS6", "ROLE-8miAFrfm44xImZyKDYlT": "RESOURCE-pHK6Dw756uXgOmsyL11Y"}, "outputBindingMap": {"ROLE-tmDkwin89G0fJevAiJAk": "RESOURCE-QqY6PlgpzymvS7933Fe4", "ROLE-ErrorOutput": "RESOURCE-vr7kYcJdj4deDxtjHoV5"}}}}]}, "strategyState": {"EXECUTION-8Zy9yQVu4zBJ6CxmuRzX": {"ROLE-D53eIsRbftGjnBflOJ1y": {"identity": "RESOURCE-9uVapefU5fjkXVYYJ1Gf", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Cb45gZH8q2V4IsBPOJ1G", "executionRef": "EXECUTION-wFJZiHyHyhfjCzSJI1sh"}, "kind": "materialized", "path": "TYPE-Natural/ec51fd95d5b843ee326c2542a3a918ca0bb5049e41c5bb8cb353346a1eeeb87a", "timestamp": "2026-01-09T11:31:18.549Z", "extractedData": {"identity": 6}}, "ROLE-CYqdRXOEmYUIfjTSa5zg": {"identity": "RESOURCE-A20P5JT43XTEV8ocOWBT", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Z0vE1UFCB1dnLQHV5H7e", "executionRef": "EXECUTION-HaPy0x6VSlmpapvm0jXc"}, "kind": "materialized", "path": "TYPE-Natural/0f0111193eb5806fa6771e6708052e3030589696b04af6051553a617ce5b26df", "timestamp": "2026-01-09T11:30:40.129Z", "extractedData": {"identity": 3}}, "ROLE-1q8WRPXa6VylxapLzu1y": {"identity": "RESOURCE-28VGL2fHMCgTrPTIg84S", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-1q8WRPXa6VylxapLzu1y", "executionRef": "EXECUTION-8Zy9yQVu4zBJ6CxmuRzX"}, "kind": "potential-output"}, "ROLE-ErrorOutput": {"identity": "RESOURCE-UxGek4m6KJhRvdL38Yev", "resourceTypeRef": "TYPE-Error", "creationContext": {"resourceRoleRef": "ROLE-ErrorOutput", "executionRef": "EXECUTION-8Zy9yQVu4zBJ6CxmuRzX"}, "kind": "potential-output"}}, "EXECUTION-5IWcGBoou4BVZZvc8CZT": {"ROLE-nSwFCAZ9fclBI67P9Oex": {"identity": "RESOURCE-28VGL2fHMCgTrPTIg84S", "resourceTypeRef": "TYPE-Natural", "creationContext": {"executionRef": "EXECUTION-8Zy9yQVu4zBJ6CxmuRzX", "resourceRoleRef": "ROLE-1q8WRPXa6VylxapLzu1y"}, "kind": "potential-input"}, "ROLE-TIwsruD2XvanYjXl7HzC": {"identity": "RESOURCE-QltEh8HRzl3RikkNvlp6", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-1q8WRPXa6VylxapLzu1y", "executionRef": "EXECUTION-Zb5B69f8g47g62UJ2g2U"}, "kind": "materialized", "path": "TYPE-Natural/8fbf3e1b6cfa14f8a5f6d5fef64d5ca56cac108086915fbba97aa034b5b3da4f", "timestamp": "2026-01-12T05:18:22.299Z", "extractedData": {"identity": 10}}, "ROLE-rOgs9eznABxyuPXj4Jr6": {"identity": "RESOURCE-Hl5EWq1500rxCVFM3HEn", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-rOgs9eznABxyuPXj4Jr6", "executionRef": "EXECUTION-5IWcGBoou4BVZZvc8CZT"}, "kind": "potential-output"}, "ROLE-ErrorOutput": {"identity": "RESOURCE-gUprIx9CCqpWpieYmC3j", "resourceTypeRef": "TYPE-Error", "creationContext": {"resourceRoleRef": "ROLE-ErrorOutput", "executionRef": "EXECUTION-5IWcGBoou4BVZZvc8CZT"}, "kind": "potential-output"}}, "EXECUTION-dfhIEK9pXwUQ0udBHZW0": {"ROLE-JxfvRsSfrhv1f7hljNNT": {"identity": "RESOURCE-Hl5EWq1500rxCVFM3HEn", "resourceTypeRef": "TYPE-Natural", "creationContext": {"executionRef": "EXECUTION-5IWcGBoou4BVZZvc8CZT", "resourceRoleRef": "ROLE-rOgs9eznABxyuPXj4Jr6"}, "kind": "potential-input"}, "ROLE-8miAFrfm44xImZyKDYlT": {"identity": "RESOURCE-JUBsr8flG30hN78JbFkf", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-FQmn0hvhwePIVpl4CLpb"}, "kind": "materialized", "path": "TYPE-Natural/85149ba8dfb3fea64a69448f5dff9e8e0ba215e8a7d95344800a01e9fdfde2e4", "timestamp": "2026-01-08T16:34:40.041Z", "extractedData": {"identity": 1}}, "ROLE-tmDkwin89G0fJevAiJAk": {"identity": "RESOURCE-QqY6PlgpzymvS7933Fe4", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-tmDkwin89G0fJevAiJAk", "executionRef": "EXECUTION-dfhIEK9pXwUQ0udBHZW0"}, "kind": "potential-output"}, "ROLE-ErrorOutput": {"identity": "RESOURCE-vr7kYcJdj4deDxtjHoV5", "resourceTypeRef": "TYPE-Error", "creationContext": {"resourceRoleRef": "ROLE-ErrorOutput", "executionRef": "EXECUTION-dfhIEK9pXwUQ0udBHZW0"}, "kind": "potential-output"}}}}}], "TYPE-Natural": [{"identity": "RESOURCE-0", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/0", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 0}}, {"identity": "RESOURCE-1", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/1", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 1}}, {"identity": "RESOURCE-2", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/2", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 2}}, {"identity": "RESOURCE-3", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/3", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 3}}, {"identity": "RESOURCE-4", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/4", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 4}}]}}