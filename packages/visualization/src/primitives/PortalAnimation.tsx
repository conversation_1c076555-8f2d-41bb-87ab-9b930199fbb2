import React from 'react';
import { Grid, OrbitControls } from '@react-three/drei';
import * as THREE from 'three';
import type { ElementRef } from 'react';

import Portal from './Portal.js';

type OrbitControlsImpl = ElementRef<typeof OrbitControls>;

export type PortalAnimationItem = {
    id: string;
    index: number;
    opacity: number;
    label?: string;
};

export type PortalAnimationRenderItemArgs<TItem extends PortalAnimationItem = PortalAnimationItem> = {
    item: TItem;
    index: number;
    startPosition: [number, number, number];
    position: [number, number, number];
    delay: number;
    portalPosition: [number, number, number];
};

export interface PortalAnimationProps {
    items: PortalAnimationItem[];
    renderItem: (args: PortalAnimationRenderItemArgs) => React.ReactNode;
    orbitControlsRef?: React.RefObject<OrbitControlsImpl | null>;
    onPortalReady?: (ready: boolean) => void;
    portalPosition?: [number, number, number];
    radius?: number;
    center?: [number, number, number];
    itemDelaySeconds?: number;
}
export default function PortalAnimation({
    items,
    renderItem,
    orbitControlsRef,
    onPortalReady,
    portalPosition = [0, 2, 0],
    radius = 5,
    center = [15, 0, 0],
    itemDelaySeconds = 0.2,
}: PortalAnimationProps) {

    return (
        <>
            <Grid
                position={[0, -0.01, 0]}
                args={[20, 20]}
                cellColor="#333"
                sectionColor="#555"
                sectionThickness={1}
                cellThickness={0.5}
                fadeDistance={20}
                fadeStrength={1}
                infiniteGrid
            />

            <ambientLight intensity={0.4} />
            <directionalLight position={[10, 10, 5]} intensity={1} />
            <directionalLight position={[-10, -10, -5]} intensity={0.5} color="#4ecdc4" />
            <pointLight position={[0, 5, 0]} intensity={0.5} color="#ff6b6b" />

            <group position={portalPosition} scale={0.4} rotation={[0, Math.PI / 2, 0]}>
                <Portal
                    isActive
                    setIsActive={() => { }}
                    orbitControlsRef={orbitControlsRef as unknown as React.RefObject<{ target: THREE.Vector3 } | null>}
                    onTransitionComplete={() => onPortalReady?.(true)}
                />
            </group>

            <group>
                {items.map((item, i) => {
                    const total = Math.max(items.length, 1);
                    const angle = (i / total) * Math.PI * 2;

                    const x = center[0] + radius * Math.cos(angle);
                    const y = center[1];
                    const z = center[2] + radius * Math.sin(angle);

                    return (
                        <React.Fragment key={`${item.id}-${i}`}>
                            {renderItem({
                                item,
                                index: i,
                                startPosition: portalPosition,
                                position: [x, y, z],
                                delay: i * itemDelaySeconds,
                                portalPosition,
                            })}
                        </React.Fragment>
                    );
                })}
            </group>

            <group>
                <mesh position={[15, 0, 0]} rotation={[Math.PI / 2, 0, 0]}>
                    <torusGeometry args={[5, 0.03, 16, 100]} />
                    <meshStandardMaterial color="black" />
                </mesh>
            </group>
        </>
    );
}
