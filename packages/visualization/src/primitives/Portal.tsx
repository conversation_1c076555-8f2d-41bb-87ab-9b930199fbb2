import React, { useEffect, useLayoutEffect, useRef } from 'react';
import * as THREE from 'three';
import { ThreeEvent, useFrame, useThree } from '@react-three/fiber';
import { Environment, MeshPortalMaterial, Sparkles, Stars } from '@react-three/drei';
import gsap from 'gsap';

type OrbitControlsLike = {
    target: THREE.Vector3;
};

type MeshPortalMaterialImpl = React.ElementRef<typeof MeshPortalMaterial>;

export interface PortalProps {
    isActive: boolean;
    setIsActive: (active: boolean) => void;
    orbitControlsRef?: React.RefObject<OrbitControlsLike | null>;
    onTransitionComplete?: (active: boolean) => void;
    children?: React.ReactNode;
}

const PortalRing: React.FC<{
    radius: number;
    rotation?: number;
    speed?: number;
}> = ({ radius, rotation = 0, speed = 1 }) => {
    const ringRef = useRef<THREE.Mesh>(null);

    useFrame((state) => {
        if (ringRef.current) {
            ringRef.current.rotation.z = state.clock.elapsedTime * speed + rotation;
        }
    });

    return (
        <mesh ref={ringRef}>
            <torusGeometry args={[radius, 0.08, 16, 100]} />
            <meshStandardMaterial
                color="#00d4ff"
                emissive="#00d4ff"
                emissiveIntensity={2}
                transparent
                opacity={0.8}
            />
        </mesh>
    );
};

export default function Portal({
    isActive,
    setIsActive,
    orbitControlsRef,
    onTransitionComplete,
    children,
}: PortalProps) {
    const portalMaterial = useRef<MeshPortalMaterialImpl | null>(null);
    const { camera } = useThree();

    const onTransitionCompleteRef = useRef<PortalProps['onTransitionComplete']>(undefined);
    useEffect(() => {
        onTransitionCompleteRef.current = onTransitionComplete;
    }, [onTransitionComplete]);

    useLayoutEffect(() => {
        if (!portalMaterial.current) return;

        gsap.to(portalMaterial.current, {
            blend: isActive ? 1 : 0,
            duration: 0.7,
            ease: 'power2.inOut',
        });
    }, [isActive]);

    useLayoutEffect(() => {
        const targetPos = isActive ? new THREE.Vector3(5, 5, 10) : new THREE.Vector3(10, 10, 0);
        const targetLookAt = isActive ? new THREE.Vector3(0, 0, 20) : new THREE.Vector3(0, 0, 0);

        const tl = gsap.timeline({
            defaults: { duration: 1.5, ease: 'power3.inOut' },
            onComplete: () => onTransitionCompleteRef.current?.(isActive),
        });

        tl.to(camera.position, {
            x: targetPos.x,
            y: targetPos.y,
            z: targetPos.z,
        });

        const orbitControls = orbitControlsRef?.current ?? null;
        if (orbitControls) {
            tl.to(
                orbitControls.target,
                {
                    x: targetLookAt.x,
                    y: targetLookAt.y,
                    z: targetLookAt.z,
                },
                0
            );
        }

        return () => {
            tl.kill();
        };
    }, [isActive, camera, orbitControlsRef]);

    const handleDoubleClick = (e: ThreeEvent<MouseEvent>) => {
        e.stopPropagation();
        setIsActive(!isActive);
    };

    return (
        <group>
            <PortalRing radius={8.5} rotation={0} speed={0.3} />
            <PortalRing radius={9} rotation={Math.PI / 4} speed={-0.2} />
            <PortalRing radius={9.5} rotation={Math.PI / 2} speed={0.15} />

            <Sparkles count={100} size={3} scale={[20, 20, 6]} speed={0.5} color="#00d4ff" />

            <mesh onDoubleClick={handleDoubleClick}>
                <circleGeometry args={[8, 64]} />
                <MeshPortalMaterial ref={portalMaterial} side={THREE.DoubleSide} transparent blur={0.3} resolution={512}>
                    <ambientLight intensity={0.3} />
                    <pointLight position={[0, 5, 5]} intensity={2} color="#00d4ff" />
                    <pointLight position={[0, -5, 5]} intensity={1} color="#8b5cf6" />
                    <Environment preset="night" />

                    <mesh>
                        <sphereGeometry args={[30, 64, 64]} />
                        <meshStandardMaterial color="#050520" side={THREE.BackSide} />
                    </mesh>

                    <mesh>
                        <sphereGeometry args={[28, 32, 32]} />
                        <meshBasicMaterial color="#1a0a30" transparent opacity={0.5} side={THREE.BackSide} />
                    </mesh>

                    <Stars radius={25} depth={50} count={3000} factor={4} saturation={0.5} fade speed={1} />

                    <group>{children}</group>

                    <Sparkles count={200} size={2} scale={[20, 20, 20]} speed={0.3} color="#ffffff" />
                </MeshPortalMaterial>
            </mesh>

            <mesh>
                <ringGeometry args={[7.8, 8.2, 64]} />
                <meshBasicMaterial color="#00d4ff" transparent opacity={0.8} side={THREE.DoubleSide} />
            </mesh>

            <mesh>
                <ringGeometry args={[8, 8.5, 64]} />
                <meshBasicMaterial color="#00d4ff" transparent opacity={0.4} side={THREE.DoubleSide} />
            </mesh>
        </group>
    );
}
