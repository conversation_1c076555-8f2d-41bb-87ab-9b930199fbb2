'use client';

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from '@react-three/drei';

import Runtime, { type RuntimeCameraConfig } from './RuntimeView.js';
import Portal from '../primitives/Portal.js';

export type SpaceId = string;

export type PortalDescriptor = {
    id: string;
    from: SpaceId;
    to: SpaceId;
    position: [number, number, number];
    rotation: [number, number, number];
    scale: number;
    visible: boolean;
};

export type RuntimeControllerStatus = {
    activeSpace: SpaceId;
    portal?: PortalDescriptor;
    isTransitioning: boolean;
};

export type RuntimeControllerHandle = {
    setActiveSpace: (space: SpaceId) => void;
    transitionTo: (space: SpaceId, opts?: { via?: 'portal'; portalId?: string }) => void;
    getStatus: () => RuntimeControllerStatus;
    subscribeStatus: (listener: (status: RuntimeControllerStatus) => void) => () => void;
};

type OrbitControlsImpl = React.ElementRef<typeof OrbitControls>;

export type RuntimeControllerRenderSpaceArgs = {
    activeSpace: SpaceId;
    orbitControlsRef: React.RefObject<OrbitControlsImpl | null>;
    setPortal: (portal?: PortalDescriptor) => void;
};

export type RuntimeControllerProps = {
    initialSpace: SpaceId;
    cameraConfig: RuntimeCameraConfig;
    renderSpace: (args: RuntimeControllerRenderSpaceArgs) => React.ReactNode;
    background?: string;
};

export default React.forwardRef<RuntimeControllerHandle, RuntimeControllerProps>(function RuntimeController(props, ref) {
    const [activeSpace, setActiveSpaceState] = useState<SpaceId>(props.initialSpace);
    const [portalDescriptor, setPortalDescriptor] = useState<PortalDescriptor | undefined>(undefined);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const [isPortalActive, setIsPortalActive] = useState(false);

    const orbitControlsRef = useRef<OrbitControlsImpl | null>(null);
    const cameraRef = useRef<THREE.Camera | null>(null);

    const listenersRef = useRef(new Set<(status: RuntimeControllerStatus) => void>());

    const setPortal = useCallback((portal?: PortalDescriptor) => {
        setPortalDescriptor(portal);
    }, []);

    const status = useMemo<RuntimeControllerStatus>(() => {
        return {
            activeSpace,
            portal: portalDescriptor,
            isTransitioning,
        };
    }, [activeSpace, isTransitioning, portalDescriptor]);

    useEffect(() => {
        for (const listener of listenersRef.current) {
            listener(status);
        }
    }, [status]);

    const setActiveSpace = useCallback((space: SpaceId) => {
        setIsTransitioning(false);
        setIsPortalActive(false);
        setActiveSpaceState(space);
    }, []);

    const transitionTo = useCallback(
        (space: SpaceId, opts?: { via?: 'portal'; portalId?: string }) => {
            if (space === activeSpace) return;

            if (opts?.via === 'portal') {
                const canUsePortal =
                    !!portalDescriptor?.visible &&
                    portalDescriptor.from === activeSpace &&
                    portalDescriptor.to === space &&
                    (!opts.portalId || portalDescriptor.id === opts.portalId);

                if (canUsePortal) {
                    setIsTransitioning(true);
                    setIsPortalActive(true);
                    return;
                }
            }

            setActiveSpace(space);
        },
        [activeSpace, portalDescriptor, setActiveSpace]
    );

    React.useImperativeHandle(
        ref,
        (): RuntimeControllerHandle => ({
            setActiveSpace,
            transitionTo,
            getStatus: () => status,
            subscribeStatus: (listener) => {
                listenersRef.current.add(listener);
                listener(status);
                return () => {
                    listenersRef.current.delete(listener);
                };
            },
        }),
        [setActiveSpace, status, transitionTo]
    );

    const showPortal = !!portalDescriptor?.visible && portalDescriptor.from === activeSpace;

    return (
        <Runtime cameraConfig={props.cameraConfig} orbitControlsRef={orbitControlsRef} cameraRef={cameraRef} background={props.background}>
            {props.renderSpace({ activeSpace, orbitControlsRef, setPortal })}

            {showPortal && portalDescriptor ? (
                <group position={portalDescriptor.position} rotation={portalDescriptor.rotation} scale={portalDescriptor.scale}>
                    <Portal
                        isActive={isPortalActive}
                        setIsActive={(active) => {
                            setIsPortalActive(active);
                            setIsTransitioning(active);
                        }}
                        orbitControlsRef={orbitControlsRef as unknown as React.RefObject<{ target: THREE.Vector3 } | null>}
                        onTransitionComplete={(active) => {
                            if (!active) {
                                setIsTransitioning(false);
                                setIsPortalActive(false);
                                return;
                            }

                            // Transition finished: commit to the destination space.
                            const destination = portalDescriptor.to;
                            setIsTransitioning(false);
                            setIsPortalActive(false);
                            setActiveSpaceState(destination);
                        }}
                    />
                </group>
            ) : null}
        </Runtime>
    );
});
