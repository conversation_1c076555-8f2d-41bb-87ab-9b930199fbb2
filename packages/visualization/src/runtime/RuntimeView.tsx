'use client';

import type React from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { XR, createXRStore, useXR, type XRStore } from '@react-three/xr';
import * as THREE from 'three';
import { useEffect, useMemo, useRef } from 'react';

export type RuntimeCameraConfig = {
    position: [number, number, number];
    target: [number, number, number];
    fov: number;
};

type OrbitControlsImpl = React.ElementRef<typeof OrbitControls>;

export const runtimeXrStore: XRStore = createXRStore();

function RuntimeControls({
    orbitControlsRef,
    target,
}: {
    orbitControlsRef: React.RefObject<OrbitControlsImpl | null>;
    target: [number, number, number];
}) {
    const session = useXR((state) => state.session);

    return (
        <OrbitControls
            ref={orbitControlsRef}
            target={target}
            makeDefault
            enableDamping
            dampingFactor={0.05}
            enabled={!session}
        />
    );
}

export default function Runtime(props: {
    cameraConfig: RuntimeCameraConfig;
    children: React.ReactNode;
    orbitControlsRef?: React.RefObject<OrbitControlsImpl | null>;
    cameraRef?: React.RefObject<THREE.Camera | null>;
    background?: string;
}) {
    const { cameraConfig, children, background = 'skyblue' } = props;

    const fallbackOrbitControlsRef = useRef<OrbitControlsImpl | null>(null);
    const fallbackCameraRef = useRef<THREE.Camera | null>(null);

    const orbitControlsRef = props.orbitControlsRef ?? fallbackOrbitControlsRef;
    const cameraRef = props.cameraRef ?? fallbackCameraRef;

    const initialCamera = useMemo(
        () => ({ position: cameraConfig.position, fov: cameraConfig.fov }),
        // Only used for initial mount; follow-up updates happen via effect.
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    useEffect(() => {
        const camera = cameraRef.current;
        const controls = orbitControlsRef.current;
        if (!camera || !controls) return;

        camera.position.set(...cameraConfig.position);
        if ('fov' in camera && typeof (camera as THREE.PerspectiveCamera).fov === 'number') {
            (camera as THREE.PerspectiveCamera).fov = cameraConfig.fov;
            (camera as THREE.PerspectiveCamera).updateProjectionMatrix();
        }

        controls.target.set(...cameraConfig.target);
        controls.update();
    }, [cameraConfig, cameraRef, orbitControlsRef]);

    return (
        <Canvas
            style={{ width: '100%', height: '100%' }}
            camera={initialCamera}
            onCreated={({ camera, scene }) => {
                cameraRef.current = camera;
                scene.background = new THREE.Color(background);
            }}
        >
            <XR store={runtimeXrStore}>
                <RuntimeControls orbitControlsRef={orbitControlsRef} target={cameraConfig.target} />
                {children}
            </XR>
        </Canvas>
    );
}
