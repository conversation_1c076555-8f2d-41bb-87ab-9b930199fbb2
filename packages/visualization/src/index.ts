import { ResourceJson } from '@toolproof-npm/schema/';

type ResourceJsonWithIdentity = ResourceJson & {
    extractedData: {
        identity: string | number | boolean;
        name?: string;
        description?: string
    }
};


/*
    Abshishek:

    Can you start with making an R3F component that takes a list of resources and a configuration object (specifying origin, orientation, mesh shape, mesh size, mesh spacing, mesh color, etc.) as props, and renders the passed resources in a ring (like the the type-ring and role-rings in CosmosSpace)?

    Next, can you make a similar R3F component that takes a list of resources and renders them as a line of panels (like the Natural, Job, and Step panels in CosmosSpace)?

    Use the file resourceMap.json to get lists of resources to test with. Extract, for example, the resources at the TYPE-ResourceType key to build a ring of ResourceTypes.

    The components need to show metadata when a mesh is clicked. Then we should show "identity", "name" and "description". Identity is always present, name and description may be missing.

    We'll always work with data of type ResourceJsonWithIdentity.

    Once you have these two components working, we'll publish the visualization package to npm and test it in console/explorer.



    Question to Copilot:

    With this approach, will we be able to transform and animate the meshes in console/explorer?

    Answer:

    Yes — with this approach (export R3F components that render rings/panels from ResourceJsonWithIdentity[] + config props), you’ll still be able to transform and animate the meshes in console/explorer.

    What makes it work (and what to include in the component API):

    Expose refs for imperative animation

    Add groupRef?: React.Ref<THREE.Group> (for the whole ring/panel strip)
    Add itemRefs?: React.MutableRefObject<Record<string, THREE.Object3D | null>> or a callback like onMountItem?: (resourceId, object) => void
    Then console/explorer can GSAP-animate item.position, item.rotation, etc., exactly like your current job/step refs.
    Avoid “fighting React” during animation

    If the component sets position={...} on every render from props, and you animate position imperatively, React can overwrite your animation on re-render.
    Fix: either (a) keep positions stable and animate via refs, or (b) make animation declarative (props drive the animation), or (c) only set initial positions and don’t recompute unless inputs change.
    Put metadata on the actual Three object

    Set mesh.userData.extractedData = { identity, name?, description? } and mesh.userData.resourceId = resource.identity.
    This makes picking/click handlers in console/explorer straightforward, and it also supports external raycasting if you ever need it.
    Click-to-show metadata won’t block animation

    Prefer onResourceClick?: (resource, object, event) => void so console/explorer decides how/where to show the UI (sidebar, overlay, tooltip), without the package “owning” the interaction model.
    So: exporting R3F components is compatible with transforms/animations, as long as the components provide either (1) refs/handles to animate, or (2) props that drive animation declaratively. The ref-based handle is the closest match to how CosmosSpace works today.

*/
