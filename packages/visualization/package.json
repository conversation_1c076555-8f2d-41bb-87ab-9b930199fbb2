{"name": "@toolproof-npm/visualization", "version": "0.1.2", "description": "Visualization for ToolProof data", "keywords": [], "author": "ToolProof Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ToolProof/core.git", "directory": "packages/visualization"}, "homepage": "https://github.com/ToolProof/core#readme", "bugs": {"url": "https://github.com/ToolProof/core/issues"}, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "sideEffects": false, "scripts": {"build": "tsc -b"}, "files": ["dist", "README.md"], "devDependencies": {"@types/node": "^20.19.25", "ts-node": "^10.9.2", "typescript": "^5.9.3", "@types/three": "^0.177.0", "@types/react": "18.3.5", "@types/react-dom": "18.3.0"}, "dependencies": {"@toolproof-npm/schema": "^0.1.68", "@toolproof-npm/shared": "^0.1.100", "three": "^0.177.0", "react": "18.2.0", "react-dom": "18.2.0", "uuid": "^11.1.0"}}