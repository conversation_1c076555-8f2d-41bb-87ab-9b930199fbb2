import dotenv from 'dotenv';
dotenv.config();
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { StatelessStrategyIdentityJson, StatefulStrategyIdentityJson, StatefulStrategyJson } from '@toolproof-npm/schema';
import { Client } from '@langchain/langgraph-sdk';
import { RemoteGraph } from '@langchain/langgraph/remote';
import { HumanMessage } from '@langchain/core/messages';


const urlLocal = `http://localhost:2024`;
const urlRemote = `https://ht-silver-silence-54-960b977fef625b03bcb03971e056e70d.us.langgraph.app`;
const url = urlLocal; //process.env.URL || urlLocal;
const graphId = 'GraphRunStrategy';
const client = new Client({
    apiUrl: url,
});
const remoteGraph = new RemoteGraph({ graphId, url });

export async function runRemoteGraph() {
    try {
        // Create a thread (or use an existing thread instead)
        const thread = await client.threads.create();
        // console.log('thread :', thread);
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 1800000); // 30 minutes
        // console.log('timeout :', timeout);

        const statefulStrategy: StatefulStrategyJson = {
            identity: 'dummy' as StatefulStrategyIdentityJson,
            statelessStrategy: {
                identity: 'dummy' as StatelessStrategyIdentityJson,
                steps: []
            },
            strategyState: {},
        };

        try {
            // console.log('Invoking the graph')
            const result = await remoteGraph.invoke({
                messages: [new HumanMessage('Graph is invoked')],
                dryModeManager: {
                    dryRunMode: true,
                    delay: 1000,
                    drySocketMode: true,
                },
                statefulStrategy
            }, {
                configurable: { thread_id: thread.thread_id },
                signal: controller.signal,
            });

            // console.log('threadId:', thread.thread_id);
            console.log('result:', JSON.stringify(result.messages, null, 2));

            // Check if the graph was interrupted (has interruptData with needsUserInput)
            if (result.interruptData?.needsUserInput) {
                console.log('\n=== GRAPH INTERRUPTED ===');
                console.log('Interrupt Message:', result.interruptData.message);
                console.log('Missing Roles:', result.interruptData.missingRoles);

                // To resume the graph with user input, you would:
                // 1. Get user input (e.g., selected role index)
                // 2. Update the state with the user's selection
                // 3. Resume the graph

                // Example (commented out - implement based on your needs):
                /*
                const userSelection = 1; // User selects the first missing role
                const selectedRole = result.interruptData.missingRoles[userSelection - 1];
                
                // Update the state with user's selection
                await client.threads.updateState(thread.thread_id, {
                    values: {
                        interruptData: {
                            message: `User selected: ${selectedRole.executionId} - ${selectedRole.roleId}`,
                            selectedRole: selectedRole,
                            needsUserInput: false
                        }
                    }
                });
                
                // Resume the graph (invoke again with null to continue from checkpoint)
                const resumedResult = await remoteGraph.invoke(null, {
                    configurable: { thread_id: thread.thread_id },
                    signal: controller.signal,
                });
                console.log('Resumed result:', JSON.stringify(resumedResult.messages, null, 2));
                */
            }

            return result;

        } finally {
            clearTimeout(timeout);
            if (!controller.signal.aborted) {
                controller.abort();
            }
        }

    } catch (error) {
        console.error('Error invoking graph:', error);
    }

}
