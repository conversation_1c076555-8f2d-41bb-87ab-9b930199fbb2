import { GraphStateAnnotationRoot } from './graphState.js';
import { Runnable, RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';
import * as CONSTANTS_LOCAL from './constants.js';


export type GraphState = typeof GraphStateAnnotationRoot.State;

export abstract class BaseNode extends Runnable {
    protected nodeName: string;

    constructor(nodeName: string) {
        super();
        this.nodeName = nodeName;
    }

    lc_namespace = [];

    // Template method - handles common logic
    async invoke(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        // Execute the actual node logic
        return await this.executeNode(state, options);
    }

    // Abstract method for subclasses to implement their specific logic
    protected abstract executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>>;

}


export interface MockModeManagerType {
    enabled: boolean;
}