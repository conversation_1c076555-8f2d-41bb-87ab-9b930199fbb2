import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, Creation<PERSON>ontext<PERSON><PERSON>, ResourcePotentialOutput<PERSON>son, StrategyRun<PERSON>son, JobJson, StepJson, JsonData<PERSON>son } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import * as CONSTANTS_LOCAL from '../constants.js';
import { RESOURCE_CREATION } from '@toolproof-npm/shared';
import { STEP_CREATION } from '@toolproof-npm/shared';
import { bindInputRefInStrategyState, bindInputResInStrategyState } from '../_lib/utils/roleResourceBindingRun.js';
import {
    getExecutionSingleInputRoleRef,
    getExecutionSingleNonErrorOutputCreationContext,
    getRoleRefByInputName,
    seedExecutionOutputPotentials,
} from '../_lib/utils/loopStepRun.js';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


const nodeName = CONSTANTS_LOCAL.NODE_FOR_STEP;

export class NodeForStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        const currentThreadRef = state.currentThreadIdentity;
        const currentStepCounter = currentThreadRef ? (state.stepCounters[currentThreadRef] ?? 0) : 0;

        console.log(`[${this.nodeName}] START - currentThreadIdentity: "${currentThreadRef}", counter: ${currentStepCounter}`);

        // Validate strategyRun exists
        if (!state.strategyRun) {
            throw new Error('strategyRun not initialized in graph state');
        }

        if (!currentThreadRef) {
            throw new Error('currentThreadIdentity not set in graph state');
        }

        const threadSteps = state.strategyRun.strategyThreadMap[currentThreadRef];
        if (!threadSteps) {
            throw new Error(`Thread ${currentThreadRef} not found in strategyRun`);
        }

        const step = threadSteps[currentStepCounter];
        if (step.kind !== CONSTANTS.STEPS.for) {
            throw new Error(`[${nodeName}] Expected a ForStep at counter ${currentStepCounter}`);
        }

        const forStep = step as ForStepJson; // EdgeRouting ensures only ForSteps reach here

            /*
                We extract the whatExecution and whenExecution from the ForStep
            */

            const whatExecution = forStep.case.what.execution;
            const whenExecution = forStep.case.when.execution;

            /*
                We extract a creationContext for the current-step whatExecution's output. 
            */

            const whatSource: CreationContextJson = getExecutionSingleNonErrorOutputCreationContext(whatExecution, nodeName);

            /*
                We clone the forStep itself
            */
            const selfClone = await STEP_CREATION.cloneForStep(forStep);

            /*
                We re-bind the input role of the clone's whatExecution with the output role of the current-step whatExecution.
            */

            const cloneWhatExecution = selfClone.case.what.execution;
            const cloneInputRoleRef = getExecutionSingleInputRoleRef(cloneWhatExecution, nodeName);

            const whatTarget: CreationContextJson = {
                executionRef: cloneWhatExecution.identity,
                resourceRoleRef: cloneInputRoleRef,
            };

            const nextStrategyState = bindInputRefInStrategyState(state.strategyRun.strategyState, whatTarget, whatSource);
        const baseRun: StrategyRunJson = {
            ...state.strategyRun,
            strategyState: nextStrategyState,
        };

            // Bind clone whenExecution inputs:
            const cloneWhenExecution = selfClone.case.when.execution;
            const whenJob = state.jobMap[cloneWhenExecution.jobRef];
            if (!whenJob) {
                throw new Error(`[${nodeName}] Job '${cloneWhenExecution.jobRef}' not found in jobMap`);
            }

            const dynamicRoleRef = getRoleRefByInputName(whenJob, 'DynamicSource', nodeName);
            const staticRoleRef = getRoleRefByInputName(whenJob, 'StaticTarget', nodeName);

            const originalStaticEntry = state.strategyRun.strategyState?.[whenExecution.identity]?.[staticRoleRef];
            if (!originalStaticEntry || originalStaticEntry.kind !== 'materialized') {
                throw new Error(`[${nodeName}] Expected materialized StaticTarget at (${whenExecution.identity}, ${staticRoleRef})`);
            }

            // Seed the *current* whenExecution DynamicSource if the incoming statefulStrategy omitted it.
            // This avoids NodeWorkStep failing with "Missing resource map entry" when executing the inserted whenWorkStep.
            const currentIteration = state.iterationCounters[currentThreadRef] ?? 0;
            const originalDynamicEntry = state.strategyRun.strategyState?.[whenExecution.identity]?.[dynamicRoleRef];
            let baseStrategyState = baseRun.strategyState;
            if (!originalDynamicEntry) {
                const originalIterationResourceRef = whenExecution.roleBindings.inputBindingMap[dynamicRoleRef];
                if (!originalIterationResourceRef) {
                    throw new Error(`[${nodeName}] Missing DynamicSource binding on whenExecution ${whenExecution.identity}`);
                }

                const originalIterationPotential: ResourcePotentialOutputJson = {
                    identity: originalIterationResourceRef,
                    resourceTypeRef: CONSTANTS.SPECIALS.TYPE_Natural,
                    creationContext: {
                        resourceRoleRef: dynamicRoleRef,
                        executionRef: whenExecution.identity,
                    },
                    kind: 'potential-output',
                };

                const originalIterationContent = { identity: currentIteration } satisfies JsonDataJson;
                const originalIterationMaterialized = RESOURCE_CREATION.createMaterializedResource(
                    originalIterationPotential,
                    originalIterationContent
                );

                baseStrategyState = bindInputResInStrategyState(baseStrategyState, {
                    executionRef: whenExecution.identity,
                    resourceRoleRef: dynamicRoleRef,
                }, originalIterationMaterialized);
            }
            const nextIteration = currentIteration + 1;

            const iterationResourceRef = cloneWhenExecution.roleBindings.inputBindingMap[dynamicRoleRef];

            const iterationPotentialOutput: ResourcePotentialOutputJson = {
                identity: iterationResourceRef,
                resourceTypeRef: CONSTANTS.SPECIALS.TYPE_Natural,
                creationContext: {
                    resourceRoleRef: dynamicRoleRef,
                    executionRef: cloneWhenExecution.identity,
                },
                kind: 'potential-output',
            };

            const iterationContent = { identity: nextIteration } satisfies JsonDataJson;
            const iterationMaterialized = RESOURCE_CREATION.createMaterializedResource(iterationPotentialOutput, iterationContent);

            let nextState2 = bindInputResInStrategyState(baseStrategyState, {
                executionRef: cloneWhenExecution.identity,
                resourceRoleRef: dynamicRoleRef,
            }, iterationMaterialized);

            nextState2 = bindInputResInStrategyState(nextState2, {
                executionRef: cloneWhenExecution.identity,
                resourceRoleRef: staticRoleRef,
            }, originalStaticEntry);

            // Seed output potential-outputs for the clone executions so NodeWorkStep can run them.
            const whatJob = state.jobMap[cloneWhatExecution.jobRef];
            if (!whatJob) {
                throw new Error(`[${nodeName}] Job '${cloneWhatExecution.jobRef}' not found in jobMap`);
            }

            nextState2 = seedExecutionOutputPotentials(nextState2, cloneWhatExecution, whatJob, nodeName);
            nextState2 = seedExecutionOutputPotentials(nextState2, cloneWhenExecution, whenJob, nodeName);

            // Persist strategyState and mutate thread steps:
            const nextThreadSteps: StepJson[] = [
                ...threadSteps.slice(0, currentStepCounter + 1),
                forStep.case.what,
                forStep.case.when,
                selfClone,
                ...threadSteps.slice(currentStepCounter + 1),
            ];

        const updatedStrategyRun: StrategyRunJson = {
            ...baseRun,
            strategyState: nextState2,
            strategyThreadMap: {
                ...baseRun.strategyThreadMap,
                [currentThreadRef]: nextThreadSteps,
            },
        };

            // Advance counters: next step is the inserted whatWorkStep
        return {
            strategyRun: updatedStrategyRun,
            iterationCounters: { [currentThreadRef]: nextIteration },
            stepCounters: { [currentThreadRef]: currentStepCounter + 1 },
            currentThreadIdentity: currentThreadRef,
        };
    }

}


