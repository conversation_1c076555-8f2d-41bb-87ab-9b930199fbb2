import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import * as CONSTANTS_LOCAL from '../constants.js';

const nodeName = CONSTANTS_LOCAL.NODE_INTERRUPT_CHECK;

/**
 * Node that checks for missing role kinds in strategyState and sets up interrupt data
 */
export class NodeInterruptCheck extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {
        console.log('NodeInterruptCheck: Checking for missing role kinds...');

        // Check if we have strategyState
        if (!state.statefulStrategy?.strategyState) {
            console.log('NodeInterruptCheck: No strategyState found, continuing...');
            return {};
        }

        const strategyState = state.statefulStrategy.strategyState;
        const missingRoles: Array<{
            executionRef: string;
            roleRef: string;
            resourceTypeRef?: string;
        }> = [];

        // Iterate through all executions and roles to find missing kinds
        for (const [executionRef, roleMap] of Object.entries(strategyState)) {
            for (const [roleRef, resource] of Object.entries(roleMap)) {
                // Use type assertion to check for missing kind
                if ((resource as any).kind === 'missing') {
                    missingRoles.push({
                        executionRef,
                        roleRef,
                        resourceTypeRef: (resource as any).resourceTypeRef
                    });
                }
            }
        }

        if (missingRoles.length > 0) {
            console.log(`NodeInterruptCheck: Found ${missingRoles.length} missing roles:`, missingRoles);

            // Create a user-friendly message for the interrupt
            const missingRolesList = missingRoles.map((role, index) =>
                `${index + 1}. Execution: ${role.executionRef}, Role: ${role.roleRef}, Type: ${role.resourceTypeRef || 'Unknown'}`
            ).join('\n');

            const interruptMessage = `Found missing role kinds that need user input:\n\n${missingRolesList}\n\nPlease select a number (1-${missingRoles.length}) to provide input for:`;

            // Store the interrupt data in state instead of calling interrupt()
            // The graph will pause after this node due to interruptAfter configuration
            console.log('NodeInterruptCheck: Storing interrupt data in state');
            return {
                interruptData: {
                    message: interruptMessage,
                    missingRoles: missingRoles,
                    needsUserInput: true
                }
            };
        } else {
            console.log('NodeInterruptCheck: No missing roles found, continuing...');
            return {};
        }
    }
}
