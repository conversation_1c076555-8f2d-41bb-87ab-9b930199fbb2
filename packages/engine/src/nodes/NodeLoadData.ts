import * as CONSTANTS_LOCAL from '../constants.js';
import { listResources } from '@toolproof-npm/shared/server';
import { extractJobMap } from '@toolproof-npm/shared/utils';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


const nodeName = CONSTANTS_LOCAL.NODE_LOAD_DATA;


export class NodeLoadData extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        try {
            console.log(`[${this.nodeName}] START`);
            // console.log("GOOGLE_APPLICATION_CREDENTIALS_JSON : ", process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);
            const asyncWrapper = async () => {

                const resourceMap = await listResources(
                    [
                        'TYPE-Job',
                        'TYPE-Natural',
                    ]
                );

                return resourceMap;
            };

            const resourceMap = await asyncWrapper();

            const jobMap = Object.fromEntries(extractJobMap(resourceMap));

            return {
                messages: [new AIMessage(`${nodeName} completed successfully`)],
                jobMap
            };
        } catch (error: any) {
            console.error(`Error in ${nodeName}:`, error);
            throw new Error(`Error in ${nodeName}: ${error.message}`);
        }
    }

}



