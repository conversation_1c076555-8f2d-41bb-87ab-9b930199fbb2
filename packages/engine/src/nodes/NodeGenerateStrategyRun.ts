import { CONSTANTS } from '@toolproof-npm/shared/constants';
import * as CONSTANTS_LOCAL from '../constants.js';
import { getNewIdentity } from '@toolproof-npm/shared/server';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';
import type { StrategyRunJson, StatefulStrategyIdentityJson } from '@toolproof-npm/schema';


const nodeName = CONSTANTS_LOCAL.NODE_GENERATE_STRATEGY_RUN;


export class NodeGenerateStrategyRun extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {
        console.log(`[${this.nodeName}] START`);
        const strategyRunIdentity = getNewIdentity(CONSTANTS.TERMINALS.strategy_run);

        const steps = state.statefulStrategy.statelessStrategy.steps;
        const strategyState = state.statefulStrategy.strategyState;

        // Algorithm to find what steps can run in parallel
        const threadStepsGroups = this.getIndependentThreads(steps, strategyState);

        const strategyThreadMap: Record<string, any[]> = {};
        threadStepsGroups.forEach((group, index) => {
            const threadIdentity = getNewIdentity(CONSTANTS.TERMINALS.strategy_thread);
            strategyThreadMap[threadIdentity] = group;
        });

        // Create strategyRun from statefulStrategy
        const strategyRun: StrategyRunJson = {
            identity: strategyRunIdentity as any,
            statefulStrategyRef: state.statefulStrategy.identity as StatefulStrategyIdentityJson,
            strategyState: state.statefulStrategy.strategyState,
            strategyRunContext: {
                status: 'running'
            },
            strategyThreadMap
        } as any; // Cast as any to handle potential schema mismatch during transition

        return {
            messages: [new AIMessage(`${nodeName} completed with ${threadStepsGroups.length} parallel threads`)],
            strategyRun,
            currentThreadIdentity: "" // Reset so EdgeRouting can fan out
        };
    }

    private getIndependentThreads(steps: any[], strategyState: any): any[][] {
        const getStepId = (s: any, i: number) => s.execution?.identity || `step-${i}`;
        const stepById = new Map(steps.map((s, i) => [getStepId(s, i), s]));
        const adj = new Map<string, string[]>(); // undirected graph for components

        steps.forEach((step, i) => {
            const id = getStepId(step, i);
            if (!adj.has(id)) adj.set(id, []);

            if (step.kind === CONSTANTS.STEPS.work && step.execution) {
                const inputBindingMap = step.execution.roleBindings.inputBindingMap;
                for (const inputRoleId of Object.keys(inputBindingMap)) {
                    const entry = strategyState[id]?.[inputRoleId];
                    if (entry?.kind === 'potential-input') {
                        const creatorId = entry.creationContext?.executionRef;
                        if (creatorId && stepById.has(creatorId)) {
                            // Dependency exists
                            adj.get(id)!.push(creatorId);
                            if (!adj.has(creatorId)) adj.set(creatorId, []);
                            adj.get(creatorId)!.push(id);
                        }
                    }
                }
            }
        });

        const visited = new Set<string>();
        const components: string[][] = [];

        for (const [id] of adj) {
            if (!visited.has(id)) {
                const component: string[] = [];
                const queue = [id];
                visited.add(id);
                while (queue.length > 0) {
                    const node = queue.shift()!;
                    component.push(node);
                    for (const neighbor of adj.get(node) || []) {
                        if (!visited.has(neighbor)) {
                            visited.add(neighbor);
                            queue.push(neighbor);
                        }
                    }
                }
                components.push(component);
            }
        }

        return components.map(compIds => {
            return steps.filter((s, i) => {
                const sId = getStepId(s, i);
                return compIds.includes(sId);
            });
        }).filter(group => group.length > 0);
    }

}



