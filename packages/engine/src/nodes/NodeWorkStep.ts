import type {
    Execution<PERSON><PERSON><PERSON><PERSON><PERSON>,
    Json<PERSON>ata<PERSON>son,
    ResourceMissingJson,
    ResourcePotentialInputJson,
    ResourcePotentialOutputJson,
    ResourceJson,
    ResourceRoleIdentityJson,
    StrategyRunJson,
    StrategyStateJson,
    WorkStepJson,
} from '@toolproof-npm/schema';
import { RESOURCE_CREATION, RESOURCE_CHAIN } from '@toolproof-npm/shared';
import * as CONSTANTS_LOCAL from '../constants.js';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';
import axios from 'axios';
import { executeMockNumericalJob } from '../_lib/internals/numericalMockJobs.js';
import { executeLessThanJob } from '../_lib/internals/lessThanJob.js';

const nodeName = CONSTANTS_LOCAL.NODE_Work_Step;

type ResumeValue = { identity: number; path: string; key: string };

function isResumeValue(value: unknown): value is ResumeValue {
    if (!value || typeof value !== 'object') return false;
    const obj = value as Record<string, unknown>;
    return typeof obj.identity === 'number' && typeof obj.path === 'string' && typeof obj.key === 'string';
}


export class NodeWorkStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {
        const currentThreadRef = state.currentThreadIdentity;
        const currentStepCounter = currentThreadRef ? (state.stepCounters[currentThreadRef] ?? 0) : 0;

        console.log(`[${this.nodeName}] START - currentThreadIdentity: "${currentThreadRef}", counter: ${currentStepCounter}`);

        try {
            const resumeValue = (() => {
                const configurable = options?.configurable as unknown as
                    | { __pregel_scratchpad?: { nullResume?: unknown } }
                    | undefined;
                const maybe = configurable?.__pregel_scratchpad?.nullResume;
                return isResumeValue(maybe) ? maybe : undefined;
            })();

            state.interruptData = null;

            // Validate strategyRun exists
            if (!state.strategyRun) {
                throw new Error('strategyRun not initialized in graph state');
            }

            if (!currentThreadRef) {
                throw new Error('currentThreadIdentity not set in graph state');
            }

            const threadSteps = state.strategyRun.strategyThreadMap[currentThreadRef];
            if (!threadSteps) {
                throw new Error(`Thread ${currentThreadRef} not found in strategyRun`);
            }

            const workStep = threadSteps[currentStepCounter] as WorkStepJson; // safe to assert as WorkStepJson because EdgeRouting ensures that only WorkSteps reach here
            const execution = workStep.execution;

            const inputBindingMap = execution.roleBindings.inputBindingMap;
            const outputBindingMap = execution.roleBindings.outputBindingMap;
            const strategyState = state.strategyRun.strategyState;

            const job = state.jobMap[execution.jobRef];
            if (!job) {
                throw new Error(`Job with ID ${execution.jobRef} not found in jobMap`);
            }

            const payload: Record<string, ResourceJson | ResourcePotentialOutputJson> = {};

            // Resolve inputs
            for (const inputRoleRef of Object.keys(inputBindingMap) as ResourceRoleIdentityJson[]) {
                const inputRoleName = job.roles.inputMap[inputRoleRef].name;
                const entry = strategyState[execution.identity]?.[inputRoleRef] as
                    | ResourceJson
                    | ResourceMissingJson
                    | ResourcePotentialInputJson
                    | ResourcePotentialOutputJson
                    | undefined;
                if (!entry) throw new Error(`Missing resource map entry for input role '${inputRoleRef}' in execution '${execution.identity}'`);

                if (entry.kind === 'missing') {
                    // Check if this resource is already materialized elsewhere in the state
                    let alreadyMaterialized: ResourceJson | undefined;
                    for (const execRef of Object.keys(strategyState) as ExecutionIdentityJson[]) {
                        for (const roleRef of Object.keys(strategyState[execRef]) as ResourceRoleIdentityJson[]) {
                            const otherEntry = strategyState[execRef][roleRef];
                            if (otherEntry.identity === entry.identity && otherEntry.kind === 'materialized') {
                                alreadyMaterialized = otherEntry as ResourceJson;
                                break;
                            }
                        }
                        if (alreadyMaterialized) break;
                    }

                    if (alreadyMaterialized) {
                        strategyState[execution.identity][inputRoleRef] = alreadyMaterialized;
                        payload[inputRoleName] = alreadyMaterialized;
                        continue;
                    }

                    if (!resumeValue || resumeValue.key !== entry.identity) {
                        const interruptContext = {
                            executionRef: execution.identity,
                            roleRef: inputRoleRef,
                            resourceTypeRef: entry.resourceTypeRef,
                            needsUserInput: true,
                            key: entry.identity,
                            isInterrupt: true
                        };
                        return {
                            interruptData: {
                                message: `Please provide a resource for role '${inputRoleRef}' in execution '${execution.identity}'.`,
                                ...interruptContext
                            }
                        };
                    }

                    // ▶ Resume path: materialize resource
                    const missingEntry = entry;
                    const materializedEntry: ResourceJson = {
                        identity: missingEntry.identity,
                        resourceTypeRef: missingEntry.resourceTypeRef,
                        kind: 'materialized',
                        creationContext: {
                            resourceRoleRef: inputRoleRef,
                            executionRef: execution.identity
                        },
                        path: resumeValue.path,
                        timestamp: new Date().toISOString(),
                        extractedData: {
                            identity: resumeValue.identity
                        }
                    };

                    // Update all occurrences of this missing resource identity across the whole strategy state
                    for (const execRef of Object.keys(strategyState) as ExecutionIdentityJson[]) {
                        for (const roleRef of Object.keys(strategyState[execRef]) as ResourceRoleIdentityJson[]) {
                            const res = strategyState[execRef][roleRef];
                            if (res && res.kind === 'missing' && res.identity === materializedEntry.identity) {
                                strategyState[execRef][roleRef] = materializedEntry;
                            }
                        }
                    }

                    payload[inputRoleName] = materializedEntry;
                    continue;
                }

                if (entry.kind === 'materialized') {
                    payload[inputRoleName] = entry;
                    continue;
                }
                if (entry.kind === 'potential-input') {
                    const result = RESOURCE_CHAIN.resolveResourceChain(strategyState, entry.creationContext);
                    if (result.status === 'materialized') {
                        payload[inputRoleName] = result.entry;
                        continue;
                    }
                    // Blocked or unresolved: halt execution gracefully
                    throw new Error(`Cannot realize input role '${inputRoleRef}': ${result.status === 'potential-output' ? 'blocked by future output' : 'unresolved chain (' + result.path + ')'} `);
                }
                if (entry.kind === 'potential-output') {
                    throw new Error(`Input role '${inputRoleRef}' unexpectedly bound to potential-output in same execution.`);
                }
                throw new Error(`Unsupported resource kind for input role '${inputRoleRef}'`);
            }

            // Pass output potentials to the job (job will materialize them and return complete resources)
            for (const outputRoleRef of Object.keys(outputBindingMap) as ResourceRoleIdentityJson[]) {
                const outputRoleName = job.roles.outputMap[outputRoleRef].name;
                const pot = strategyState[execution.identity]?.[outputRoleRef] as (ResourcePotentialOutputJson | undefined);
                if (!pot || pot.kind !== 'potential-output') {
                    throw new Error(`Expected potential-output entry for output role '${outputRoleRef}'`);
                }
                payload[outputRoleName] = pot;
            }

            // console.log('payload:', JSON.stringify(payload, null, 2));

            const mockModeEnabled = state.mockModeManager?.enabled ?? false;
            let roleNameOutputMap: Record<string, ResourceJson>;

            const isInternalLessThan =
                execution.jobRef === 'JOB-LessThan' ||
                job.name === 'LessThan';

            if (isInternalLessThan) {
                roleNameOutputMap = executeLessThanJob(payload);
            } else if (mockModeEnabled) {
                // Use mock numerical jobs instead of remote HTTP calls
                // Note: job is already the extractedData (JobJson), not the resource envelope
                const jobName = job.name ?? '';
                console.log(`[MOCK MODE] Executing mock job: ${jobName}`, { payloadKeys: Object.keys(payload) });
                try {
                    roleNameOutputMap = executeMockNumericalJob(jobName, payload);
                    console.log(`[MOCK MODE] Job returned:`, { outputKeys: Object.keys(roleNameOutputMap), outputs: roleNameOutputMap });
                } catch (error) {
                    throw new Error(`Mock job execution failed: ${(error as Error).message}`);
                }
            } else {
                // Normal remote job execution
                const asyncWrapper = async (url: string): Promise<Record<string, ResourceJson>> => {

                    const response = await axios.post(
                        url,
                        payload,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            timeout: 30 * 60 * 1000, // 30 minutes in milliseconds
                        }
                    );

                    const result = response.data;

                    // console.log('result:', JSON.stringify(result, null, 2));

                    return result.outputMap;
                }

                roleNameOutputMap = await asyncWrapper(
                    job.implementationUri
                );
            }

            // Build reverse lookup map from role name to role ID
            const roleNameToRoleRefMap = new Map(
                Object.entries(job.roles.outputMap).map(([roleRef, role]) => [role.name, roleRef as ResourceRoleIdentityJson])
            );

            const resourceRoleRefOutputMap: Record<ResourceRoleIdentityJson, ResourceJson> = {};

            // Map back from role names to role IDs
            for (const [roleName, output] of Object.entries(roleNameOutputMap)) {
                const roleRef = roleNameToRoleRefMap.get(roleName);
                if (!roleRef) {
                    throw new Error(`Output role name '${roleName}' not found in job roles`);
                }
                resourceRoleRefOutputMap[roleRef] = output;
            }

            // Update resourceMap with materialized outputs
            const updatedStrategyState: StrategyStateJson = { ...strategyState };
            const execBucket = { ...updatedStrategyState[execution.identity] };

            const ERROR_ROLE_REF = 'ROLE-ErrorOutput' as ResourceRoleIdentityJson;
            const errorOutputFromJob = resourceRoleRefOutputMap[ERROR_ROLE_REF];

            // Enforce output contract:
            // - If ErrorOutput materializes => fail whole run
            // - Else all non-error bound outputs must materialize
            const expectedOutputRoleRefs = Object.keys(outputBindingMap) as ResourceRoleIdentityJson[];
            const nonErrorExpectedRoleRefs = expectedOutputRoleRefs.filter((roleRef) => roleRef !== ERROR_ROLE_REF);
            const missingNonErrorRoleRefs = nonErrorExpectedRoleRefs.filter((roleRef) => !resourceRoleRefOutputMap[roleRef]);

            if (!errorOutputFromJob && missingNonErrorRoleRefs.length > 0) {
                // Contract violation: job omitted required outputs AND did not emit ErrorOutput.
                const errorPot = strategyState[execution.identity]?.[ERROR_ROLE_REF] as ResourcePotentialOutputJson | undefined;
                if (!errorPot || errorPot.kind !== 'potential-output') {
                    throw new Error(
                        `ContractViolation: missing potential-output placeholder for '${ERROR_ROLE_REF}' in execution '${execution.identity}'`
                    );
                }

                const missingRoleNames = missingNonErrorRoleRefs.map((roleRef) => job.roles.outputMap[roleRef]?.name ?? roleRef);

                const content: JsonDataJson = {
                    name: 'ContractViolation',
                    description: `Job execution omitted required outputs (${missingRoleNames.join(', ')}) without emitting ${ERROR_ROLE_REF}.`,
                    details: {
                        executionRef: execution.identity,
                        jobRef: execution.jobRef,
                        missingOutputRoleIds: missingNonErrorRoleRefs,
                        missingOutputRoleNames: missingRoleNames,
                    }
                };

                const synthesizedError = RESOURCE_CREATION.createMaterializedResource(errorPot, content);
                resourceRoleRefOutputMap[ERROR_ROLE_REF] = synthesizedError;
            }

            for (const outputRoleRef of Object.keys(outputBindingMap) as ResourceRoleIdentityJson[]) {
                const materializedResource = resourceRoleRefOutputMap[outputRoleRef];
                if (!materializedResource) continue;

                execBucket[outputRoleRef] = materializedResource;
            }
            updatedStrategyState[execution.identity] = execBucket;

            console.log('updatedStrategyState:', JSON.stringify(updatedStrategyState, null, 2));

            // Preserve all thread map entries while updating strategyState
            const updatedStrategyRun: StrategyRunJson = {
                ...state.strategyRun,
                strategyState: updatedStrategyState,
            };

            // If error output materialized (either from job or synthesized ContractViolation), fail the whole run.
            if (resourceRoleRefOutputMap[ERROR_ROLE_REF]) {
                const failedStrategyRun: StrategyRunJson = {
                    ...updatedStrategyRun,
                    strategyRunContext: {
                        ...updatedStrategyRun.strategyRunContext,
                        status: 'failed',
                    },
                };

                return {
                    messages: [new AIMessage(`${nodeName} failed`)],
                    strategyRun: failedStrategyRun,
                    // Keep step counter at failing step
                    stepCounters: { [currentThreadRef]: currentStepCounter },
                    currentThreadIdentity: currentThreadRef
                };
            }

            console.log('Step complete:', currentStepCounter);
            return {
                messages: [new AIMessage(`${nodeName} completed`)],
                strategyRun: updatedStrategyRun,
                stepCounters: { [currentThreadRef]: currentStepCounter + 1 },
                currentThreadIdentity: currentThreadRef // Explicitly return this to ensure it persists in merged state updates
            };

        } catch (error: unknown) {
            // Structured logging to help diagnose HTTP / Axios errors (EROLE_BAD_REQUEST, etc.)
            try {
                // Use Axios config data (if present) rather than referencing local variables that
                // may be out of scope when the catch runs.
                const err = error as {
                    message?: unknown;
                    code?: unknown;
                    response?: { status?: unknown; data?: unknown };
                    config?: { url?: unknown; data?: unknown };
                };
                const payloadPreview = err?.config?.data ?? null;
                console.error(`Error in ${nodeName}:`, {
                    message: err?.message,
                    code: err?.code,
                    status: err?.response?.status,
                    responseData: err?.response?.data,
                    requestUrl: err?.config?.url,
                    payload: payloadPreview,
                });
            } catch (logErr) {
                // Fallback if structured logging throws
                console.error(`Error in ${nodeName} (logging failed):`, error);
            }

            return {
                messages: [new AIMessage(`${nodeName} failed`)],
                strategyRun: {
                    ...state.strategyRun,
                    strategyRunContext: {
                        ...state.strategyRun.strategyRunContext,
                        status: 'failed',
                    },
                },
                // Keep step counter at failing step
                stepCounters: { [currentThreadRef]: currentStepCounter },
                currentThreadIdentity: currentThreadRef
            };
        }
    }
}



