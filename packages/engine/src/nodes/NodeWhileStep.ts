import * as CONSTANTS_LOCAL from '../constants.js';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, WhileStep<PERSON>son } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { STEP_CREATION } from '@toolproof-npm/shared';
import { bindInputRefInStrategyState, bindInputResInStrategyState } from '../_lib/utils/roleResourceBindingRun.js';
import {
    getExecutionSingleInputRoleRef,
    getExecutionSingleNonErrorOutputCreationContext,
    getRoleRefByInputName,
    seedExecutionOutputPotentials,
} from '../_lib/utils/loopStepRun.js';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';


const nodeName = CONSTANTS_LOCAL.NODE_WHILE_STEP;


export class NodeWhileStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        const currentThreadRef = state.currentThreadIdentity;
        const currentStepCounter = currentThreadRef ? (state.stepCounters[currentThreadRef] ?? 0) : 0;

        console.log(`[${this.nodeName}] START - currentThreadIdentity: "${currentThreadRef}", counter: ${currentStepCounter}`);

        // Validate strategyRun exists
        if (!state.strategyRun) {
            throw new Error('strategyRun not initialized in graph state');
        }

        if (!currentThreadRef) {
            throw new Error('currentThreadIdentity not set in graph state');
        }

        const threadSteps = state.strategyRun.strategyThreadMap[currentThreadRef];
        if (!threadSteps) {
            throw new Error(`Thread ${currentThreadRef} not found in strategyRun`);
        }

        const step = threadSteps[currentStepCounter];
        if (step.kind !== CONSTANTS.STEPS.while) {
            throw new Error(`[${nodeName}] Expected a WhileStep at counter ${currentStepCounter}`);
        }

        const whileStep = step as WhileStepJson; // EdgeRouting ensures only WhileSteps reach here

        const whatExecution = whileStep.case.what.execution;
        const whenExecution = whileStep.case.when.execution;

        // The output of the current-step whatExecution drives the LessThan DynamicSource.
        const whatSource: CreationContextJson = getExecutionSingleNonErrorOutputCreationContext(whatExecution, nodeName);

        // Clone the WhileStep (same cloning behavior as ForStep for now).
        const selfClone = await STEP_CREATION.cloneWhileStep(whileStep);

        // Bind clone.what input to current.what output.
        const cloneWhatExecution = selfClone.case.what.execution;
        const cloneInputRoleRef = getExecutionSingleInputRoleRef(cloneWhatExecution, nodeName);
        const whatTarget: CreationContextJson = {
            executionRef: cloneWhatExecution.identity,
            resourceRoleRef: cloneInputRoleRef,
        };

        const nextStrategyState = bindInputRefInStrategyState(state.strategyRun.strategyState, whatTarget, whatSource);
        const baseRun: StrategyRunJson = {
            ...state.strategyRun,
            strategyState: nextStrategyState,
        };

        // Bind whenExecution inputs (LessThan).
        const cloneWhenExecution = selfClone.case.when.execution;
        const whenJob = state.jobMap[cloneWhenExecution.jobRef] as JobJson | undefined;
        if (!whenJob) {
            throw new Error(`[${nodeName}] Job '${cloneWhenExecution.jobRef}' not found in jobMap`);
        }

        const dynamicRoleRef = getRoleRefByInputName(whenJob, 'DynamicSource', nodeName);
        const staticRoleRef = getRoleRefByInputName(whenJob, 'StaticTarget', nodeName);

        const originalStaticEntry = state.strategyRun.strategyState?.[whenExecution.identity]?.[staticRoleRef];
        if (!originalStaticEntry || originalStaticEntry.kind !== 'materialized') {
            throw new Error(`[${nodeName}] Expected materialized StaticTarget at (${whenExecution.identity}, ${staticRoleRef})`);
        }

        // Ensure the *current* whenExecution has DynamicSource wired up to current what output.
        // Without this, the inserted whenWorkStep would hit NodeWorkStep's "Missing resource map entry" error.
        const originalDynamicEntry = state.strategyRun.strategyState?.[whenExecution.identity]?.[dynamicRoleRef];
        let baseStrategyState = baseRun.strategyState;
        if (!originalDynamicEntry) {
            baseStrategyState = bindInputRefInStrategyState(
                baseStrategyState,
                {
                    executionRef: whenExecution.identity,
                    resourceRoleRef: dynamicRoleRef,
                },
                whatSource
            );
        }

        // Seed clone.what outputs before binding clone.when DynamicSource -> clone.what output.
        // bindInputRefInStrategyState requires the source resourceEntry to exist.
        const whatJob = state.jobMap[cloneWhatExecution.jobRef] as JobJson | undefined;
        if (!whatJob) {
            throw new Error(`[${nodeName}] Job '${cloneWhatExecution.jobRef}' not found in jobMap`);
        }

        baseStrategyState = seedExecutionOutputPotentials(baseStrategyState, cloneWhatExecution, whatJob, nodeName);

        // Bind clone.when DynamicSource to clone.what output (so each iteration checks that iteration's value).
        const cloneWhatSource: CreationContextJson = getExecutionSingleNonErrorOutputCreationContext(cloneWhatExecution, nodeName);
        let nextState2 = bindInputRefInStrategyState(
            baseStrategyState,
            {
                executionRef: cloneWhenExecution.identity,
                resourceRoleRef: dynamicRoleRef,
            },
            cloneWhatSource
        );

        // StaticTarget stays constant across iterations.
        nextState2 = bindInputResInStrategyState(
            nextState2,
            {
                executionRef: cloneWhenExecution.identity,
                resourceRoleRef: staticRoleRef,
            },
            originalStaticEntry
        );

        // Seed output potentials for the clone executions so NodeWorkStep can run them.
        nextState2 = seedExecutionOutputPotentials(nextState2, cloneWhatExecution, whatJob, nodeName);
        nextState2 = seedExecutionOutputPotentials(nextState2, cloneWhenExecution, whenJob, nodeName);

        // Mutate thread steps: insert current what, current when, and the cloned WhileStep.
        const nextThreadSteps: StepJson[] = [
            ...threadSteps.slice(0, currentStepCounter + 1),
            whileStep.case.what,
            whileStep.case.when,
            selfClone,
            ...threadSteps.slice(currentStepCounter + 1),
        ];

        const updatedStrategyRun: StrategyRunJson = {
            ...baseRun,
            strategyState: nextState2,
            strategyThreadMap: {
                ...baseRun.strategyThreadMap,
                [currentThreadRef]: nextThreadSteps,
            },
        };

        // Advance counter: next step is the inserted whatWorkStep.
        return {
            strategyRun: updatedStrategyRun,
            stepCounters: { [currentThreadRef]: currentStepCounter + 1 },
            currentThreadIdentity: currentThreadRef,
        };
    }

}