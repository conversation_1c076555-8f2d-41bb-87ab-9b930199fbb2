import type { <PERSON><PERSON><PERSON>, StatefulStrategy<PERSON>son, StrategyRunJson } from '@toolproof-npm/schema';
import type { MockModeManagerType } from './types.js';
import { Annotation, MessagesAnnotation } from '@langchain/langgraph';


export const GraphStateAnnotationRoot = Annotation.Root({
    ...MessagesAnnotation.spec,
    mockModeManager: Annotation<MockModeManagerType>(
        {
            reducer: (prev, next) => next,
            default: () => ({
                enabled: false,
            }),
        }
    ),
    statefulStrategy: Annotation<StatefulStrategyJson>({
        reducer: (prev, next) => next || prev,
        default: () => ({} as any)
    }),
    strategyRun: Annotation<StrategyRunJson>({
        reducer: (prev, next) => {
            if (!prev) return next;
            return {
                ...prev,
                ...next,
                strategyState: {
                    ...(prev.strategyState ?? {}),
                    ...(next.strategyState ?? {}),
                },
                strategyRunContext: {
                    ...(prev.strategyRunContext ?? {}),
                    ...(next.strategyRunContext ?? {}),
                },
                strategyThreadMap: {
                    ...(prev.strategyThreadMap ?? {}),
                    ...(next.strategyThreadMap ?? {}),
                },
            };
        }
    }),
    jobMap: Annotation<Record<string, JobJson>>({
        reducer: (prev, next) => ({ ...prev, ...next }),
        default: () => ({})
    }),
    currentThreadIdentity: Annotation<string>({
        // Important: allow explicit reset to "" so EdgeRouting can fan out.
        // Using `next || prev` breaks resets because "" is falsy.
        reducer: (prev, next) => (next === undefined ? prev : next),
        default: () => ""
    }),
    // Use an object to store counters per thread identity to avoid race conditions in parallel execution
    stepCounters: Annotation<Record<string, number>>({
        reducer: (prev, next) => ({ ...prev, ...next }),
        default: () => ({})
    }),
    iterationCounters: Annotation<Record<string, number>>({
        reducer: (prev, next) => ({ ...prev, ...next }),
        default: () => ({})
    }),
    interruptData: Annotation<{
        message: string;
        missingRoles?: Array<{
            executionRef: string;
            roleRef: string;
            resourceTypeRef?: string;
        }>;
        selectedRole?: {
            executionRef: string;
            roleRef: string;
            resourceTypeRef?: string;
        } | null;
        needsUserInput?: boolean;
        isInterrupt?: boolean;
        executionRef?: string;
        roleRef?: string;
        resourceTypeRef?: string;
    } | null>(
        {
            reducer: (prev, next) => next,
            default: () => null
        }
    ),
});