import * as CONSTANTS_LOCAL from '../constants.js';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { GraphStateAnnotationRoot } from '../graphState.js';
import { NodeGenerateStrategyRun } from '../nodes/NodeGenerateStrategyRun.js';
import { NodeLoadData } from '../nodes/NodeLoadData.js';
import { NodeWorkStep } from '../nodes/NodeWorkStep.js';
import { NodeBranchStep } from '../nodes/NodeBranchStep.js';
import { NodeWhileStep } from '../nodes/NodeWhileStep.js';
import { NodeForStep } from '../nodes/NodeForStep.js';
import { GraphState } from '../types.js';
import { StateGraph, START, END, MemorySaver, Send } from '@langchain/langgraph';

import type {
    <PERSON><PERSON><PERSON>,
    ResourceRoleIdentity<PERSON><PERSON>,
    Step<PERSON><PERSON>,
    StrategyRun<PERSON>son,
    StrategyState<PERSON>son,
    WorkStepJson,
} from '@toolproof-npm/schema';

function getNextNodeForStep(step: StepJson): string {
    if (step.kind === CONSTANTS.STEPS.work) return CONSTANTS_LOCAL.NODE_Work_Step;
    if (step.kind === CONSTANTS.STEPS.branch) return CONSTANTS_LOCAL.NODE_BRANCH_STEP;
    if (step.kind === CONSTANTS.STEPS.while) return CONSTANTS_LOCAL.NODE_WHILE_STEP;
    if (step.kind === CONSTANTS.STEPS.for) return CONSTANTS_LOCAL.NODE_FOR_STEP;
    return END;
}

function getDecisionRoleRef(whenJob: JobJson): ResourceRoleIdentityJson {
    const decisionRoleRefs = (Object.entries(whenJob.roles.outputMap) as Array<[
        ResourceRoleIdentityJson,
        { name: string }
    ]>)
        .filter(([roleRef, role]) => role.name === 'Decision' && roleRef !== CONSTANTS.SPECIALS.ROLE_ErrorOutput);

    if (decisionRoleRefs.length !== 1) {
        throw new Error(
            `Expected exactly one non-error output role named 'Decision' for job '${whenJob.identity}'; got ${decisionRoleRefs.length}`
        );
    }

    return decisionRoleRefs[0][0];
}

function isLessThanWorkStep(work: WorkStepJson, jobMap: Record<string, JobJson>): { job: JobJson; isLessThan: boolean } {
    const job = jobMap[work.execution.jobRef];
    if (!job) {
        throw new Error(`Job '${work.execution.jobRef}' not found in jobMap`);
    }

    const isLessThan = work.execution.jobRef === 'JOB-LessThan' || job.name === 'LessThan';
    return { job, isLessThan };
}

function readMaterializedBooleanDecision(
    strategyState: StrategyStateJson,
    whenExecution: WorkStepJson['execution'],
    whenJob: JobJson
): boolean {
    const decisionRoleRef = getDecisionRoleRef(whenJob);
    const entry = strategyState[whenExecution.identity]?.[decisionRoleRef];
    if (!entry || entry.kind !== 'materialized') {
        throw new Error(`Expected materialized Decision at (${whenExecution.identity}, ${decisionRoleRef})`);
    }

    const decision = entry.extractedData?.identity;
    if (typeof decision !== 'boolean') {
        throw new Error(`Expected boolean extractedData.identity for Decision at (${whenExecution.identity}, ${decisionRoleRef})`);
    }

    return decision;
}



const EdgeRouting = (state: GraphState) => {
    try {
        // console.log('EgeRouting', JSON.stringify([...state.dataLib.jobs], null, 2));

        // return END;

        if (state.interruptData?.isInterrupt) {
            state.interruptData.isInterrupt = false;
            return CONSTANTS_LOCAL.NODE_INTERRUPT;
        }

    // Validate strategyRun exists
    if (!state.strategyRun) {
        console.warn('[EdgeRouting] strategyRun not initialized in graph state - returning END');
        return END;
    }

    // Halt whole run on failure
    if (state.strategyRun.strategyRunContext?.status === 'failed') {
        console.warn('[EdgeRouting] strategyRunContext.status is "failed" - returning END');
        return END;
    }

    if (!state.currentThreadIdentity) {
        // Handle initial fan-out to parallel threads
        const threadRefs = Object.keys(state.strategyRun.strategyThreadMap);
        if (threadRefs.length === 0) {
            return END;
        }

        // Return Send objects to fork the execution for each thread
        return threadRefs.map(threadRef => {
            const threadSteps = state.strategyRun.strategyThreadMap[threadRef];
            return new Send(getNextNodeForStep(threadSteps[0]), {
                ...state, // Pass the current state to preserve shared values like jobMap, strategyRun, etc.
                currentThreadIdentity: threadRef,
                stepCounters: { [threadRef]: 0 }
            });
        });
    }

    const threadSteps: StepJson[] | undefined = state.strategyRun.strategyThreadMap[state.currentThreadIdentity];
    if (!threadSteps) {
        console.error(`EdgeRouting: Thread ${state.currentThreadIdentity} not found, ending graph`);
        return END;
    }

    const currentStepCounter = state.stepCounters[state.currentThreadIdentity] ?? 0;

    if (currentStepCounter >= threadSteps.length) {
        return END;
    }

    const step = threadSteps[currentStepCounter];

    // BranchStep short-circuiting (Option A):
    // Pattern after expansion: [BRANCH, WHEN(LessThan), WHAT, BRANCH_REMAINDER?]
    // - If we're about to run WHAT and WHEN decided false, skip WHAT.
    // - If we're about to run BRANCH_REMAINDER and WHEN decided true (meaning WHAT just ran), skip the remainder.
    if (step.kind === CONSTANTS.STEPS.work) {
        const prev = currentStepCounter > 0 ? threadSteps[currentStepCounter - 1] : undefined;
        const prevPrev = currentStepCounter > 1 ? threadSteps[currentStepCounter - 2] : undefined;
        if (prev && prevPrev && prev.kind === CONSTANTS.STEPS.work && prevPrev.kind === CONSTANTS.STEPS.branch) {
            const prevWork = prev as WorkStepJson;
            const { job: prevJob, isLessThan } = isLessThanWorkStep(prevWork, state.jobMap);
            if (isLessThan) {
                const decision = readMaterializedBooleanDecision(
                    state.strategyRun.strategyState,
                    prevWork.execution,
                    prevJob
                );

                if (!decision) {
                    const nextIndex = currentStepCounter + 1;
                    if (nextIndex >= threadSteps.length) {
                        return END;
                    }

                    const nextStep = threadSteps[nextIndex];
                    return new Send(getNextNodeForStep(nextStep), {
                        ...state,
                        currentThreadIdentity: state.currentThreadIdentity,
                        stepCounters: { [state.currentThreadIdentity]: nextIndex },
                    });
                }
            }
        }
    }

    if (step.kind === CONSTANTS.STEPS.branch) {
        const prev = currentStepCounter > 0 ? threadSteps[currentStepCounter - 1] : undefined;
        const prevPrev = currentStepCounter > 1 ? threadSteps[currentStepCounter - 2] : undefined;
        const prevPrevPrev = currentStepCounter > 2 ? threadSteps[currentStepCounter - 3] : undefined;
        if (
            prev &&
            prevPrev &&
            prevPrevPrev &&
            prev.kind === CONSTANTS.STEPS.work &&
            prevPrev.kind === CONSTANTS.STEPS.work &&
            prevPrevPrev.kind === CONSTANTS.STEPS.branch
        ) {
            const whenWork = prevPrev as WorkStepJson;
            const { job: whenJob, isLessThan } = isLessThanWorkStep(whenWork, state.jobMap);
            if (isLessThan) {
                const decision = readMaterializedBooleanDecision(
                    state.strategyRun.strategyState,
                    whenWork.execution,
                    whenJob
                );

                if (decision) {
                    const nextIndex = currentStepCounter + 1;
                    if (nextIndex >= threadSteps.length) {
                        return END;
                    }

                    const nextStep = threadSteps[nextIndex];
                    return new Send(getNextNodeForStep(nextStep), {
                        ...state,
                        currentThreadIdentity: state.currentThreadIdentity,
                        stepCounters: { [state.currentThreadIdentity]: nextIndex },
                    });
                }
            }
        }
    }

        if (step.kind === CONSTANTS.STEPS.work) {
            return CONSTANTS_LOCAL.NODE_Work_Step;
        } else if (step.kind === CONSTANTS.STEPS.branch) {
            return CONSTANTS_LOCAL.NODE_BRANCH_STEP;
        }
        else if (step.kind === CONSTANTS.STEPS.while) {
            // Special-case: WhileStep clone should be skipped if the preceding whenWorkStep decided false.
            // We treat "when" as an internal LessThan workstep for now.
            const prev = currentStepCounter > 0 ? threadSteps[currentStepCounter - 1] : undefined;
            if (prev && prev.kind === CONSTANTS.STEPS.work) {
                const prevWork = prev as WorkStepJson;
                const prevJob = state.jobMap[prevWork.execution.jobRef];
                if (!prevJob) {
                    throw new Error(`Job '${prevWork.execution.jobRef}' not found in jobMap`);
                }

                const isLessThan =
                    prevWork.execution.jobRef === 'JOB-LessThan' ||
                    prevJob.name === 'LessThan';

                if (isLessThan) {
                    const decision = readMaterializedBooleanDecision(
                        state.strategyRun.strategyState,
                        prevWork.execution,
                        prevJob
                    );
                    if (!decision) {
                        const nextIndex = currentStepCounter + 1;
                        if (nextIndex >= threadSteps.length) {
                            return END;
                        }

                        const nextStep = threadSteps[nextIndex];
                        // Use Send to advance the stepCounter before routing.
                        return new Send(getNextNodeForStep(nextStep), {
                            ...state,
                            currentThreadIdentity: state.currentThreadIdentity,
                            stepCounters: { [state.currentThreadIdentity]: nextIndex },
                        });
                    }
                }
            }

            return CONSTANTS_LOCAL.NODE_WHILE_STEP;
        } else if (step.kind === CONSTANTS.STEPS.for) {
            // Special-case: ForStep clone should be skipped if the preceding whenWorkStep decided false.
            // We treat "when" as an internal LessThan workstep for now.
            const prev = currentStepCounter > 0 ? threadSteps[currentStepCounter - 1] : undefined;
            if (prev && prev.kind === CONSTANTS.STEPS.work) {
                const prevWork = prev as WorkStepJson;
                const prevJob = state.jobMap[prevWork.execution.jobRef];
                if (!prevJob) {
                    throw new Error(`Job '${prevWork.execution.jobRef}' not found in jobMap`);
                }

                const isLessThan =
                    prevWork.execution.jobRef === 'JOB-LessThan' ||
                    prevJob.name === 'LessThan';

                if (isLessThan) {
                    const decision = readMaterializedBooleanDecision(
                        state.strategyRun.strategyState,
                        prevWork.execution,
                        prevJob
                    );
                    if (!decision) {
                        const nextIndex = currentStepCounter + 1;
                        if (nextIndex >= threadSteps.length) {
                            return END;
                        }

                        const nextStep = threadSteps[nextIndex];
                        // Use Send to advance the stepCounter before routing.
                        return new Send(getNextNodeForStep(nextStep), {
                            ...state,
                            currentThreadIdentity: state.currentThreadIdentity,
                            stepCounters: { [state.currentThreadIdentity]: nextIndex },
                        });
                    }
                }
            }

            return CONSTANTS_LOCAL.NODE_FOR_STEP;
        }

        return END;
    } catch (error) {
        console.error('EdgeRouting: Unexpected error, ending graph:', error);
        return END;
    }
};

const NodeInterrupt = async (state: GraphState) => {
    // Intentionally do nothing
    // This node exists to "park" execution until resume
    return {
        interruptData: state.interruptData
    };
};

const stateGraph = new StateGraph(GraphStateAnnotationRoot)
    .addNode( // ATTENTION: consider an Init subgraph for GenerateStrategyRun + LoadData
        CONSTANTS_LOCAL.NODE_GENERATE_STRATEGY_RUN,
        new NodeGenerateStrategyRun()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_LOAD_DATA,
        new NodeLoadData()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_Work_Step,
        new NodeWorkStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_BRANCH_STEP,
        new NodeBranchStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_WHILE_STEP,
        new NodeWhileStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_FOR_STEP,
        new NodeForStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_INTERRUPT,
        NodeInterrupt
    )
    .addEdge(START, CONSTANTS_LOCAL.NODE_LOAD_DATA)
    .addEdge(CONSTANTS_LOCAL.NODE_LOAD_DATA, CONSTANTS_LOCAL.NODE_GENERATE_STRATEGY_RUN)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_GENERATE_STRATEGY_RUN, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_Work_Step, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_BRANCH_STEP, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_WHILE_STEP, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_FOR_STEP, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_INTERRUPT, EdgeRouting)


// Compile with checkpointer
const checkpointer = new MemorySaver();
export const graph = stateGraph.compile({
    checkpointer,
    interruptAfter: [CONSTANTS_LOCAL.NODE_INTERRUPT]
});



