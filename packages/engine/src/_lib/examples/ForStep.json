{"identity": "STATEFUL_STRATEGY-VY3H2tn42GnKNoxn3lg4", "statelessStrategy": {"identity": "STATELESS_STRATEGY-NNPGrWfwD7CewFhgSTyD", "steps": [{"identity": "FOR-PHTL5UUhlO4pnZ3QaAoY", "kind": "for", "case": {"what": {"identity": "WORK-YhnoqGC6qbGPscAhgLbH", "kind": "work", "execution": {"identity": "EXECUTION-YhnoqGC6qbGPscAhgLbH", "jobRef": "JOB-bzePRd0ZJ0QMN8ZycXL0", "roleBindings": {"inputBindingMap": {"ROLE-XjqaruvyjrT3dIkDOW86": "RESOURCE-w2XS19k9gcu4cPZWSuB2"}, "outputBindingMap": {"ROLE-7AXmzIGERZQJ8YAlOr3o": "RESOURCE-ZuGWiyoUP76ADMXnlVL7", "ROLE-ErrorOutput": "RESOURCE-VmhqsdT4cVWcPnQHQEpE"}}}}, "when": {"identity": "WORK-eklhIiYiYVo5YvGYPzS8", "kind": "work", "execution": {"identity": "EXECUTION-eklhIiYiYVo5YvGYPzS8", "jobRef": "JOB-LessThan", "roleBindings": {"inputBindingMap": {"ROLE-u4vW7PDlX6V9IGJoNxhl": "RESOURCE-32xVLbwkhPyDlqjFWLyF", "ROLE-xLaQo8L1fMxKTQUJcaJn": "RESOURCE-qihRv2atWPgk4qHbC02j"}, "outputBindingMap": {"ROLE-C4FKVwXipgngzPQc3MDc": "RESOURCE-QU6H7IklpQZbOI5Y8Ua3", "ROLE-ErrorOutput": "RESOURCE-X6CH8qIMXurJEk5DJSns"}}}}}}]}, "strategyState": {"EXECUTION-eklhIiYiYVo5YvGYPzS8": {"ROLE-xLaQo8L1fMxKTQUJcaJn": {"identity": "RESOURCE-4", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/4", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 4}}, "ROLE-C4FKVwXipgngzPQc3MDc": {"identity": "RESOURCE-QU6H7IklpQZbOI5Y8Ua3", "resourceTypeRef": "TYPE-Boolean", "creationContext": {"resourceRoleRef": "ROLE-C4FKVwXipgngzPQc3MDc", "executionRef": "EXECUTION-eklhIiYiYVo5YvGYPzS8"}, "kind": "potential-output"}, "ROLE-ErrorOutput": {"identity": "RESOURCE-X6CH8qIMXurJEk5DJSns", "resourceTypeRef": "TYPE-Error", "creationContext": {"resourceRoleRef": "ROLE-ErrorOutput", "executionRef": "EXECUTION-eklhIiYiYVo5YvGYPzS8"}, "kind": "potential-output"}}, "EXECUTION-YhnoqGC6qbGPscAhgLbH": {"ROLE-XjqaruvyjrT3dIkDOW86": {"identity": "RESOURCE-1", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-Manual", "executionRef": "EXECUTION-Genesis"}, "kind": "materialized", "path": "mock://natural/1", "timestamp": "2025-11-30T00:00:00.000Z", "extractedData": {"identity": 1}}, "ROLE-7AXmzIGERZQJ8YAlOr3o": {"identity": "RESOURCE-ZuGWiyoUP76ADMXnlVL7", "resourceTypeRef": "TYPE-Natural", "creationContext": {"resourceRoleRef": "ROLE-7AXmzIGERZQJ8YAlOr3o", "executionRef": "EXECUTION-YhnoqGC6qbGPscAhgLbH"}, "kind": "potential-output"}, "ROLE-ErrorOutput": {"identity": "RESOURCE-VmhqsdT4cVWcPnQHQEpE", "resourceTypeRef": "TYPE-Error", "creationContext": {"resourceRoleRef": "ROLE-ErrorOutput", "executionRef": "EXECUTION-YhnoqGC6qbGPscAhgLbH"}, "kind": "potential-output"}}}}