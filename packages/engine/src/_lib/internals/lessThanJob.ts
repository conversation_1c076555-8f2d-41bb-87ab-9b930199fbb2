import type { <PERSON>son<PERSON><PERSON><PERSON><PERSON>, ResourceJson, ResourcePotentialOutputJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { RESOURCE_CREATION } from '@toolproof-npm/shared';

function getNaturalIdentity(resource: ResourceJson, inputName: string): number {
  const raw = resource.extractedData?.identity;
  if (typeof raw !== 'number' || Number.isNaN(raw)) {
    throw new Error(`LessThan: input '${inputName}' must have extractedData.identity:number`);
  }
  return raw;
}

export function executeLessThanJob(
  payload: Record<string, ResourceJson | ResourcePotentialOutputJson>
): Record<string, ResourceJson> {
  const dynamic = payload['DynamicSource'];
  const statik = payload['StaticTarget'];

  if (!dynamic || dynamic.kind !== 'materialized') {
    throw new Error(`LessThan: missing materialized input 'DynamicSource'`);
  }
  if (!statik || statik.kind !== 'materialized') {
    throw new Error(`LessThan: missing materialized input 'StaticTarget'`);
  }

  const left = getNaturalIdentity(dynamic, 'DynamicSource');
  const right = getNaturalIdentity(statik, 'StaticTarget');
  const decision = left < right;

  // Prefer the explicit Decision output. (GraphRunStrategy enforces Decision by name.)
  const decisionPotRaw = payload['Decision'];
  if (!decisionPotRaw || decisionPotRaw.kind !== 'potential-output') {
    throw new Error(`LessThan: missing potential-output 'Decision'`);
  }
  if (decisionPotRaw.resourceTypeRef !== CONSTANTS.SPECIALS.TYPE_Boolean) {
    throw new Error(`LessThan: 'Decision' must be TYPE_Boolean`);
  }

  const decisionKey = 'Decision';
  const decisionPot = decisionPotRaw;

  const content = { identity: decision } satisfies JsonDataJson;
  const decisionRes = RESOURCE_CREATION.createMaterializedResource(decisionPot, content);

  return {
    [decisionKey]: decisionRes,
  };
}
