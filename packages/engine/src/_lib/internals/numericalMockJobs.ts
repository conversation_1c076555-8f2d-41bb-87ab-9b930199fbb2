import type { ResourceJson, ResourcePotentialOutputJson, JsonDataJson } from '@toolproof-npm/schema';
import { RESOURCE_CREATION } from '@toolproof-npm/shared';

/**
 * Mock numerical jobs that skip CAFS persistence and return complete materialized resources.
 * Used when mockMode is enabled to avoid remote job calls and GCS writes.
 */

/**
 * Helper to extract Natural value from a materialized resource's extractedData
 */
function extractNaturalValue(resource: ResourceJson): number {
    const extracted = resource.extractedData as any;
    if (typeof extracted === 'number') return extracted;
    if (extracted && typeof extracted.identity === 'number') return extracted.identity;
    throw new Error(`Invalid Natural resource: missing numeric identity in extractedData`);
}

/**
 * Helper to create a mock output as a complete materialized resource
 * In mock mode, we use mock:// paths instead of real content-addressed paths
 */
function createMockOutput(
    potentialOutput: ResourcePotentialOutputJson,
    value: number
): ResourceJson {
    const extractedData = { identity: value } as JsonData<PERSON><PERSON>;

    // In mock mode, keep a clearly-non-persisted path scheme
    const mockPath = `mock://natural/${value}`;

    const materialized = RESOURCE_CREATION.createMaterializedResource(
        potentialOutput,
        extractedData
    );

    return {
        ...materialized,
        path: mockPath,
    };
}

function createMockErrorOutput(
    potentialOutput: ResourcePotentialOutputJson,
    name: string,
    description: string,
    details?: Record<string, JsonDataJson>
): ResourceJson {
    const extractedData = {
        name,
        description,
        ...(details ? { details } : {}),
    } as unknown as JsonDataJson;

    const mockPath = `mock://error/${encodeURIComponent(name)}`;

    const materialized = RESOURCE_CREATION.createMaterializedResource(
        potentialOutput,
        extractedData
    );

    return {
        ...materialized,
        path: mockPath,
    };
}

/**
 * Mock Add job: AddendOne + AddendTwo = Sum
 */
export function mockAdd(
    inputs: Record<string, ResourceJson | ResourcePotentialOutputJson>
): Record<string, ResourceJson> {
    const addendOne = inputs['AddendOne'] as ResourceJson;
    const addendTwo = inputs['AddendTwo'] as ResourceJson;
    const sumOutput = inputs['Sum'] as ResourcePotentialOutputJson;

    const value1 = extractNaturalValue(addendOne);
    const value2 = extractNaturalValue(addendTwo);
    const result = value1 + value2;

    return {
        Sum: createMockOutput(sumOutput, result),
    };
}

/**
 * Mock Subtract job: Minuend - Subtrahend = Difference
 */
export function mockSubtract(
    inputs: Record<string, ResourceJson | ResourcePotentialOutputJson>
): Record<string, ResourceJson> {
    const minuend = inputs['Minuend'] as ResourceJson;
    const subtrahend = inputs['Subtrahend'] as ResourceJson;
    const differenceOutput = inputs['Difference'] as ResourcePotentialOutputJson;

    const errorOutput = inputs['ErrorOutput'] as ResourcePotentialOutputJson | undefined;

    const value1 = extractNaturalValue(minuend);
    const value2 = extractNaturalValue(subtrahend);

    if (value2 > value1) {
        if (!errorOutput) {
            throw new Error(
                `Subtract mock job cannot emit error: missing 'ErrorOutput' potential output (subtrahend ${value2} > minuend ${value1})`
            );
        }

        return {
            ErrorOutput: createMockErrorOutput(
                errorOutput,
                'SubtractInvalidInput',
                `Subtrahend (${value2}) is larger than minuend (${value1}); subtraction would result in a negative value.`,
                {
                    minuend: value1 as unknown as JsonDataJson,
                    subtrahend: value2 as unknown as JsonDataJson,
                }
            ),
        };
    }

    const result = value1 - value2;

    return {
        Difference: createMockOutput(differenceOutput, result),
    };
}

/**
 * Mock Multiply job: Multiplicand * Multiplier = Product
 */
export function mockMultiply(
    inputs: Record<string, ResourceJson | ResourcePotentialOutputJson>
): Record<string, ResourceJson> {
    const multiplicand = inputs['Multiplicand'] as ResourceJson;
    const multiplier = inputs['Multiplier'] as ResourceJson;
    const productOutput = inputs['Product'] as ResourcePotentialOutputJson;

    const value1 = extractNaturalValue(multiplicand);
    const value2 = extractNaturalValue(multiplier);
    const result = value1 * value2;

    return {
        Product: createMockOutput(productOutput, result),
    };
}

/**
 * Mock Double job: N * 2 = Doubled
 */
export function mockDouble(
    inputs: Record<string, ResourceJson | ResourcePotentialOutputJson>
): Record<string, ResourceJson> {
    const N = inputs['N'] as ResourceJson;
    const DoubledOutput = inputs['Doubled'] as ResourcePotentialOutputJson;

    const value = extractNaturalValue(N);
    const result = value * 2;

    return {
        Doubled: createMockOutput(DoubledOutput, result),
    };
}

/**
 * Mock Divide job: Dividend / Divisor = Quotient, Remainder
 */
export function mockDivide(
    inputs: Record<string, ResourceJson | ResourcePotentialOutputJson>
): Record<string, ResourceJson> {
    const dividend = inputs['Dividend'] as ResourceJson;
    const divisor = inputs['Divisor'] as ResourceJson;
    const quotientOutput = inputs['Quotient'] as ResourcePotentialOutputJson;
    const remainderOutput = inputs['Remainder'] as ResourcePotentialOutputJson;

    const errorOutput = inputs['ErrorOutput'] as ResourcePotentialOutputJson | undefined;

    const value1 = extractNaturalValue(dividend);
    const value2 = extractNaturalValue(divisor);

    if (value2 === 0) {
        if (!errorOutput) {
            throw new Error(
                `Divide mock job cannot emit error: missing 'ErrorOutput' potential output (division by zero; dividend ${value1})`
            );
        }

        return {
            ErrorOutput: createMockErrorOutput(
                errorOutput,
                'DivideByZero',
                `Cannot divide by zero (dividend ${value1}, divisor ${value2}).`,
                {
                    dividend: value1 as unknown as JsonDataJson,
                    divisor: value2 as unknown as JsonDataJson,
                }
            ),
        };
    }

    const quotient = Math.floor(value1 / value2);
    const remainder = value1 % value2;

    return {
        Quotient: createMockOutput(quotientOutput, quotient),
        Remainder: createMockOutput(remainderOutput, remainder),
    };
}

/**
 * Execute a mock numerical job by name
 */
export function executeMockNumericalJob(
    jobName: string,
    payload: Record<string, ResourceJson | ResourcePotentialOutputJson>
): Record<string, ResourceJson> {
    const normalizedName = jobName.toLowerCase();

    switch (normalizedName) {
        case 'add':
            return mockAdd(payload);
        case 'subtract':
            return mockSubtract(payload);
        case 'multiply':
            return mockMultiply(payload);
        case 'double':
            return mockDouble(payload);
        case 'divide':
            return mockDivide(payload);
        default:
            throw new Error(`Unknown numerical job: ${jobName}`);
    }
}
