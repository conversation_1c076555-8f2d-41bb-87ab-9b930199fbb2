import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Resource<PERSON><PERSON>,
  ResourcePotentialInputJson,
  ResourcePotentialOutputJson,
  StrategyStateJson,
} from '@toolproof-npm/schema';
import { RESOURCE_CHAIN } from '@toolproof-npm/shared';

export function bindInputResInStrategyState(
  strategyState: StrategyStateJson,
  target: CreationContextJson,
  resource: ResourceJson
): StrategyStateJson {
  const bucket = strategyState[target.executionRef] ?? {};

  return {
    ...strategyState,
    [target.executionRef]: {
      ...bucket,
      [target.resourceRoleRef]: resource,
    },
  };
}

export function bindInputRefInStrategyState(
  strategyState: StrategyStateJson,
  target: CreationContextJson,
  source: CreationContextJson
): StrategyStateJson {
  const sourceEntry = strategyState?.[source.executionRef]?.[source.resourceRoleRef] as
    | ResourcePotentialOutputJson
    | undefined;
  if (!sourceEntry) {
    throw new Error(
      `resourceEntry not found for source (${source.executionRef}, ${source.resourceRoleRef})`
    );
  }

  const result = RESOURCE_CHAIN.resolveResourceChain(strategyState, source);
  if (result.status !== 'potential-output') {
    throw new Error(`Expected source to resolve to potential-output; got ${result.status}`);
  }

  const potentialInput: ResourcePotentialInputJson = {
    identity: sourceEntry.identity,
    resourceTypeRef: sourceEntry.resourceTypeRef,
    creationContext: {
      executionRef: source.executionRef,
      resourceRoleRef: source.resourceRoleRef,
    },
    kind: 'potential-input',
  };

  const bucket = strategyState[target.executionRef] ?? {};

  return {
    ...strategyState,
    [target.executionRef]: {
      ...bucket,
      [target.resourceRoleRef]: potentialInput,
    },
  };
}
