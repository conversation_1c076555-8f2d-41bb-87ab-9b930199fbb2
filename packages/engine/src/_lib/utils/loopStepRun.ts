import type {
  Creation<PERSON>ontext<PERSON><PERSON>,
  ExecutionIdentity<PERSON>son,
  Execution<PERSON>son,
  Job<PERSON>son,
  ResourcePotentialOutput<PERSON>son,
  ResourceRoleIdentityJson,
  StrategyStateJson,
} from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';

export function getExecutionSingleNonErrorOutputCreationContext(
  execution: ExecutionJson,
  nodeName: string
): CreationContextJson {
  const outputBindingMap = execution.roleBindings?.outputBindingMap ?? {};
  const nonErrorRoleRefs = (Object.keys(outputBindingMap) as ResourceRoleIdentityJson[]).filter(
    (roleRef) => roleRef !== CONSTANTS.SPECIALS.ROLE_ErrorOutput
  );

  if (nonErrorRoleRefs.length !== 1) {
    throw new Error(
      `[${nodeName}] Execution must have exactly one non-error output role binding; got ${nonErrorRoleRefs.length} (execution ${execution.identity})`
    );
  }

  return {
    executionRef: execution.identity,
    resourceRoleRef: nonErrorRoleRefs[0],
  };
}

export function getExecutionSingleInputRoleRef(execution: ExecutionJson, nodeName: string): ResourceRoleIdentityJson {
  const inputBindingMap = execution.roleBindings?.inputBindingMap ?? {};
  const inputRoleRefs = Object.keys(inputBindingMap) as ResourceRoleIdentityJson[];

  if (inputRoleRefs.length !== 1) {
    throw new Error(
      `[${nodeName}] Expected exactly one input role for execution ${execution.identity}; got ${inputRoleRefs.length}`
    );
  }

  return inputRoleRefs[0];
}

export function getRoleRefByInputName(job: JobJson, inputName: string, nodeName: string): ResourceRoleIdentityJson {
  const entries = Object.entries(job.roles.inputMap ?? {}) as Array<[ResourceRoleIdentityJson, { name: string }]>;
  const match = entries.find(([, role]) => role.name === inputName);
  if (!match) {
    throw new Error(`[${nodeName}] Input role '${inputName}' not found on job ${job.identity}`);
  }
  return match[0];
}

export function seedExecutionOutputPotentials(
  strategyState: StrategyStateJson,
  execution: ExecutionJson,
  job: JobJson,
  nodeName: string
): StrategyStateJson {
  const outputBindingMap = execution.roleBindings?.outputBindingMap ?? {};
  const outputRoleRefs = Object.keys(outputBindingMap) as ResourceRoleIdentityJson[];
  if (outputRoleRefs.length === 0) return strategyState;

  type ExecutionBucket = StrategyStateJson[ExecutionIdentityJson];
  const execBucket: ExecutionBucket = { ...(strategyState[execution.identity] ?? {}) } as ExecutionBucket;

  for (const roleRef of outputRoleRefs) {
    if (execBucket[roleRef]) continue;

    const resourceRef = outputBindingMap[roleRef];
    const resourceTypeRef = job.roles.outputMap[roleRef]?.resourceTypeRef;
    if (!resourceTypeRef) {
      throw new Error(`[${nodeName}] Missing resourceTypeRef for output role '${roleRef}' on job ${job.identity}`);
    }

    execBucket[roleRef] = {
      identity: resourceRef,
      resourceTypeRef,
      creationContext: {
        resourceRoleRef: roleRef,
        executionRef: execution.identity,
      },
      kind: 'potential-output',
    } satisfies ResourcePotentialOutputJson;
  }

  return {
    ...strategyState,
    [execution.identity]: execBucket,
  };
}
