# Macro Steps in the Engine — BranchStep and LoopStep

This is a high-level walkthrough of the **runtime behavior** of the engine’s macro steps.

**Core idea**

> A macro step is a step that, at runtime, expands itself into a sequence of concrete steps by mutating the thread’s `steps` array, while also updating `strategyState` so the inserted steps can execute.

This README covers two macro-step families:

- **BranchStep** (conditional multi-case selection)
- **LoopStep**
  - **ForStep**
  - **WhileStep**

## Table of contents

- [Macro Steps in the Engine — BranchStep and LoopStep](#macro-steps-in-the-engine--branchstep-and-loopstep)
  - [Table of contents](#table-of-contents)
  - [Where the logic lives](#where-the-logic-lives)
  - [Runtime data model](#runtime-data-model)
    - [`strategyRun.strategyThreadMap`](#strategyrunstrategythreadmap)
    - [`strategyRun.strategyState`](#strategyrunstrategystate)
    - [Counters](#counters)
  - [Execution flow overview](#execution-flow-overview)
  - [BranchStep](#branchstep)
    - [Macro expansion shape](#macro-expansion-shape)
    - [ATTENTION: should we guard against this in console/validation?](#attention-should-we-guard-against-this-in-consolevalidation)
    - [Short-circuiting (skip logic)](#short-circuiting-skip-logic)
    - [StrategyState expectations](#strategystate-expectations)
  - [LoopStep](#loopstep)
    - [Shared loop behavior](#shared-loop-behavior)
    - [ForStep](#forstep)
    - [WhileStep](#whilestep)
  - [NodeWorkStep](#nodeworkstep)
  - [Internal LessThan: producing Decision](#internal-lessthan-producing-decision)

## Where the logic lives

- `NodeWorkStep`: `core/packages/engine/src/nodes/NodeWorkStep.ts`
- `NodeBranchStep`: `core/packages/engine/src/nodes/NodeBranchStep.ts`
- `NodeForStep`: `core/packages/engine/src/nodes/NodeForStep.ts`
- `NodeWhileStep`: `core/packages/engine/src/nodes/NodeWhileStep.ts`
- Routing/branching: `core/packages/engine/src/graphs/GraphRunStrategy.ts`
- Internal LessThan job: `core/packages/engine/src/_lib/internals/lessThanJob.ts`
- Macro-step helpers (seeding potentials, single-input/output assumptions): `core/packages/engine/src/_lib/utils/loopStepRun.ts`

## Runtime data model

### `strategyRun.strategyThreadMap`

- Maps `threadRef -> StepJson[]`.
- Each thread is a linear list of steps.
- The engine uses `stepCounters[threadRef]` to track the current index into that array.
- Macro steps work by **splicing additional steps** into a thread’s array at runtime.

### `strategyRun.strategyState`

Nested map:

```
strategyState[executionRef][roleRef] -> ResourceEntry
```

Entries are one of:

- `materialized` (actual resource with `extractedData`)
- `potential-output` (placeholder for a job output that will exist later)
- `potential-input` (a pointer that references another `creationContext`; resolved via a chain)
- `missing` (interrupt path)

### Counters

- `stepCounters[threadRef]`: which step index is executing next.
- `iterationCounters[threadRef]`: loop iteration counter used by ForStep to seed `DynamicSource`.

## Execution flow overview

In `GraphRunStrategy.ts`, `EdgeRouting` routes based on the current step’s `kind`:

- `work` → `NodeWorkStep`
- `branch` → `NodeBranchStep` (with special skip logic to short-circuit cases)
- `for` → `NodeForStep` (with special skip logic to terminate the loop)
- `while` → `NodeWhileStep` (with special skip logic to terminate the loop)

## BranchStep

BranchStep implements ordered, first-match selection across cases:

```ts
branch.cases = [
  { when: WorkStep /* LessThan */, what: WorkStep },
  { when: WorkStep /* LessThan */, what: WorkStep },
  ...
]
```

### Macro expansion shape

The engine uses a counter-free technique ("remainder branch") so multiple macro steps can coexist in a thread without sharing a single per-thread "case index".

When `NodeBranchStep` is reached at `threadSteps[currentStepCounter]`, it expands only the first case and (if needed) inserts a new BranchStep that contains the remaining cases.

Given `case0 = branch.cases[0]`:

```
Before:
  [..., BRANCH, ...]

After:
  [..., BRANCH, WHEN0, WHAT0, BRANCH_REMAINDER?, ...]
```

Where:

- `WHEN0` is `case0.when` (currently an internal LessThan job)
- `WHAT0` is `case0.what`
- `BRANCH_REMAINDER` is a new BranchStep identity with `cases = branch.cases.slice(1)` (only inserted if there are remaining cases)

### ATTENTION: should we guard against this in console/validation?
To keep `NodeWorkStep` happy, `NodeBranchStep` seeds output potential-outputs for the injected `WHEN0` and `WHAT0` executions before they run.

### Short-circuiting (skip logic)

BranchStep relies on routing logic in `GraphRunStrategy.ts` to select the correct execution path based on the boolean `Decision` output of the `WHEN` (LessThan) workstep.

The injected layout always includes both `WHEN0` and `WHAT0`, plus a possible remainder-branch. Routing then skips what should not run:

- If `WHEN0.Decision === false`:
  - skip `WHAT0`
  - proceed to `BRANCH_REMAINDER` (if present), which expands the next case

- If `WHEN0.Decision === true`:
  - run `WHAT0`
  - then skip `BRANCH_REMAINDER` (so later cases are not evaluated)

Today this skip logic is implemented with a LessThan-only heuristic (jobRef/name), matching the loop behavior.

### StrategyState expectations

BranchStep does not re-bind resources between cases.

It assumes:

- each case’s `when.execution` inputs are present/bindable in `strategyState`
- each case’s `what.execution` inputs are present/bindable in `strategyState`

BranchStep’s runtime responsibilities are primarily:

- splice steps
- seed output potentials for the injected WorkSteps

## LoopStep

LoopStep is a family consisting of ForStep and WhileStep. Both have the same macro-expansion shape and rely on LessThan’s boolean `Decision` output for termination.

### Shared loop behavior

When a loop step is reached, the engine expands it into three steps:

1. the loop’s current `what` work step
2. the loop’s current `when` work step (LessThan)
3. a cloned loop step (pre-bound for the next iteration)

Conceptually:

```
Before:
  [..., LOOP, ...]

After:
  [..., LOOP, WHAT, WHEN, LOOP_CLONE, ...]
```

Routing checks the preceding `WHEN` step’s `Decision`. If `Decision === false`, it skips the next loop clone so the loop terminates.

### ForStep

ForStep uses `iterationCounters[threadRef]` to drive LessThan’s `DynamicSource`.

High-level behavior:

- Clone the ForStep.
- Bind the clone’s `what` input to the current `what` output via a `potential-input` reference.
- Bind the clone’s `when` inputs:
  - `StaticTarget`: reused from the original `when` execution’s materialized StaticTarget
  - `DynamicSource`: materialized as the next iteration natural (`currentIteration + 1`)
- Seed output potentials for the clone executions.
- Splice `WHAT`, `WHEN`, and `FOR_CLONE` into the thread.
- Increment `iterationCounters[threadRef]`.

ForStep also ensures the *current* `when` has a usable `DynamicSource` entry; if it’s missing from the incoming `strategyState`, it materializes the current iteration value to avoid `NodeWorkStep` throwing a missing-resource error.

### WhileStep

WhileStep is implemented with the same cloning/splicing behavior as ForStep, but it uses a different source for LessThan’s `DynamicSource`:

- `DynamicSource` comes from the current iteration’s `what` output.
- For the next iteration, `clone.when.DynamicSource` is bound to `clone.what` output (via a `potential-input` reference).

Important ordering detail:

`bindInputRefInStrategyState` requires the source resource entry to exist in `strategyState`, so WhileStep seeds the clone.what output potentials *before* binding `clone.when.DynamicSource -> clone.what output`.

## NodeWorkStep

When routing reaches a WorkStep, `NodeWorkStep` executes the job.

High-level behavior:

- Resolve inputs from `strategyState`:
  - `materialized` values are passed directly
  - `potential-input` chains are resolved
  - `missing` triggers interrupt handling
  - missing entries throw
- Provide output `potential-output` placeholders to the job.
- Execute internal LessThan / mock jobs / remote execution.
- Persist materialized outputs back into `strategyState`.

## Internal LessThan: producing Decision

In `lessThanJob.ts`:

- Reads `payload['DynamicSource']` and `payload['StaticTarget']` (both materialized `TYPE-Natural`).
- Computes `left < right`.
- Materializes a `TYPE-Boolean` output named `Decision`.
  - The boolean value is stored at `materialized.extractedData.identity`.