/**
 * Express Server with CAFS APIs
 *
 * This server provides:
 * - REST API endpoints for Content Addressable File Storage (CAFS)
 * - Store content with deduplication
 * - Retrieve content by hash
 */

import type { PersistenceWriteResult } from './_lib/types.js';
import type { ResourceJson } from '@toolproof-npm/schema';
import { CAFS } from './cafs.js';
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { startFileListener } from './fileHandling.js';

// Load environment variables
dotenv.config();

// Create Express app
const app: express.Application = express();
const port = Number(process.env.PORT) || 3000;

// Initialize CAFS instance

// Middleware
app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-device-agent']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (_req: express.Request, res: express.Response) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'CAFS API Server'
    });
});

// Store content endpoint
app.post('/api/cafs/store', async (req: express.Request, res: express.Response) => {
    try {
        const { resource, storageType = "" } = req.body as { resource: ResourceJson, storageType: string };

        // Validate required fields
        if (!resource) {
            return res.status(400).json({
                error: 'Missing required field: resource'
            });
        }

        if (!resource.identity || !resource.resourceTypeRef || !resource.creationContext?.resourceRoleRef || !resource.creationContext?.executionRef) {
            return res.status(400).json({
                error: 'Missing required resource fields: identity, resourceTypeRef, creationContext.resourceRoleRef, creationContext.executionRef'
            });
        }

        if (!resource.path || !resource.kind || !resource.timestamp || !resource.extractedData) {
            return res.status(400).json({
                error: 'Missing required resource fields: path, kind, timestamp, extractedData'
            });
        }

        let cafs;

        if (storageType === 'dispatch') {
            cafs = new CAFS({
                bucketName: process.env.STRATEGY_BUCKET_NAME || 'tp-strategies',
                maxFileSize: 10 * 1024 * 1024, // 10MB
                defaultContentType: 'application/json'
            });
        } else {
            cafs = new CAFS({
                bucketName: process.env.BUCKET_NAME || 'tp-resources',
                maxFileSize: 10 * 1024 * 1024, // 10MB
                defaultContentType: 'application/json'
            });
        }

        // Store content using CAFS (content is derived from extractedData)
        const result: PersistenceWriteResult = await cafs.storeContent(resource);

        if (result.success) {
            res.status(201).json({
                success: true,
                path: result.path,
                message: 'Content stored successfully'
            });
        } else {
            res.status(500).json({
                success: false,
                error: result.error || 'Failed to store content'
            });
        }

    } catch (error) {
        console.error('Error storing content:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error while storing content'
        });
    }
});

// Retrieve content endpoint - use middleware to handle any path
app.use('/api/cafs/retrieve', async (req: express.Request, res: express.Response) => {
    try {
        // Extract path after /api/cafs/retrieve/
        const fullPath = req.path; // e.g., /TYPE-Natural/abc123
        const path = fullPath.startsWith('/') ? fullPath.substring(1) : fullPath;

        if (!path) {
            return res.status(400).json({
                error: 'Path is required'
            });
        }

        const cafs = new CAFS({
            bucketName: process.env.BUCKET_NAME || 'tp-resources',
            maxFileSize: 10 * 1024 * 1024, // 10MB
            defaultContentType: 'application/json'
        });

        // Retrieve content using CAFS
        const content = await cafs.retrieveContent(path);

        res.json({
            success: true,
            path,
            content,
            retrievedAt: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error retrieving content:', error);

        if (error instanceof Error && error.message.includes('not found')) {
            const fullPath = req.path;
            const path = fullPath.startsWith('/') ? fullPath.substring(1) : fullPath;
            res.status(404).json({
                success: false,
                error: `Content at path ${path} not found`
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Internal server error while retrieving content'
            });
        }
    }
});

// Delete resource endpoint
app.delete('/api/cafs/delete', async (req: express.Request, res: express.Response) => {
    try {
        const { path, resourceTypeRefs } = req.body as { path: string; resourceTypeRefs: string[] };

        // Validate required fields
        if (!path || !resourceTypeRefs || !Array.isArray(resourceTypeRefs)) {
            return res.status(400).json({
                error: 'Missing required fields: path (string) and resourceTypeRefs (array)'
            });
        }

        const cafs = new CAFS({
            bucketName: process.env.BUCKET_NAME || 'tp-resources',
            maxFileSize: 10 * 1024 * 1024, // 10MB
            defaultContentType: 'application/json'
        });

        // Delete resource using CAFS
        const result = await cafs.deleteContent(path, resourceTypeRefs);

        if (result.success) {
            res.json({
                success: true,
                message: 'Resource deleted successfully',
                path: result.path,
                deletedIdentities: result.deletedIdentities || []
            });
        } else {
            res.status(404).json({
                success: false,
                error: result.error || 'Failed to delete resource'
            });
        }

    } catch (error) {
        console.error('Error deleting resource:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error while deleting resource'
        });
    }
});


// Global error handling - log but don't crash to prevent cascade failures
process.on('unhandledRejection', (reason: any) => {
    console.error('⚠️  Unhandled Rejection (non-fatal):', reason);
    // Don't exit - let the service continue serving other requests
});

process.on('uncaughtException', (err: any) => {
    console.error('⚠️  Uncaught Exception (non-fatal):', err);
    // Don't exit - let the service continue serving other requests
});

process.on('SIGINT', () => {
    console.log('Shutting down server gracefully...');
    process.exit(0);
});

async function bootstrap() {
  console.log('🚀 Starting service...');
  
  // Temporarily disabled - GCP credentials issue
  await startFileListener();
  
//   console.log('⚠️  File listener disabled - skipping Pub/Sub subscription');
}

bootstrap().catch((err) => {
  console.error('❌ Failed to start service', err);
  process.exit(1);
});


// Start server (with HMR guard to prevent port collisions)
if (!(globalThis as any).__cafsServerStarted) {
    (globalThis as any).__cafsServerStarted = true;
    app.listen(port, '0.0.0.0', () => {
        console.log(`🚀 CAFS API Server running on port ${port}`);
        console.log(`📊 Health check: http://localhost:${port}/health`);
        console.log(`📝 Store content: POST http://localhost:${port}/api/cafs/store`);
        console.log(`📖 Retrieve content: GET http://localhost:${port}/api/cafs/retrieve/:contentHash`);
        console.log(`🔍 Check existence: GET http://localhost:${port}/api/cafs/exists/:contentHash`);
    }).on('error', (err: any) => {
        console.error('❌ Error starting server:', err);
        process.exit(1);
    });
}

// Export for testing
export default app;