import { PubSub } from '@google-cloud/pubsub';
import { Storage } from '@google-cloud/storage';
import { StatefulStrategyJson } from '@toolproof-npm/schema';
import { Client } from '@langchain/langgraph-sdk';
import { RemoteGraph } from '@langchain/langgraph/remote';
import { HumanMessage } from '@langchain/core/messages';

const pubsub = new PubSub();
const storage = new Storage();

interface ProcessFileInput {
  fileName: string;
  bucketName: string;
  buffer: Buffer;
  contentType?: string;
}

async function runRemoteGraph(statefulStrategy: StatefulStrategyJson) {

  const apiKey = process.env.LANGCHAIN_API_KEY;
  if (!apiKey) {
    return { error: 'Missing LANGCHAIN_API_KEY', status: 500 };
  }

  const urlLocal = `http://localhost:2024`;
  const urlRemote = `https://engine-core-a7953b216e1d518b84f7f1f2cab2edfa.us.langgraph.app`;
  const apiUrl = process.env.LANGGRAPH_API_URL || urlRemote; // fallback to remote
  const graphId = 'GraphRunStrategy';

  const client = new Client({ apiUrl, apiKey });
  const remoteGraph = new RemoteGraph({ graphId, url: apiUrl, apiKey });

  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 30 * 60 * 1000);

  try {
    // Create a thread (or use an existing thread instead)
    const thread = await client.threads.create();

    try {
      const result = await remoteGraph.invoke({
        messages: [new HumanMessage('Graph is invoked')],
        mockModeManager: {
          enabled: false,
        },
        statefulStrategy
      }, {
        configurable: { thread_id: thread.thread_id },
        signal: controller.signal,
      });

      console.log('result:', JSON.stringify(result.messages, null, 2));

      return result;

    } finally {
      clearTimeout(timeout);
      if (!controller.signal.aborted) {
        controller.abort();
      }
    }
  } catch (error) {
    console.error('Error invoking graph:', error);
  }

}

export async function processFile({
  fileName,
  buffer,
  contentType,
}: ProcessFileInput): Promise<void> {
  console.log('Processing:', fileName);
  console.log('Type:', contentType);
  console.log('Size:', buffer.length);

  // if (contentType === 'application/json') {
  //   const json = JSON.parse(buffer.toString());
  //   console.log('Parsed JSON:', json);
  // }

  if (contentType !== 'text/plain') {
    return;
  }

  const text = buffer.toString('utf-8');
  const text2 = JSON.parse(text);

  const statefulStrategy: StatefulStrategyJson = JSON.parse(text2);

  const graphResult = await runRemoteGraph(statefulStrategy);

  return graphResult;
}

export async function startFileListener(): Promise<void> {
  const subscriptionName = process.env.PUBSUB_SUBSCRIPTION!;
  const watchFolder = process.env.WATCH_FOLDER || 'TYPE-StatefulStrategy/';
  const subscription = pubsub.subscription(subscriptionName);

  console.log(`Listening for files in folder: ${watchFolder}`);

  subscription.on('message', async (message: any) => {
    try {
      const event = JSON.parse(message.data.toString());
      message.ack();

      const fileName: string = event.name;
      const bucketName: string = event.bucket;

      if (!fileName || !bucketName) {
        return;
      }

      // 🔎 Folder filter
      if (!fileName.startsWith(watchFolder)) {
        return;
      }

      console.log(`New file detected: ${fileName}`);

      const file = storage.bucket(bucketName).file(fileName);

      // 🔽 Download file (or stream if large)
      const [buffer] = await file.download();

      await processFile({
        fileName,
        bucketName,
        buffer,
        contentType: event.contentType,
      });

      console.log(`File processed: ${fileName}`);
      return;
    } catch (err) {
      console.error('File handling error:', err);
      message.nack();
    }
  });

  subscription.on('error', (err: any) => {
    console.error('Subscription error:', err);
  });
}
