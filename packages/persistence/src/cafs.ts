import { StorageHelper } from './storageHelper.js';
import {
    PersistenceWriteResult,
    PersistenceConfig
} from './_lib/types.js';
import type { ResourceJson } from '@toolproof-npm/schema';

/**
 * Content Addressable File Storage (CAFS)
 */
export class CAFS {
    private storageHelper: StorageHelper;
    private config: PersistenceConfig;

    constructor(config: Partial<PersistenceConfig> = {}) {
        this.config = {
            bucketName: config.bucketName || process.env.BUCKET_NAME || 'tp-resources',
            maxFileSize: config.maxFileSize || 10 * 1024 * 1024, // 10MB default
            defaultContentType: config.defaultContentType || 'application/json'
        };

        this.storageHelper = new StorageHelper(this.config.bucketName);
    }

    async storeContent(
        resource: ResourceJson
    ): Promise<PersistenceWriteResult> {
        try {
            // Derive content from extractedData with pretty formatting
            const content = JSON.stringify(resource.extractedData, null, 2);

            // Validate content size
            const contentSize = Buffer.byteLength(content, 'utf8');
            if (contentSize > this.config.maxFileSize) {
                return {
                    success: false,
                    path: '',
                    error: `Content size ${contentSize} exceeds maximum allowed size ${this.config.maxFileSize}`
                };
            }

            // Check if file already exists in GCS
            const fileExists = await this.storageHelper.checkExistence(resource.path);

            // Write to GCS only if file doesn't exist
            if (!fileExists.fileExists) {
                await this.storageHelper.writeRawContent(content, resource);
            }

            // Always write to Firestore (updates metadata even if file exists)
            await this.storageHelper.writeToFirestore(resource);

            return {
                success: true,
                path: resource.path,
            };

        } catch (error) {
            return {
                success: false,
                path: '',
                error: `Failed to store content: ${error}`
            };
        }
    }

    async retrieveContent(path: string): Promise<string> {
        try {

            // Check if content exists
            const { fileExists: exists } = await this.storageHelper.checkExistence(path);
            if (!exists) {
                throw new Error(`Content with path ${path} not found`);
            }

            // Retrieve content
            const content = await this.storageHelper.readRawContent(path);

            return content;

        } catch (error) {
            throw new Error(`Failed to retrieve content: ${error}`);
        }
    }

    /**
     * Deletes a resource from both GCS and Firestore
     * Deletes the GCS file at the given path and all Firestore documents
     * (across specified resource types) that reference this path
     * @param path The GCS path to the file
     * @param resourceTypeRefs Array of resource type references to search for documents
     * @returns Result indicating success or failure with deleted identities
     */
    async deleteContent(
        path: string,
        resourceTypeRefs: string[]
    ): Promise<PersistenceWriteResult & { deletedIdentities?: string[] }> {
        try {
            // Delete from GCS first
            await this.storageHelper.deleteFromGCS(path);

            // Delete all Firestore documents that reference this path
            const deletedIdentities = await this.storageHelper.deleteByPath(path, resourceTypeRefs);

            return {
                success: true,
                path,
                deletedIdentities
            };

        } catch (error) {
            return {
                success: false,
                path: '',
                error: `Failed to delete content: ${error}`
            };
        }
    }

}
