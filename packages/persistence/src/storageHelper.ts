import { Storage } from '@google-cloud/storage';
import { dbAdmin } from './firebaseAdminInit.js';
import type { ResourceMetaJson } from '@toolproof-npm/schema'

/**
 * Core GCS utilities for reading and writing files
 */
export class StorageHelper {
    private storage: Storage;
    private bucketName: string;

    constructor(bucketName: string = 'tp-resources') {
        this.storage = new Storage();
        this.bucketName = bucketName;
    }

    async writeToFirestore(
        meta: ResourceMetaJson
    ): Promise<void> {
        try {
            const col = dbAdmin.collection('resources').doc(meta.resourceTypeRef).collection('members');
            const docRef = col.doc(meta.identity);
            await docRef.set(meta);
        } catch (error) {
            throw new Error(`Failed to write to Firestore: ${error}`);
        }
    }

    /**
     * Reads raw content from GCS
     * @param filePath The path to the file in the GCS bucket
     * @returns The raw file content as string
     */
    async readRawContent(filePath: string): Promise<string> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(filePath);

            const [exists] = await file.exists();
            if (!exists) {
                throw new Error(`File ${filePath} does not exist in bucket ${this.bucketName}`);
            }

            const [fileContents] = await file.download();
            return fileContents.toString();
        } catch (error) {
            throw new Error(`Failed to read raw content from ${filePath}: ${error}`);
        }
    }

    /**
     * Writes raw content to GCS
     * @param filePath The path where to store the file
     * @param content The content to store
     * @param contentType The MIME type of the content
     */
    async writeRawContent(
        content: string,
        meta: ResourceMetaJson
    ): Promise<void> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(meta.path);
            // Flatten meta object into key/value pairs without assumptions.
            const flat: Record<string, string> = {};
            const flatten = (value: any, prefix: string) => {
                if (value === null || value === undefined) return;
                if (typeof value === 'object' && !Array.isArray(value)) {
                    for (const [k, v] of Object.entries(value)) {
                        const next = prefix ? `${prefix}.${k}` : k;
                        flatten(v, next);
                    }
                } else if (Array.isArray(value)) {
                    value.forEach((v, idx) => {
                        const next = `${prefix}[${idx}]`;
                        flatten(v, next);
                    });
                } else {
                    // Primitive: stringify safely
                    flat[prefix] = typeof value === 'string' ? value : JSON.stringify(value);
                }
            };
            flatten(meta, '');
            // Remove possible leading empty key produced if meta is primitive (unlikely)
            if (flat['']) delete flat[''];

            await file.save(content, {
                metadata: {
                    contentType: 'text/plain',
                    metadata: flat
                }
            });
        } catch (error) {
            throw new Error(`Failed to write raw content to ${meta.path}: ${error}`);
        }
    }


    async checkExistence(path: string): Promise<{ fileExists: boolean; foo: string }> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(path);
            const [exists] = await file.exists();

            if (!exists) {
                return { fileExists: false, foo: '' };
            }

            return { fileExists: true, foo: '' };

        } catch (error) {
            return { fileExists: false, foo: '' };
        }
    }

    /**
     * Reads a Firestore document and returns it
     * @param resourceTypeRef The resource type reference
     * @param resourceIdentity The resource identity
     * @returns The document data or null if not found
     */
    async readFromFirestore(
        resourceTypeRef: string,
        resourceIdentity: string
    ): Promise<ResourceMetaJson | null> {
        try {
            const col = dbAdmin.collection('resources').doc(resourceTypeRef).collection('members');
            const docRef = col.doc(resourceIdentity);
            const doc = await docRef.get();

            if (!doc.exists) {
                return null;
            }

            return doc.data() as ResourceMetaJson;
        } catch (error) {
            throw new Error(`Failed to read from Firestore: ${error}`);
        }
    }

    /**
     * Deletes a document from Firestore
     * @param resourceTypeRef The resource type reference
     * @param resourceIdentity The resource identity
     */
    async deleteFromFirestore(
        resourceTypeRef: string,
        resourceIdentity: string
    ): Promise<void> {
        try {
            const col = dbAdmin.collection('resources').doc(resourceTypeRef).collection('members');
            const docRef = col.doc(resourceIdentity);
            await docRef.delete();
        } catch (error) {
            throw new Error(`Failed to delete from Firestore: ${error}`);
        }
    }

    /**
     * Deletes all documents with a given path from Firestore for specified resource types
     * @param path The GCS path to search for
     * @param resourceTypeRefs Array of resource type references to search in
     * @returns Array of deleted document identities
     */
    async deleteByPath(
        path: string,
        resourceTypeRefs: string[]
    ): Promise<string[]> {
        try {
            const deletedIdentities: string[] = [];

            for (const resourceTypeRef of resourceTypeRefs) {
                const col = dbAdmin.collection('resources').doc(resourceTypeRef).collection('members');
                const snapshot = await col.where('path', '==', path).get();

                // Delete all matching documents
                const deletePromises = snapshot.docs.map(async (doc) => {
                    await doc.ref.delete();
                    deletedIdentities.push(doc.id);
                });

                await Promise.all(deletePromises);
            }

            return deletedIdentities;
        } catch (error) {
            throw new Error(`Failed to delete by path from Firestore: ${error}`);
        }
    }

    /**
     * Deletes a file from GCS
     * @param path The path to the file in the GCS bucket
     */
    async deleteFromGCS(path: string): Promise<void> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(path);

            const [exists] = await file.exists();
            if (exists) {
                await file.delete();
            }
        } catch (error) {
            throw new Error(`Failed to delete from GCS: ${error}`);
        }
    }

}
