name: Publish Packages

on:
  push:
    branches:
      - master
    paths:
      - "packages/schema/package.json"
      - "packages/shared/package.json"
      - "packages/validation/package.json"
      - "packages/visualization/package.json"

jobs:

  publish-schema:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Detect if schema changed
        id: detect
        run: |
          git fetch --depth=2 origin master
          if git diff --name-only HEAD HEAD~1 | grep -q "packages/schema/package.json"; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - name: Stop if schema not changed
        if: steps.detect.outputs.changed == 'false'
        run: echo "No schema changes — skipping."

      - name: Remove pnpm store cache
        if: steps.detect.outputs.changed == 'true'
        run: rm -rf ~/.pnpm-store

      - name: Setup pnpm
        if: steps.detect.outputs.changed == 'true'
        uses: pnpm/action-setup@v3
        with:
          version: 9
          run_install: false

      - name: Setup Node.js and npm registry
        if: steps.detect.outputs.changed == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: "https://registry.npmjs.org/"
          cache: "pnpm"

      - name: Install workspace dependencies
        if: steps.detect.outputs.changed == 'true'
        run: pnpm install --no-frozen-lockfile

      - name: Build schema
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/schema run update

      - name: Publish schema
        if: steps.detect.outputs.changed == 'true'
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: npm publish --filter ./packages/schema --access public


  publish-shared:
    needs: publish-schema
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Detect if shared changed
        id: detect
        run: |
          git fetch --depth=2 origin master
          if git diff --name-only HEAD HEAD~1 | grep -q "packages/shared/package.json"; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - name: Stop if shared not changed
        if: steps.detect.outputs.changed == 'false'
        run: echo "No shared changes — skipping."

      - name: Remove pnpm store cache
        if: steps.detect.outputs.changed == 'true'
        run: rm -rf ~/.pnpm-store

      - name: Setup pnpm
        if: steps.detect.outputs.changed == 'true'
        uses: pnpm/action-setup@v3
        with:
          version: 9
          run_install: false

      - name: Setup Node.js and npm registry
        if: steps.detect.outputs.changed == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: "https://registry.npmjs.org/"
          cache: "pnpm"

      - name: Install workspace dependencies
        if: steps.detect.outputs.changed == 'true'
        run: pnpm install --no-frozen-lockfile

      - name: Build schema FIRST
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/schema run update

      - name: Build shared
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/shared build

      - name: Publish shared
        if: steps.detect.outputs.changed == 'true'
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: npm publish --filter ./packages/shared --access public


  publish-validation:
    needs: publish-shared
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Detect if validation changed
        id: detect
        run: |
          git fetch --depth=2 origin master
          if git diff --name-only HEAD HEAD~1 | grep -q "packages/validation/package.json"; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - name: Stop if validation not changed
        if: steps.detect.outputs.changed == 'false'
        run: echo "No validation changes — skipping."

      - name: Remove pnpm store cache
        if: steps.detect.outputs.changed == 'true'
        run: rm -rf ~/.pnpm-store

      - name: Setup pnpm
        if: steps.detect.outputs.changed == 'true'
        uses: pnpm/action-setup@v3
        with:
          version: 9
          run_install: false

      - name: Setup Node.js and npm registry
        if: steps.detect.outputs.changed == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: "https://registry.npmjs.org/"
          cache: "pnpm"

      - name: Install workspace dependencies
        if: steps.detect.outputs.changed == 'true'
        run: pnpm install --no-frozen-lockfile

      - name: Build schema FIRST
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/schema run update

      - name: Build shared NEXT
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/shared build

      - name: Build validation
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/validation build

      - name: Publish validation
        if: steps.detect.outputs.changed == 'true'
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: npm publish --filter ./packages/validation --access public


  publish-visualization:
    needs: publish-validation
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Detect if visualization changed
        id: detect
        run: |
          git fetch --depth=2 origin master
          if git diff --name-only HEAD HEAD~1 | grep -q "packages/visualization/package.json"; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - name: Stop if visualization not changed
        if: steps.detect.outputs.changed == 'false'
        run: echo "No visualization changes — skipping."

      - name: Remove pnpm store cache
        if: steps.detect.outputs.changed == 'true'
        run: rm -rf ~/.pnpm-store

      - name: Setup pnpm
        if: steps.detect.outputs.changed == 'true'
        uses: pnpm/action-setup@v3
        with:
          version: 9
          run_install: false

      - name: Setup Node.js and npm registry
        if: steps.detect.outputs.changed == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: "https://registry.npmjs.org/"
          cache: "pnpm"

      - name: Install workspace dependencies
        if: steps.detect.outputs.changed == 'true'
        run: pnpm install --no-frozen-lockfile

      - name: Build schema FIRST
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/schema run update

      - name: Build shared NEXT
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/shared build

      - name: Build validation NEXT
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/validation build

      - name: Build visualization
        if: steps.detect.outputs.changed == 'true'
        run: pnpm --filter ./packages/visualization build

      - name: Publish visualization
        if: steps.detect.outputs.changed == 'true'
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: npm publish --filter ./packages/visualization --access public
