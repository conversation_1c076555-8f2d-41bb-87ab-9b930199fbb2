# === Node.js ===
node_modules/
npm-debug.log*

# === Next.js ===
.next/
out/              
.vercel/             

# === Env files ===
.env*
!.env.example

# === TypeScript ===
*.tsbuildinfo

# === Logs ===
logs/
*.log

# === OS and editor ===
.DS_Store
Thumbs.db
.vscode/

# === Build tools ===
dist/
build/

# === Misc ===
coverage/
*.local
gcp-key.json

# === Lockfiles ===
package-lock.json

# === LangGraph API ===
.langgraph_api

packages/engine/_lib/
packages/engine/_schemas/

packages/_lib/src/_schemas/

packages/validation/*/generated/
stashed/

toolproof-563fe-556dd7e1ea79.json
